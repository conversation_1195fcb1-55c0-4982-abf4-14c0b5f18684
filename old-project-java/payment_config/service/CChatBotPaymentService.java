package com.redon_agency.chatbot.user.payment_config.service;

import com.redon_agency.chatbot.user.payment_config.dto.response.CChatBotPaymentRes;
import com.redon_agency.chatbot.user.payment_config.dto.CChatBotTypePayment;
import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;

import java.util.List;

public interface CChatBotPaymentService {
    List<CChatBotPaymentRes> listChatBotPayment();

    void settingTypePayment(Integer chatBotId, PaymentChatbotEnum paymentMethod);

    CChatBotTypePayment viewChatBotTypePayment(Integer chatBotId);

    void chatBotOnlineBankingSettingPayment(Integer chatBotId, Integer id);
}
