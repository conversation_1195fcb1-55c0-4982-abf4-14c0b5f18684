package com.redon_agency.chatbot.user.payment_config.service;

import com.redon_agency.chatbot.dto.client.sepay.PaymentSlipResponse;
import com.redon_agency.chatbot.dto.client.sepay.hub.BankAccountDTO;
import com.redon_agency.chatbot.dto.client.sepay.hub.WebhooksSepayHubRequest;
import com.redon_agency.chatbot.dto.response.ApiResponse;
import com.redon_agency.chatbot.dto.response.PageResponse;
import com.redon_agency.chatbot.dto.response.sepay.BankResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CreateBankResponse;
import com.redon_agency.chatbot.dto.response.sepay_hub.BankAccountDetailResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CAccountRes;
import com.redon_agency.chatbot.user.payment_config.dto.request.CCreateBankAccountReq;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface CSepayHubService {
    List<BankResponse> getListAvailableBanks();

    CreateBankResponse getCreateBank(CCreateBankAccountReq request);

    Object confirmApiConnection(String otp, Integer id);

    List<CAccountRes> listOfLinkedAccounts();

    Object createVaAccount(Integer id, String email, String VA);

    Object confirmVAAccount(String otp, Integer id);

    ResponseEntity<?> ipn(WebhooksSepayHubRequest request, HttpServletRequest httpServletRequest);

    PaymentSlipResponse getQRUrl(HttpServletRequest request, Integer orderId);

    ApiResponse<?> deleteBankAccount(Integer id);

    String confirmDeleteBankAccount(Integer id, String otp);

    CreateBankResponse requestApiConnection(Integer id);

    Object requestApiConnectionVA(Integer id, String email);

    PageResponse<BankAccountDTO> getPageBankAccounts(Integer pageNo, Integer pageSize, String searchKey);

    BankAccountDetailResponse getBankAccountDetails(Integer id);

    List<CAccountRes> getEligibleVAAccounts();

    List<CAccountRes> listEligiblePaymentAccountsForChatBot();
}
