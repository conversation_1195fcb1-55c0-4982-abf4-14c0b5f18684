package com.redon_agency.chatbot.user.payment_config.event;

import com.redon_agency.chatbot.common.utils.ConvertTupleSaveUtils;
import com.redon_agency.chatbot.entity.ElectronicPaymentGateway;
import com.redon_agency.chatbot.repository.UserRepository;
import com.redon_agency.chatbot.utils.email.SendWithTemplateUtils;
import jakarta.persistence.Tuple;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CSepayHubEvent {
    private final UserRepository userRepository;
    private final SendWithTemplateUtils sendWithTemplateUtils;

    @Async
    public void eventAfterConfirmApiConnection(Integer userId, ElectronicPaymentGateway paymentGateway) {

        try {
            Tuple tuple = userRepository.getSendTo(userId);
            String to = ConvertTupleSaveUtils.getSafeString(tuple, "email");

            sendWithTemplateUtils.sendIntegrationPaymentGateway(to, userId, paymentGateway.getId());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
