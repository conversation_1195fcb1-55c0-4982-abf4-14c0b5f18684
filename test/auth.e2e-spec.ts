import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '../src/modules/user/entities';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { RedisService } from '@shared/services/redis.service';

describe('AuthController (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let redisService: RedisService;
  
  // Test user data
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    fullName: 'E2E Test User',
    phoneNumber: '**********',
  };
  
  // Hashed password for test user
  let hashedPassword: string;
  
  // Tokens for testing
  let otpToken: string;
  let changePasswordToken: string;
  
  beforeAll(async () => {
    // Create hashed password for test user
    hashedPassword = await bcrypt.hash(testUser.password, 10);
    
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    redisService = moduleFixture.get<RedisService>(RedisService);
    
    // Clean up any existing test user
    await userRepository.delete({ email: testUser.email });
  });

  afterAll(async () => {
    // Clean up test user
    await userRepository.delete({ email: testUser.email });
    await app.close();
  });

  describe('POST /auth/register', () => {
    it('should register a new user and return OTP information', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send({
          fullName: testUser.fullName,
          email: testUser.email,
          password: testUser.password,
          phoneNumber: testUser.phoneNumber,
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toContain('Mã OTP đã được gửi');
      expect(response.body.result).toHaveProperty('otpToken');
      expect(response.body.result).toHaveProperty('expiresIn');
      expect(response.body.result).toHaveProperty('maskedEmail');
      
      // Save OTP token for next test
      otpToken = response.body.result.otpToken;
      
      // In development environment, OTP should be returned for testing
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        expect(response.body.result).toHaveProperty('otp');
      }
    });

    it('should return conflict error for existing email', async () => {
      // Create a user with the same email
      await userRepository.save({
        email: '<EMAIL>',
        password: hashedPassword,
        fullName: 'Existing User',
        phoneNumber: '0987654321',
        role: 'user',
        isActive: true,
        isVerifyEmail: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      const response = await request(app.getHttpServer())
        .post('/auth/register')
        .send({
          fullName: 'Another User',
          email: '<EMAIL>', // Same email
          password: 'password123',
          phoneNumber: '0912345679',
        })
        .expect(409);

      expect(response.body.message).toContain('Email đã được sử dụng');
      
      // Clean up
      await userRepository.delete({ email: '<EMAIL>' });
    });
  });

  describe('POST /auth/verify-otp', () => {
    it('should verify OTP and complete registration', async () => {
      // Get the OTP from Redis (in a real test, you would use the OTP from the response)
      const registrationKey = `registration:${otpToken}`;
      const registrationData = await redisService.get(registrationKey);
      const parsedData = JSON.parse(registrationData);
      const otp = parsedData.otp;

      const response = await request(app.getHttpServer())
        .post('/auth/verify-otp')
        .send({
          otp: otp,
          otpToken: otpToken,
        })
        .expect(200);

      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
      
      // Check if refresh token cookie is set
      expect(response.headers['set-cookie']).toBeDefined();
      expect(response.headers['set-cookie'][0]).toContain('refresh_token=');
      
      // Verify user is now verified in database
      const user = await userRepository.findOne({ where: { email: testUser.email } });
      expect(user).toBeDefined();
      expect(user.isVerifyEmail).toBe(true);
    });

    it('should return error for invalid OTP', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/verify-otp')
        .send({
          otp: '000000', // Wrong OTP
          otpToken: otpToken,
        })
        .expect(400);

      expect(response.body.message).toContain('OTP không chính xác');
    });
  });

  describe('POST /auth/login', () => {
    it('should login successfully and return access token', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Đăng nhập thành công');
      expect(response.body.result).toHaveProperty('accessToken');
      expect(response.body.result).toHaveProperty('user');
      expect(response.body.result.user.email).toBe(testUser.email);
      
      // Check if refresh token cookie is set
      expect(response.headers['set-cookie']).toBeDefined();
      expect(response.headers['set-cookie'][0]).toContain('refresh_token=');
    });

    it('should return unauthorized for wrong password', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'wrong-password',
        })
        .expect(401);

      expect(response.body.message).toContain('Email hoặc mật khẩu không chính xác');
    });

    it('should return unauthorized for non-existent email', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(401);

      expect(response.body.message).toContain('Email hoặc mật khẩu không chính xác');
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should initiate forgot password process', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({
          email: testUser.email,
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toContain('Mã OTP đã được gửi');
      expect(response.body.result).toHaveProperty('otpToken');
      expect(response.body.result).toHaveProperty('expiresIn');
      expect(response.body.result).toHaveProperty('maskedEmail');
      
      // Save OTP token for next test
      otpToken = response.body.result.otpToken;
      
      // In development environment, OTP should be returned for testing
      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        expect(response.body.result).toHaveProperty('otp');
      }
    });

    it('should return not found for non-existent email', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({
          email: '<EMAIL>',
        })
        .expect(404);

      expect(response.body.message).toContain('Email không tồn tại');
    });
  });

  describe('POST /auth/verify-forgot-password', () => {
    it('should verify forgot password OTP and return change password token', async () => {
      // Get the OTP from Redis (in a real test, you would use the OTP from the response)
      const forgotPasswordKey = `forgot_password:${otpToken}`;
      const forgotPasswordData = await redisService.get(forgotPasswordKey);
      const parsedData = JSON.parse(forgotPasswordData);
      const otp = parsedData.otp;

      const response = await request(app.getHttpServer())
        .post('/auth/verify-forgot-password')
        .send({
          otp: otp,
          otpToken: otpToken,
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Xác thực OTP thành công');
      expect(response.body.result).toHaveProperty('changePasswordToken');
      expect(response.body.result).toHaveProperty('expiresIn');
      
      // Save change password token for next test
      changePasswordToken = response.body.result.changePasswordToken;
    });

    it('should return error for invalid OTP', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/verify-forgot-password')
        .send({
          otp: '000000', // Wrong OTP
          otpToken: otpToken,
        })
        .expect(400);

      expect(response.body.message).toContain('OTP không chính xác');
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should reset password with valid token', async () => {
      const newPassword = 'newPassword123';
      
      const response = await request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({
          newPassword: newPassword,
          changePasswordToken: changePasswordToken,
        })
        .expect(200);

      expect(response.body.code).toBe(200);
      expect(response.body.message).toBe('Đổi mật khẩu thành công');
      
      // Verify login with new password works
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: newPassword,
        })
        .expect(200);
      
      expect(loginResponse.body.code).toBe(200);
      expect(loginResponse.body.message).toBe('Đăng nhập thành công');
    });

    it('should return error for invalid token', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({
          newPassword: 'anotherPassword123',
          changePasswordToken: 'invalid-token',
        })
        .expect(400);

      expect(response.body.message).toContain('Token không hợp lệ hoặc đã hết hạn');
    });
  });
});
