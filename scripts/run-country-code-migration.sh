#!/bin/bash

# Script to run country code migration
# This script adds country_code column to users table

echo "Running country code migration..."

# Check if database connection variables are set
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "Error: Database connection variables not set"
    echo "Please set DB_HOST, DB_NAME, DB_USER, and DB_PASSWORD environment variables"
    echo ""
    echo "Example:"
    echo "export DB_HOST=localhost"
    echo "export DB_NAME=redai_db"
    echo "export DB_USER=postgres"
    echo "export DB_PASSWORD=your_password"
    exit 1
fi

# Backup current database schema (optional but recommended)
echo "Creating backup of current schema..."
pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" --schema-only > "backup_schema_$(date +%Y%m%d_%H%M%S).sql"

if [ $? -eq 0 ]; then
    echo "Schema backup created successfully"
else
    echo "Warning: Failed to create schema backup"
fi

# Run the migration
echo "Applying migration..."
psql -h "$DB_HOST" -d "$DB_NAME" -U "$DB_USER" -f src/database/migrations/add-country-code-to-users.sql

if [ $? -eq 0 ]; then
    echo "✅ Migration completed successfully!"
    echo ""
    echo "Changes applied:"
    echo "- Added country_code column to users table"
    echo "- Set default value to '+84'"
    echo "- Updated existing records with default value"
    echo ""
    echo "You can now use the countryCode field in your RegisterDto and User entity."
else
    echo "❌ Migration failed!"
    echo ""
    echo "To rollback, run:"
    echo "psql -h \$DB_HOST -d \$DB_NAME -U \$DB_USER -f src/database/migrations/rollback-country-code-from-users.sql"
    exit 1
fi
