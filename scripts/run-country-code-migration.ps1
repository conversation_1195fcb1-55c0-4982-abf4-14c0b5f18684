# PowerShell script to run country code migration
# This script adds country_code column to users table

Write-Host "Running country code migration..." -ForegroundColor Green

# Check if database connection variables are set
if (-not $env:DB_HOST -or -not $env:DB_NAME -or -not $env:DB_USER) {
    Write-Host "Error: Database connection variables not set" -ForegroundColor Red
    Write-Host "Please set DB_HOST, DB_NAME, DB_USER, and DB_PASSWORD environment variables" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Example:" -ForegroundColor Cyan
    Write-Host "`$env:DB_HOST='localhost'" -ForegroundColor Cyan
    Write-Host "`$env:DB_NAME='redai_db'" -ForegroundColor Cyan
    Write-Host "`$env:DB_USER='postgres'" -ForegroundColor Cyan
    Write-Host "`$env:DB_PASSWORD='your_password'" -ForegroundColor Cyan
    exit 1
}

# Set PGPASSWORD environment variable for psql
$env:PGPASSWORD = $env:DB_PASSWORD

# Backup current database schema (optional but recommended)
Write-Host "Creating backup of current schema..." -ForegroundColor Yellow
$backupFile = "backup_schema_$(Get-Date -Format 'yyyyMMdd_HHmmss').sql"

try {
    & pg_dump -h $env:DB_HOST -U $env:DB_USER -d $env:DB_NAME --schema-only > $backupFile
    Write-Host "Schema backup created successfully: $backupFile" -ForegroundColor Green
} catch {
    Write-Host "Warning: Failed to create schema backup" -ForegroundColor Yellow
}

# Run the migration
Write-Host "Applying migration..." -ForegroundColor Yellow

try {
    & psql -h $env:DB_HOST -d $env:DB_NAME -U $env:DB_USER -f "src/database/migrations/add-country-code-to-users.sql"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Changes applied:" -ForegroundColor Cyan
        Write-Host "- Added country_code column to users table" -ForegroundColor White
        Write-Host "- Set default value to '+84'" -ForegroundColor White
        Write-Host "- Updated existing records with default value" -ForegroundColor White
        Write-Host ""
        Write-Host "You can now use the countryCode field in your RegisterDto and User entity." -ForegroundColor Green
    } else {
        throw "Migration command failed"
    }
} catch {
    Write-Host "❌ Migration failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "To rollback, run:" -ForegroundColor Yellow
    Write-Host "psql -h `$env:DB_HOST -d `$env:DB_NAME -U `$env:DB_USER -f src/database/migrations/rollback-country-code-from-users.sql" -ForegroundColor Cyan
    exit 1
}
