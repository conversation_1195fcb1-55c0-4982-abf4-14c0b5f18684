#!/bin/bash

# <PERSON><PERSON>t to run email server configuration migration
# This script adds is_active and use_start_tls columns to email_server_configurations table

echo "Running email server configuration migration..."

# Check if database connection variables are set
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "Error: Database connection variables not set"
    echo "Please set DB_HOST, DB_NAME, DB_USER, and DB_PASSWORD environment variables"
    exit 1
fi

# Run the migration
psql -h "$DB_HOST" -d "$DB_NAME" -U "$DB_USER" -f src/database/migrations/add-email-server-configuration-fields.sql

if [ $? -eq 0 ]; then
    echo "Migration completed successfully!"
else
    echo "Migration failed!"
    exit 1
fi
