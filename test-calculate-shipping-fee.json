{"test_case_1_use_customer_address": {"description": "Sử dụng địa chỉ của customer (không truyền deliveryAddress)", "request": {"shopId": 1, "customerId": 18, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "<PERSON><PERSON> thống sẽ lấy địa chỉ từ customer ID 18 để tính phí vận chuyển"}, "test_case_2_use_specific_address": {"description": "Sử dụng địa chỉ giao hàng cụ thể", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": "123 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON>", "preferredCarrier": "GHN"}, "expected_behavior": "<PERSON><PERSON> thống sẽ sử dụng địa chỉ được truyền vào để tính phí"}, "test_case_3_both_provided": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> cả customerId và deliveryAddress", "request": {"shopId": 1, "customerId": 18, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": "456 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>", "preferredCarrier": "GHN"}, "expected_behavior": "Ưu tiên sử dụng deliveryAddress thay vì địa chỉ customer"}, "test_case_4_neither_provided": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> cả customerId và deliveryAddress", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "Trả về lỗi: <PERSON><PERSON><PERSON> cung cấp địa chỉ giao hàng hoặc ID khách hàng"}, "test_case_5_invalid_customer": {"description": "CustomerId không tồn tại", "request": {"shopId": 1, "customerId": 999, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "Trả về lỗi: <PERSON><PERSON><PERSON><PERSON> hàng không tồn tại hoặc không thuộc về bạn"}}