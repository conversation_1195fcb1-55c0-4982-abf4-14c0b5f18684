{"test_case_1_use_customer_address": {"description": "Sử dụng địa chỉ của customer (không truyền deliveryAddress)", "request": {"shopId": 1, "customerId": 18, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "<PERSON><PERSON> thống sẽ lấy địa chỉ từ customer ID 18 để tính phí vận chuyển"}, "test_case_2_use_existing_address": {"description": "Sử dụng địa chỉ đã lưu trong hệ thống", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": {"addressId": 1}, "preferredCarrier": "GHN"}, "expected_behavior": "<PERSON><PERSON> thống sẽ lấy địa chỉ từ user_addresses với ID = 1"}, "test_case_3_create_new_address": {"description": "Tạo địa chỉ mới để tính phí", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": {"newAddress": {"recipientName": "<PERSON><PERSON><PERSON><PERSON>", "recipientPhone": "0912345678", "address": "123 Đường ABC, Phường 1", "province": "T<PERSON><PERSON> <PERSON><PERSON>", "district": "Quận 1", "ward": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "70000", "isDefault": false, "addressType": "home", "note": "Gần chợ Bến <PERSON>nh"}}, "preferredCarrier": "GHN"}, "expected_behavior": "<PERSON><PERSON> thống sẽ tạo địa chỉ mới và sử dụng để tính phí"}, "test_case_4_both_provided": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> cả customerId và deliveryAddress", "request": {"shopId": 1, "customerId": 18, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": {"addressId": 2}, "preferredCarrier": "GHN"}, "expected_behavior": "Ưu tiên sử dụng deliveryAddress.addressId thay vì địa chỉ customer"}, "test_case_5_neither_provided": {"description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> cả customerId và deliveryAddress", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "Trả về lỗi: <PERSON><PERSON><PERSON> cung cấp địa chỉ giao hàng hoặc ID khách hàng"}, "test_case_6_invalid_customer": {"description": "CustomerId không tồn tại", "request": {"shopId": 1, "customerId": 999, "products": [{"productId": 60, "quantity": 2}], "preferredCarrier": "GHN"}, "expected_behavior": "Trả về lỗi: <PERSON><PERSON><PERSON><PERSON> hàng không tồn tại hoặc không thuộc về bạn"}, "test_case_7_invalid_address_id": {"description": "AddressId không tồn tại", "request": {"shopId": 1, "products": [{"productId": 60, "quantity": 2}], "deliveryAddress": {"addressId": 999}, "preferredCarrier": "GHN"}, "expected_behavior": "Trả về lỗi: <PERSON><PERSON><PERSON> chỉ không tồn tại hoặc không thuộc về bạn"}}