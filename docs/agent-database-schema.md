# Agent Module Database Schema Documentation

## Tổng quan

Agent <PERSON><PERSON><PERSON> bao gồm 18 entities ch<PERSON><PERSON>, đ<PERSON><PERSON><PERSON> thiết kế để quản lý hệ thống AI agents với các tính năng:
- Quản lý agent cơ bản và nâng cao
- Hệ thống phân quyền và vai trò
- Cấu hình linh hoạt với JSONB
- Soft delete và audit trail
- Multi-agent collaboration

## Core Entities

### 1. agents (Agent)
**Mục đích**: Bảng chính lưu thông tin cơ bản của tất cả agents

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Primary key | PK, Auto-generated |
| name | VARCHAR(255) | Tên hiển thị | NOT NULL |
| avatar | VARCHAR(255) | S3 key của avatar | NULLABLE |
| model_config | JSONB | Cấu hình AI model | Default: {} |
| instruction | TEXT | System prompt | NULLABLE |
| vector_store_id | VARCHAR(100) | ID kho vector | NULLABLE |
| created_at | BIGINT | Timestamp tạo (millis) | Auto-generated |
| updated_at | BIGINT | Timestamp cập nhật (millis) | Auto-update |
| deleted_at | BIGINT | Soft delete timestamp | NULLABLE |
| is_for_sale | BOOLEAN | Trạng thái bán | Default: false |
| status | VARCHAR(50) | Trạng thái agent | Default: DRAFT |

**JSONB Fields**:
- `model_config`: `{"temperature": 0.7, "max_tokens": 1000, "top_p": 1.0}`

### 2. agents_base (AgentBase)
**Mục đích**: Agents cơ sở của hệ thống, chỉ 1 agent active tại 1 thời điểm

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Tham chiếu agents.id | PK, FK |
| created_by | INTEGER | ID nhân viên tạo | NULLABLE |
| updated_by | INTEGER | ID nhân viên cập nhật | NULLABLE |
| deleted_by | INTEGER | ID nhân viên xóa | NULLABLE |
| active | BOOLEAN | Trạng thái hoạt động | Default: false, UNIQUE when true |
| model_registry_id | UUID | ID model registry | NULLABLE |
| model_name | VARCHAR | Tên model hiển thị | NULLABLE |
| key_llm_id | UUID | ID key LLM | NULLABLE |

**Constraints**:
- UNIQUE constraint: Chỉ 1 agent có `active = true`

### 3. agents_user (AgentUser)
**Mục đích**: Agents của người dùng với cấu hình chi tiết

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Tham chiếu agents.id | PK, FK |
| user_id | INTEGER | ID người dùng sở hữu | NULLABLE |
| type_id | INTEGER | ID loại agent | FK to type_agents |
| source_id | UUID | Agent gốc (nếu copy) | NULLABLE |
| profile | JSONB | Thông tin hồ sơ | Default: {} |
| convert_config | JSONB | Cấu hình chuyển đổi | Default: {} |
| active | BOOLEAN | Trạng thái hoạt động | Default: false |
| exp | BIGINT | Điểm kinh nghiệm | Default: 0 |
| model_registry_id | UUID | ID model registry | NULLABLE |
| model_base_id | UUID | ID model base | NULLABLE |
| model_name | VARCHAR | Tên model | NULLABLE |
| key_llm_id | UUID | ID key LLM | NULLABLE |
| strategy_id | UUID | ID strategy | NULLABLE |

**JSONB Fields**:
- `profile`: `{"bio": "Assistant for tasks", "personality": "helpful"}`
- `convert_config`: Array of conversion configurations

### 4. type_agents (TypeAgent)
**Mục đích**: Định nghĩa các loại agent với cấu hình mặc định

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | INTEGER | Primary key | PK, Auto-increment |
| name | VARCHAR(255) | Tên loại agent | NOT NULL |
| description | TEXT | Mô tả chi tiết | NULLABLE |
| config | JSONB | Cấu hình mặc định | Default: {} |
| created_by | INTEGER | ID nhân viên tạo | NULLABLE |
| updated_by | INTEGER | ID nhân viên cập nhật | NULLABLE |
| created_at | BIGINT | Timestamp tạo | Auto-generated |
| updated_at | BIGINT | Timestamp cập nhật | Auto-update |
| deleted_at | BIGINT | Soft delete timestamp | NULLABLE |
| deleted_by | BIGINT | ID nhân viên xóa | NULLABLE |
| status | ENUM | Trạng thái (DRAFT/APPROVED) | Default: DRAFT |

**JSONB Fields**:
- `config`: TypeAgentConfig interface với các tính năng enable/disable

### 5. agent_roles (AgentRole)
**Mục đích**: Vai trò và cấu hình MCP cho agents

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Primary key | PK, Auto-generated |
| name | VARCHAR(50) | Tên vai trò | NOT NULL |
| description | TEXT | Mô tả vai trò | NULLABLE |
| created_by | INTEGER | ID nhân viên tạo | NULLABLE |
| updated_by | INTEGER | ID nhân viên cập nhật | NULLABLE |
| created_at | BIGINT | Timestamp tạo | Auto-generated |
| updated_at | BIGINT | Timestamp cập nhật | Auto-update |
| deleted_at | BIGINT | Soft delete timestamp | NULLABLE |
| deleted_by | INTEGER | ID nhân viên xóa | NULLABLE |
| module_mcp_config | JSONB | Cấu hình MCP | NOT NULL |

**JSONB Fields**:
- `module_mcp_config`: MCP configuration với server, port, headers

## Strategy & Collaboration Entities

### 6. agents_strategy (AgentStrategy)
**Mục đích**: Chiến lược xử lý của agents

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Liên kết 1-1 với agents | PK, FK |
| content | JSONB | Nội dung chiến lược | Default: [] |
| example_default | JSONB | Ví dụ mặc định | Default: [] |

### 7. agents_strategy_user (AgentStrategyUser)
**Mục đích**: Strategy agents của người dùng với customization

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Primary key | PK, Auto-generated |
| agents_strategy_id | UUID | Tham chiếu agents_strategy | FK, NULLABLE |
| example | JSONB | Ví dụ tùy chỉnh | Default: [] |
| user_id | INTEGER | ID người dùng sở hữu | NULLABLE |
| owned_at | BIGINT | Timestamp sở hữu | Auto-generated |

### 8. user_multi_agents (UserMultiAgent)
**Mục đích**: Quan hệ đa cấp giữa các agents

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| parent_agent_id | UUID | Agent cấp trên | PK, FK to agents_user |
| child_agent_id | UUID | Agent cấp dưới | PK, FK to agents_user |

**Constraints**:
- CHECK: `parent_agent_id <> child_agent_id`
- Composite Primary Key

## System & Template Entities

### 9. agents_system (AgentSystem)
**Mục đích**: Agents hệ thống với vai trò cố định

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Tham chiếu agents.id | PK, FK |
| role_id | UUID | ID vai trò | PK, FK |
| name_code | VARCHAR(100) | Mã định danh | UNIQUE, NOT NULL |

**Constraints**:
- Composite Primary Key: (id, role_id)
- UNIQUE: name_code

### 10. agents_template (AgentTemplate)
**Mục đích**: Templates cho việc tạo agents

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | UUID | Tham chiếu agents.id | PK, FK |
| type_id | INTEGER | ID loại agent | FK to type_agents |
| profile | JSONB | Thông tin hồ sơ mẫu | Default: {} |
| convert_config | JSONB | Cấu hình chuyển đổi mẫu | Default: {} |
| status | ENUM | Trạng thái template | Default: DRAFT |
| model_base_id | UUID | ID model base | NULLABLE |
| is_for_sale | BOOLEAN | Có thể bán | Default: false |
| strategy_id | UUID | ID strategy | NULLABLE |

## Resource & Media Entities

### 11. agents_rank (AgentRank)
**Mục đích**: Cấp bậc agents dựa trên kinh nghiệm

| Field | Type | Description | Constraints |
|-------|------|-------------|-------------|
| id | INTEGER | Primary key | PK, Auto-increment |
| name | VARCHAR(50) | Tên cấp bậc | NOT NULL |
| description | TEXT | Mô tả cấp bậc | NULLABLE |
| badge | VARCHAR(255) | S3 key huy hiệu | NOT NULL |
| min_exp | BIGINT | Kinh nghiệm tối thiểu | NOT NULL |
| max_exp | BIGINT | Kinh nghiệm tối đa | NULLABLE |

### 12-18. Resource Entities
- `agents_media`: Quản lý media resources
- `agents_product`: Quản lý product resources
- `agents_url`: Quản lý URL resources
- `admin_type_agent_tools`: Tools cho admin type agents
- `user_type_agent_tools`: Tools cho user type agents
- `agent_user_tools`: Tools cho user agents

## Key Design Patterns

### UUID Primary Keys
- **✅ Entities với UUID**: agent, agents-base, agents-user, agents-system, agents-strategy, agents-strategy-user, user-multi-agent, agents-template
- **❌ Entities với INTEGER**: type-agent, agent-rank (sử dụng auto-increment)
- Đảm bảo uniqueness across distributed systems

### Soft Delete Pattern Analysis
**✅ Entities có đầy đủ soft delete**:
- `agent`: có deleted_at
- `agent-role`: có deleted_at, deleted_by
- `type-agent`: có deleted_at, deleted_by
- `agents-strategy`: có deleted_by (thiếu deleted_at)

**❌ Entities thiếu soft delete**:
- `agents-base`: có deleted_by nhưng THIẾU deleted_at
- `agents-user`: THIẾU hoàn toàn deleted_at, deleted_by
- `agents-template`: THIẾU hoàn toàn deleted_at, deleted_by
- `agents-strategy-user`: THIẾU hoàn toàn deleted_at, deleted_by
- `user-multi-agent`: THIẾU hoàn toàn deleted_at, deleted_by
- `agents-system`: THIẾU hoàn toàn deleted_at, deleted_by

### JSONB Configuration
- Flexible configuration storage
- Optimized for PostgreSQL querying
- Schema evolution support

### Audit Trail
- `created_at`, `updated_at`: Timestamp tracking
- `created_by`, `updated_by`: User tracking
- Full audit capability

### Relationship Patterns
- 1-1: agents ↔ agents_base, agents_strategy
- 1-N: type_agents → agents_user
- M-N: agents ↔ tools (through junction tables)
- Hierarchical: user_multi_agents (parent-child)

## Constraints & Indexes

### Unique Constraints
- `agents_base.active = true` (only one active)
- `agents_system.name_code`
- `user.email`, `user.phone_number`

### Foreign Key Relationships
- All agent tables reference `agents.id`
- User references to `users.id`
- Model references to model module tables
- Tool references to tools module tables

### Performance Considerations
- JSONB fields indexed for common queries
- Composite indexes on frequently queried combinations
- Soft delete filtering in all queries

## Issues & Recommendations

### Critical Issues Found

#### 1. Inconsistent Primary Key Types
**Problem**: Một số entities sử dụng INTEGER thay vì UUID
- `type_agents.id`: INTEGER (should be UUID)
- `agents_rank.id`: INTEGER (should be UUID)

**Impact**:
- Không consistent với design pattern
- Có thể gây vấn đề trong distributed systems
- Foreign key references không đồng nhất

**Recommendation**:
- Migrate type_agents.id và agents_rank.id sang UUID
- Update tất cả foreign key references

#### 2. Missing Soft Delete Fields
**Problem**: Nhiều entities thiếu soft delete fields
- `agents-base`: thiếu deleted_at
- `agents-user`: thiếu deleted_at, deleted_by
- `agents-template`: thiếu deleted_at, deleted_by
- `agents-strategy-user`: thiếu deleted_at, deleted_by
- `user-multi-agent`: thiếu deleted_at, deleted_by
- `agents-system`: thiếu deleted_at, deleted_by

**Impact**:
- Không thể soft delete và restore
- Data loss risk
- Inconsistent với business requirements

**Recommendation**:
- Thêm deleted_at (BIGINT, nullable) cho tất cả entities
- Thêm deleted_by (INTEGER, nullable) cho tất cả entities
- Update repositories để support soft delete

#### 3. Missing Audit Trail Fields
**Problem**: Một số entities thiếu audit fields
- `agents-base`: thiếu created_at, updated_at
- `agents-strategy`: thiếu created_at, updated_at
- `user-multi-agent`: thiếu created_at, updated_at
- `agents-strategy-user`: chỉ có owned_at

**Impact**:
- Không track được thời điểm tạo/cập nhật
- Khó debug và audit
- Không consistent với audit requirements

**Recommendation**:
- Thêm created_at, updated_at cho tất cả entities
- Implement @BeforeUpdate() hooks
- Standardize timestamp format (epoch milliseconds)

### Minor Issues

#### 4. JSONB Type Safety
**Problem**: Một số JSONB fields sử dụng `any[]` thay vì proper interfaces
- `agents-strategy.content`: any[]
- `agents-strategy.exampleDefault`: any[]
- `agents-strategy-user.example`: any[]

**Recommendation**:
- Tạo proper TypeScript interfaces
- Replace any[] với typed interfaces
- Improve type safety

#### 5. Nullable Field Consistency
**Problem**: Một số fields có nullable inconsistent
- `agents-base.modelRegistryId`: không nullable nhưng có thể cần nullable
- `agents-strategy.modelRegistryId`: không nullable

**Recommendation**:
- Review business logic để xác định nullable requirements
- Update field constraints cho consistent

### Migration Plan

#### Phase 1: Critical Fixes
1. Add missing soft delete fields
2. Add missing audit trail fields
3. Update entity definitions

#### Phase 2: Type Improvements
1. Migrate INTEGER IDs to UUID (if feasible)
2. Improve JSONB type safety
3. Review nullable constraints

#### Phase 3: Performance & Optimization
1. Add proper indexes
2. Optimize JSONB queries
3. Performance testing
