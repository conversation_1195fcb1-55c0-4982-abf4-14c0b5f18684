-- Rollback Migration: Remove country_code field from users table
-- Date: 2024-12-19
-- Description: Xóa trường country_code khỏi bảng users

-- <PERSON><PERSON><PERSON> tra xem cột có tồn tại không trước khi xóa
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'country_code'
    ) THEN
        -- Xóa cột country_code
        ALTER TABLE users DROP COLUMN country_code;
        RAISE NOTICE 'Đã xóa cột country_code khỏi bảng users';
    ELSE
        RAISE NOTICE 'Cột country_code không tồn tại trong bảng users';
    END IF;
END $$;

-- <PERSON><PERSON><PERSON> tra kết quả
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'country_code';
