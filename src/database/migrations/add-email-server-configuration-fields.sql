-- Migration: Add is_active and use_start_tls fields to email_server_configurations table
-- Date: 2024-12-19
-- Description: Add missing fields to support frontend requirements

-- Add is_active column
ALTER TABLE email_server_configurations 
ADD COLUMN is_active BOOLEAN DEFAULT true;

-- Add use_start_tls column  
ALTER TABLE email_server_configurations 
ADD COLUMN use_start_tls BOOLEAN DEFAULT false;

-- Add comments for the new columns
COMMENT ON COLUMN email_server_configurations.is_active IS 'Trạng thái hoạt động của cấu hình email server';
COMMENT ON COLUMN email_server_configurations.use_start_tls IS 'Xác định có sử dụng STARTTLS hay không';

-- Update existing records to have default values
UPDATE email_server_configurations 
SET is_active = true, use_start_tls = false 
WHERE is_active IS NULL OR use_start_tls IS NULL;