import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateUserProviderShipmentsTable1734000000000 implements MigrationInterface {
  name = 'CreateUserProviderShipmentsTable1734000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Tạo enum cho provider_shipment_type
    await queryRunner.query(`
      CREATE TYPE "provider_shipment_type" AS ENUM ('GHN', 'GHTK', 'AHAMOVE', 'JT')
    `);

    // Tạo bảng user_provider_shipments
    await queryRunner.createTable(
      new Table({
        name: 'user_provider_shipments',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'user_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'key',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['GHN', 'GHTK', 'AHAMOVE', 'JT'],
            enumName: 'provider_shipment_type',
            isNullable: false,
          },
          {
            name: 'created_at',
            type: 'bigint',
            default: '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
          },
        ],
        foreignKeys: [
          {
            columnNames: ['user_id'],
            referencedTableName: 'users',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
          },
        ],
        indices: [
          {
            name: 'idx_user_provider_shipments_user_id',
            columnNames: ['user_id'],
          },
          {
            name: 'idx_user_provider_shipments_type',
            columnNames: ['type'],
          },
          {
            name: 'idx_user_provider_shipments_user_type',
            columnNames: ['user_id', 'type'],
          },
          {
            name: 'idx_user_provider_shipments_created_at',
            columnNames: ['created_at'],
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa bảng user_provider_shipments
    await queryRunner.dropTable('user_provider_shipments');

    // Xóa enum provider_shipment_type
    await queryRunner.query(`DROP TYPE "provider_shipment_type"`);
  }
}
