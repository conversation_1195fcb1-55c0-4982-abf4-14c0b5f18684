import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột original_version_id vào bảng user_tool_versions
 * Cột này sẽ trỏ tới admin_tool_versions.id để biết phiên bản gốc từ admin
 */
export class AddOriginalVersionIdToUserToolVersions1720000000001 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột original_version_id
    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD COLUMN IF NOT EXISTS "original_version_id" UUID NULL
    `);

    // Thêm comment cho cột
    await queryRunner.query(`
      COMMENT ON COLUMN "user_tool_versions"."original_version_id" 
      IS 'ID của phiên bản gốc từ admin tool version, tham chiếu đến admin_tool_versions.id'
    `);

    // Cập nhật unique constraint để bao gồm original_version_id
    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP CONSTRAINT IF EXISTS "unique_user_function_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD CONSTRAINT "unique_user_function_version" 
      UNIQUE ("user_id", "original_function_id", "original_version_id")
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Khôi phục unique constraint cũ
    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP CONSTRAINT IF EXISTS "unique_user_function_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      ADD CONSTRAINT "unique_user_function_version" 
      UNIQUE ("user_id", "original_function_id", "version_number")
    `);

    // Xóa cột original_version_id
    await queryRunner.query(`
      ALTER TABLE "user_tool_versions" 
      DROP COLUMN IF EXISTS "original_version_id"
    `);
  }
}
