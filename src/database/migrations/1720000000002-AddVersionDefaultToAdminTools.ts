import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm cột version_default và is_default vào bảng admin_tools và admin_tool_versions
 * Cột version_default sẽ trỏ tới admin_tool_versions.id để biết phiên bản mặc định
 * Cột is_default sẽ đánh dấu phiên bản nào là mặc định trong admin_tool_versions
 */
export class AddVersionDefaultToAdminTools1720000000002 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột version_default vào bảng admin_tools
    await queryRunner.query(`
      ALTER TABLE "admin_tools"
      ADD COLUMN IF NOT EXISTS "version_default" UUID NULL
    `);

    // Thêm comment cho cột version_default
    await queryRunner.query(`
      COMMENT ON COLUMN "admin_tools"."version_default"
      IS 'ID của phiên bản mặc định, tham chiếu đến admin_tool_versions.id'
    `);

    // Thêm cột is_default vào bảng admin_tool_versions
    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD COLUMN IF NOT EXISTS "is_default" BOOLEAN NOT NULL DEFAULT FALSE
    `);

    // Thêm comment cho cột is_default
    await queryRunner.query(`
      COMMENT ON COLUMN "admin_tool_versions"."is_default"
      IS 'Đánh dấu phiên bản này có phải là phiên bản mặc định không'
    `);

    // Cập nhật unique constraint để sử dụng version_name thay vì version_number
    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP CONSTRAINT IF EXISTS "unique_tool_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD CONSTRAINT "unique_tool_version"
      UNIQUE ("tool_id", "version_name")
    `);

    // Thêm foreign key constraint cho version_default
    await queryRunner.query(`
      ALTER TABLE "admin_tools"
      ADD CONSTRAINT "fk_admin_tools_version_default"
      FOREIGN KEY ("version_default") REFERENCES "admin_tool_versions"("id")
      ON DELETE SET NULL
    `);

    // Cập nhật dữ liệu hiện có: đặt phiên bản mới nhất của mỗi tool làm mặc định
    await queryRunner.query(`
      WITH latest_versions AS (
        SELECT DISTINCT ON (tool_id)
          id, tool_id
        FROM admin_tool_versions
        ORDER BY tool_id, created_at DESC
      )
      UPDATE admin_tool_versions
      SET is_default = TRUE
      WHERE id IN (SELECT id FROM latest_versions)
    `);

    // Cập nhật version_default cho admin_tools
    await queryRunner.query(`
      UPDATE admin_tools
      SET version_default = (
        SELECT id
        FROM admin_tool_versions
        WHERE tool_id = admin_tools.id
        AND is_default = TRUE
        LIMIT 1
      )
      WHERE deleted_at IS NULL
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "admin_tools"
      DROP CONSTRAINT IF EXISTS "fk_admin_tools_version_default"
    `);

    // Khôi phục unique constraint cũ
    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP CONSTRAINT IF EXISTS "unique_tool_version"
    `);

    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      ADD CONSTRAINT "unique_tool_version"
      UNIQUE ("tool_id", "version_number")
    `);

    // Xóa cột version_default
    await queryRunner.query(`
      ALTER TABLE "admin_tools"
      DROP COLUMN IF EXISTS "version_default"
    `);

    // Xóa cột is_default
    await queryRunner.query(`
      ALTER TABLE "admin_tool_versions"
      DROP COLUMN IF EXISTS "is_default"
    `);
  }
}
