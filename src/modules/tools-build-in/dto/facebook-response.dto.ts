import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin trang Facebook
 */
export class FacebookPageResponseDto {
  @ApiProperty({
    description: 'ID của trang Facebook',
    example: '123456789012345'
  })
  pageId: string;

  @ApiProperty({
    description: 'Tên trang Facebook',
    example: 'Trang Kinh Doanh ABC'
  })
  pageName: string;

  @ApiProperty({
    description: 'URL avatar của trang Facebook',
    example: 'https://platform-lookaside.fbsbx.com/platform/profilepic/?psid=123456789012345',
    nullable: true
  })
  avatarUrl?: string;

  @ApiProperty({
    description: 'Số lượng người theo dõi',
    example: 1500,
    nullable: true
  })
  followers?: number;

  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất',
    example: '2023-01-01T00:00:00.000Z'
  })
  lastUpdated: string;
}

/**
 * DTO cho việc trả về thông tin tin nhắn Facebook
 */
export class FacebookMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 'm_abcdefghijklmnop'
  })
  messageId: string;

  @ApiProperty({
    description: 'ID của người gửi',
    example: '123456789012345'
  })
  senderId: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào, tôi cần hỗ trợ về sản phẩm của bạn'
  })
  content: string;

  @ApiProperty({
    description: 'Thời gian gửi tin nhắn',
    example: '2023-01-01T00:00:00.000Z'
  })
  timestamp: string;

  @ApiProperty({
    description: 'Các tệp đính kèm (nếu có)',
    example: [{ type: 'image', url: 'https://example.com/image.jpg' }],
    nullable: true
  })
  attachments?: any[];
}

/**
 * DTO cho việc trả về thông tin phân tích Facebook
 */
export class FacebookAnalyticsResponseDto {
  @ApiProperty({
    description: 'Tổng số lượt tiếp cận',
    example: 5000
  })
  reach: number;

  @ApiProperty({
    description: 'Tổng số lượt tương tác',
    example: 1200
  })
  engagement: number;

  @ApiProperty({
    description: 'Tổng số tin nhắn',
    example: 350
  })
  messages: number;

  @ApiProperty({
    description: 'Thời gian phản hồi trung bình (phút)',
    example: 15.5
  })
  averageResponseTime: number;
}
