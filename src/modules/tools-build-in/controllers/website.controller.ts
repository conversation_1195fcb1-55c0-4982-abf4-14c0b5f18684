import { <PERSON>, Logger } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { WebsiteService } from '../services';

/**
 * Controller x<PERSON> lý các endpoint liên quan đến website
 */
@ApiTags('Website')
@Controller('tools-build-in/website')
@ApiBearerAuth('JWT-auth')
export class WebsiteController {
  private readonly logger = new Logger(WebsiteController.name);

  constructor(private readonly websiteService: WebsiteService) { }
}
