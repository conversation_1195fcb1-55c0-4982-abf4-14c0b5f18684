# Hướng dẫn sử dụng API Key Authentication V2

Module này cung cấp một cơ chế xác thực API Key nâng cao cho các endpoint trong module `tools-build-in`. Tài liệu này mô tả cách sử dụng API Key Authentication V2.

## Cấu trúc API Key

API Key có cấu trúc như sau:
```
API_PREFIX_KEY + "_" + [Agent ID và UserID đã mã hóa với API_SECRET_KEY]
```

Ví dụ:
```
redai_MTIzNDU2Nzg5MA==:YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=
```

## Cấu hình API Key

API Key được cấu hình thông qua biến môi trường:

```
API_PREFIX_KEY=redai
API_SERECT_KEY=80fea_bd9a_17aa_add7_b7b4_2f59_c972_ede0
```

## Quy trình xác thực

1. **Giải mã API Key**: <PERSON>ệ thống sẽ giải mã API Key để lấy Agent ID và User ID.
2. **Kiểm tra Agent**: Hệ thống sẽ kiểm tra xem Agent có tồn tại không và có hoạt động không.
3. **Xác thực**: Nếu Agent tồn tại và đang hoạt động, hệ thống sẽ cho phép truy cập.

## Sử dụng API Key Authentication

### 1. Bảo vệ Controller hoặc Route

Để bảo vệ một controller hoặc route cụ thể, sử dụng `ApiKeyAuthGuard` và `@ApiKeyAuth()` decorator:

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiKeyAuthGuard } from '@modules/tools-build-in/guards';
import { ApiKeyAuth } from '@modules/tools-build-in/decorators';

@Controller('api/v1/tools')
@UseGuards(ApiKeyAuthGuard)  // Áp dụng cho toàn bộ controller
@ApiKeyAuth()                // Đánh dấu toàn bộ controller yêu cầu API Key
export class ToolsController {
  @Get()
  getAllTools() {
    // Endpoint này yêu cầu API Key
    return { tools: [] };
  }
}
```

### 2. Truy cập thông tin Agent và User

Trong controller, bạn có thể truy cập thông tin Agent và User từ request:

```typescript
@Get()
getAllTools(@Req() request: Request) {
  const agent = request['agent'];
  const agentId = agent.id;
  const userId = agent.userId;
  
  // Sử dụng agentId và userId
  return { tools: [] };
}
```

### 3. Gửi API Key trong Request

Khi gọi API, bạn cần gửi API Key trong header `x-api-key`:

```
GET /api/v1/tools HTTP/1.1
Host: example.com
x-api-key: redai_MTIzNDU2Nzg5MA==:YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=
```

Ví dụ với cURL:

```bash
curl -X GET "https://example.com/api/v1/tools" -H "x-api-key: redai_MTIzNDU2Nzg5MA==:YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo="
```

### 4. Tạo API Key (Chỉ dùng trong môi trường phát triển)

Để tạo API Key cho mục đích kiểm thử, bạn có thể sử dụng endpoint sau:

```
GET /api-key-generator?agentId=550e8400-e29b-41d4-a716-446655440000&userId=1
```

Endpoint này sẽ trả về API Key đã tạo:

```json
{
  "code": 0,
  "message": "Tạo API Key thành công",
  "data": {
    "apiKey": "redai_MTIzNDU2Nzg5MA==:YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo="
  }
}
```

### 5. Giải mã API Key (Chỉ dùng trong môi trường phát triển)

Để giải mã API Key, bạn có thể sử dụng endpoint sau:

```
GET /api-key-generator/decode?apiKey=redai_MTIzNDU2Nzg5MA==:YWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo=
```

Endpoint này sẽ trả về thông tin đã giải mã:

```json
{
  "code": 0,
  "message": "Giải mã API Key thành công",
  "data": {
    "decoded": {
      "agentId": "550e8400-e29b-41d4-a716-446655440000",
      "userId": 1
    }
  }
}
```

## Xử lý lỗi

Nếu API Key không hợp lệ hoặc Agent không tồn tại, API sẽ trả về lỗi:

- **API Key không được cung cấp**: Status 401, Code 30201
- **API Key không hợp lệ**: Status 401, Code 30202
- **Agent không tồn tại**: Status 404, Code 30205
- **Agent không hoạt động**: Status 403, Code 30206

## Ví dụ

### Lấy danh sách tool của agent

```typescript
import { Controller, Get, Req, UseGuards } from '@nestjs/common';
import { ApiKeyAuthGuard } from '@modules/tools-build-in/guards';
import { ApiKeyAuth } from '@modules/tools-build-in/decorators';
import { Request } from 'express';

@Controller('agent-tools')
@UseGuards(ApiKeyAuthGuard)
@ApiKeyAuth()
export class AgentToolsController {
  constructor(private readonly agentToolsService: AgentToolsService) {}

  @Get()
  async getAgentTools(@Req() request: Request) {
    const agent = request['agent'];
    const result = await this.agentToolsService.getAgentTools(agent.id);
    return ApiResponseDto.success(result, 'Lấy danh sách tool của agent thành công');
  }
}
```

Trong ví dụ này, API Key sẽ được sử dụng để xác thực và lấy thông tin Agent ID, sau đó sử dụng Agent ID để lấy danh sách tool của agent.
