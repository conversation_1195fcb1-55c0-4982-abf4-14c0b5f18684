import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { CartItem } from '../entities/cart-item.entity';
import { Product } from '../entities/product.entity';
import { ProductStatus } from '../enums';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';

/**
 * Kho lưu trữ tùy chỉnh cho các mục trong giỏ hàng
 */
@Injectable()
export class CartItemRepository extends Repository<CartItem> {
  private readonly logger = new Logger(CartItemRepository.name);

  constructor(private dataSource: DataSource) {
    super(CartItem, dataSource.createEntityManager());
  }

  /**
   * Tạo truy vấn cơ bản cho cart item
   * @returns QueryBuilder cho cart item
   */
  private createBaseQuery(): SelectQueryBuilder<CartItem> {
    return this.createQueryBuilder('cartItem');
  }

  /**
   * Tìm cart item theo ID
   * @param id ID cart item
   * @returns Cart item hoặc null
   */
  async findById(id: number): Promise<CartItem | null> {
    return this.createBaseQuery()
      .where('cartItem.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm cart item theo ID với thông tin sản phẩm
   * @param id ID cart item
   * @returns Cart item với thông tin sản phẩm hoặc null
   */
  async findByIdWithProduct(id: number): Promise<CartItem | null> {
    try {
      // Sử dụng SQL trực tiếp để debug
      this.logger.debug(`Tìm cart item với ID: ${id}`);

      // Truy vấn cart item với join sản phẩm
      const cartItemQuery = this.dataSource
        .createQueryBuilder()
        .select('ci')
        .from(CartItem, 'ci')
        .leftJoinAndSelect(Product, 'p', 'p.id = ci.productId')
        .where('ci.id = :id', { id });

      const cartItem = await cartItemQuery.getOne();

      if (!cartItem) {
        this.logger.debug(`Không tìm thấy cart item với ID: ${id}`);
        return null;
      }

      // Tìm thông tin sản phẩm
      const productQuery = this.dataSource
        .createQueryBuilder()
        .select('p')
        .from(Product, 'p')
        .where('p.id = :productId', { productId: cartItem.productId });

      cartItem.product = await productQuery.getOne();

      this.logger.debug(`Đã tìm thấy cart item với ID: ${id}, product: ${cartItem.product ? 'có' : 'không'}`);
      return cartItem;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm cart item với ID ${id}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Tìm cart item theo ID giỏ hàng và ID sản phẩm
   * @param cartId ID giỏ hàng
   * @param productId ID sản phẩm
   * @returns Cart item hoặc null
   */
  async findByCartIdAndProductId(cartId: number, productId: number): Promise<CartItem | null> {
    return this.createBaseQuery()
      .where('cartItem.cartId = :cartId', { cartId })
      .andWhere('cartItem.productId = :productId', { productId })
      .getOne();
  }

  /**
   * Cập nhật số lượng của cart item
   * @param id ID cart item
   * @param quantity Số lượng mới
   * @returns Cart item đã cập nhật
   */
  async updateQuantity(id: number, quantity: number): Promise<CartItem> {
    await this.update(id, {
      quantity
      // Note: CartItem entity doesn't have updatedAt field
    });
    const updatedCartItem = await this.findById(id);
    if (!updatedCartItem) {
      throw new Error(`Cart item with ID ${id} not found after update`);
    }
    return updatedCartItem;
  }

  /**
   * Xóa cart item
   * @param id ID cart item
   * @returns true nếu xóa thành công
   */
  async removeCartItem(id: number): Promise<boolean> {
    try {
      this.logger.debug(`Xóa cart item với ID: ${id}`);
      const result = await this.delete(id);
      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả xóa cart item với ID ${id}: ${success ? 'thành công' : 'không thành công'}`);
      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa cart item với ID ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Xóa nhiều cart items
   * @param ids Danh sách ID cart items
   * @returns Số lượng cart items đã xóa thành công
   */
  async removeMultipleCartItems(ids: number[]): Promise<number> {
    try {
      this.logger.debug(`Xóa nhiều cart items với IDs: ${ids.join(', ')}`);

      if (!ids || ids.length === 0) {
        this.logger.warn('Danh sách ID cart items rỗng');
        return 0;
      }

      const result = await this.createQueryBuilder()
        .delete()
        .from(CartItem)
        .where('id IN (:...ids)', { ids })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Đã xóa ${deletedCount} cart items thành công`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa nhiều cart items: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Kiểm tra xem cart item có tồn tại trong giỏ hàng của người dùng không
   * @param id ID cart item
   * @param cartId ID giỏ hàng
   * @returns true nếu cart item tồn tại trong giỏ hàng
   */
  async checkCartItemExists(id: number, cartId: number): Promise<boolean> {
    try {
      this.logger.debug(`Kiểm tra cart item với ID: ${id} trong giỏ hàng ID: ${cartId}`);

      const query = this.dataSource
        .createQueryBuilder()
        .select('1')
        .from('cart_items', 'ci')
        .where('ci.id = :id', { id })
        .andWhere('ci.cart_id = :cartId', { cartId });

      const result = await query.getRawOne();
      const exists = !!result;

      this.logger.debug(`Cart item với ID: ${id} ${exists ? 'tồn tại' : 'không tồn tại'} trong giỏ hàng ID: ${cartId}`);
      return exists;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra cart item với ID ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Kiểm tra sản phẩm có hợp lệ để thêm vào giỏ hàng không
   * @param productId ID sản phẩm
   * @param userId ID người dùng
   * @returns true nếu sản phẩm hợp lệ
   */
  async isProductValidForCart(productId: number, userId: number): Promise<boolean> {
    const query = this.dataSource
      .createQueryBuilder()
      .select('1')
      .from('products', 'product')
      .where('product.id = :productId', { productId })
      .andWhere('product.status = :status', { status: ProductStatus.APPROVED })
      .andWhere('(product.user_id != :userId OR product.user_id IS NULL)', { userId });

    const result = await query.getRawOne();
    return !!result;
  }

  /**
   * Thêm sản phẩm vào giỏ hàng
   * @param cartId ID giỏ hàng
   * @param productId ID sản phẩm
   * @param quantity Số lượng
   * @returns Cart item đã tạo
   */
  async addToCart(cartId: number, productId: number, quantity: number): Promise<CartItem> {
    // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
    const existingItem = await this.findByCartIdAndProductId(cartId, productId);

    if (existingItem) {
      // Nếu đã có, cập nhật số lượng
      const newQuantity = existingItem.quantity + quantity;
      return this.updateQuantity(existingItem.id, newQuantity);
    } else {
      // Nếu chưa có, tạo mới cart item
      const cartItem = this.create({
        cartId,
        productId,
        quantity
      });

      return this.save(cartItem);
    }
  }

  /**
   * Đếm số lượng cart item trong giỏ hàng
   * @param cartId ID giỏ hàng
   * @returns Số lượng cart item
   */
  async countCartItems(cartId: number): Promise<number> {
    try {
      this.logger.debug(`Đếm số lượng cart item trong giỏ hàng ID: ${cartId}`);

      const count = await this.createBaseQuery()
        .where('cartItem.cartId = :cartId', { cartId })
        .getCount();

      this.logger.debug(`Số lượng cart item trong giỏ hàng ID ${cartId}: ${count}`);
      return count;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng cart item trong giỏ hàng ID ${cartId}: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Xóa sản phẩm khỏi giỏ hàng của người dùng
   * @param userId ID người dùng
   * @param productIds Danh sách ID sản phẩm cần xóa
   * @returns Số lượng sản phẩm đã xóa
   */
  async removeProductsFromCart(userId: number, productIds: number[]): Promise<number> {
    if (!userId || !productIds || productIds.length === 0) {
      this.logger.warn('Không có ID người dùng hoặc ID sản phẩm nào được cung cấp');
      return 0;
    }

    try {
      this.logger.debug(`Xóa sản phẩm khỏi giỏ hàng của người dùng ${userId}: ${productIds.join(', ')}`);

      // Tìm giỏ hàng của người dùng
      const cartQuery = this.dataSource
        .createQueryBuilder()
        .select('c.id')
        .from('carts', 'c')
        .where('c.user_id = :userId', { userId });

      const cartResult = await cartQuery.getRawOne();

      if (!cartResult) {
        this.logger.debug(`Không tìm thấy giỏ hàng của người dùng ${userId}`);
        return 0;
      }

      const cartId = cartResult.id;

      // Xóa các sản phẩm khỏi giỏ hàng
      const result = await this.createQueryBuilder()
        .delete()
        .from(CartItem)
        .where('cartId = :cartId', { cartId })
        .andWhere('productId IN (:...productIds)', { productIds })
        .execute();

      const removedCount = result.affected || 0;
      this.logger.debug(`Đã xóa ${removedCount} sản phẩm khỏi giỏ hàng của người dùng ${userId}`);

      return removedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa sản phẩm khỏi giỏ hàng: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Lấy danh sách cart item theo ID giỏ hàng, bao gồm cả các sản phẩm không có trạng thái APPROVED
   * @param cartId ID giỏ hàng
   * @returns Danh sách cart item
   */
  async findApprovedItemsByCartId(cartId: number): Promise<CartItem[]> {
    try {
      this.logger.debug(`Tìm các cart item cho giỏ hàng ID: ${cartId}`);

      // Sử dụng raw query để lấy đầy đủ thông tin
      const query = this.dataSource
        .createQueryBuilder()
        .select('ci.id', 'id')
        .addSelect('ci.cart_id', 'cartId')
        .addSelect('ci.product_id', 'productId')
        .addSelect('ci.quantity', 'quantity')
        .addSelect('p.id', 'product_id')
        .addSelect('p.name', 'product_name')
        .addSelect('p.description', 'product_description')
        .addSelect('p.listed_price', 'product_listedPrice')
        .addSelect('p.discounted_price', 'product_discountedPrice')
        .addSelect('p.images', 'product_images')
        .addSelect('p.status', 'product_status')
        .addSelect('p.category', 'product_category')
        .addSelect('u.id', 'user_id')
        .addSelect('u.full_name', 'user_fullName')
        .addSelect('u.email', 'user_email')
        .addSelect('u.avatar', 'user_avatar')
        .addSelect('e.id', 'employee_id')
        .addSelect('e.full_name', 'employee_fullName')
        .addSelect('e.email', 'employee_email')
        .from('cart_items', 'ci')
        .leftJoin('products', 'p', 'p.id = ci.product_id')
        .leftJoin('users', 'u', 'u.id = p.user_id')
        .leftJoin('employees', 'e', 'e.id = p.employee_id')
        .where('ci.cart_id = :cartId', { cartId });

      const cartItemsRaw = await query.getRawMany();
      this.logger.debug(`Tìm thấy ${cartItemsRaw.length} cart items cho giỏ hàng ID: ${cartId}`);

      if (cartItemsRaw.length === 0) {
        return [];
      }

      // Chuyển đổi raw data thành CartItem objects
      const cartItems: CartItem[] = cartItemsRaw.map(raw => {
        const cartItem = new CartItem();
        cartItem.id = raw.id;
        cartItem.cartId = raw.cartId;
        cartItem.productId = raw.productId;
        cartItem.quantity = raw.quantity;

        // Gán thông tin sản phẩm
        cartItem.product = {
          id: raw.product_id,
          name: raw.product_name,
          description: raw.product_description,
          listedPrice: raw.product_listedPrice,
          discountedPrice: raw.product_discountedPrice,
          images: raw.product_images,
          status: raw.product_status,
          category: raw.product_category,
          user: {
            id: raw.user_id,
            fullName: raw.user_fullName,
            email: raw.user_email,
            avatar: raw.user_avatar
          },
          employee: {
            id: raw.employee_id,
            fullName: raw.employee_fullName,
            email: raw.employee_email
          }
        };

        return cartItem;
      });

      // Log thông tin chi tiết về cart item đầu tiên để debug
      if (cartItems.length > 0) {
        const firstItem = cartItems[0];
        this.logger.debug(`Cart item đầu tiên: ID=${firstItem.id}, ProductID=${firstItem.productId}, ProductName=${firstItem.product?.name || 'N/A'}`);
      }

      return cartItems;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm cart items cho giỏ hàng ID ${cartId}: ${error.message}`, error.stack);
      return [];
    }
  }
}
