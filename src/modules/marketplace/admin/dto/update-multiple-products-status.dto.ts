import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty } from 'class-validator';
import { ProductStatus } from '@modules/marketplace/enums';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc cập nhật trạng thái nhiều sản phẩm
 */
export class UpdateMultipleProductsStatusDto {
  @ApiProperty({
    description: 'Trạng thái mới của sản phẩm',
    enum: ProductStatus,
    example: ProductStatus.APPROVED,
  })
  @IsNotEmpty()
  @IsEnum(ProductStatus)
  status: ProductStatus;

  @ApiProperty({
    description: 'Danh sách ID của các sản phẩm cần cập nhật trạng thái',
    type: [Number],
    example: [1, 2, 3],
  })
  @IsArray()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map(v => typeof v === 'string' ? parseInt(v, 10) : v);
    }
    return value;
  })
  productIds: number[];
}
