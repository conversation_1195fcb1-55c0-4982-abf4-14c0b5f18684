import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin lỗi khi xóa sản phẩm
 */
export class ProductDeleteErrorDto {
  @ApiProperty({
    description: 'ID của sản phẩm gặp lỗi khi xóa',
    example: 42,
  })
  id: number;

  @ApiProperty({
    description: 'Lý do lỗi',
    example: 'Sản phẩm không tồn tại',
  })
  reason: string;
}

/**
 * DTO phản hồi cho việc xóa nhiều sản phẩm
 */
export class DeleteMultipleProductsResponseDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm đã xóa thành công',
    type: [Number],
    example: [1, 2, 3],
  })
  successIds: number[];

  @ApiProperty({
    description: 'Danh sách sản phẩm gặp lỗi khi xóa',
    type: [ProductDeleteErrorDto],
    example: [
      {
        id: 4,
        reason: 'Sản phẩm không tồn tại',
      },
      {
        id: 5,
        reason: 'Không đủ quyền để xóa sản phẩm',
      },
    ],
  })
  failedIds: ProductDeleteErrorDto[];
}
