import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateProductAdminDto } from '../../dto/create-product-admin.dto';
import { ProductCategory } from '@modules/marketplace/enums';

describe('CreateProductAdminDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      description: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
      userManualMediaType: 'application/pdf',
      detailMediaType: 'application/pdf',
      sourceId: '34f5c7ef-649a-46e2-a399-34fc7c197032',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ với các trường tùy chọn bị bỏ qua', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải thất bại khi thiếu tên sản phẩm', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      description: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải thất bại khi tên sản phẩm quá ngắn', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI', // Ngắn hơn 3 ký tự
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('minLength');
  });

  it('phải thất bại khi tên sản phẩm quá dài', async () => {
    // Arrange
    const longName = 'A'.repeat(501); // Dài hơn 500 ký tự
    const dto = plainToInstance(CreateProductAdminDto, {
      name: longName,
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('maxLength');
  });

  it('phải thất bại khi giá niêm yết âm', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: -100, // Giá âm
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('phải thất bại khi giá sau giảm âm', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: -100, // Giá âm
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('phải thất bại khi loại sản phẩm không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: 'INVALID_CATEGORY', // Loại không hợp lệ
      imagesMediaTypes: ['image/jpeg', 'image/png'],
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('phải thất bại khi thiếu danh sách MIME types của ảnh', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      // Thiếu imagesMediaTypes
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('phải thất bại khi danh sách MIME types của ảnh không phải là mảng chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: [123, 456], // Không phải mảng chuỗi
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('phải thất bại khi sourceId không phải là UUID hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateProductAdminDto, {
      name: 'AI Chatbot Template',
      listedPrice: 1200,
      discountedPrice: 1000,
      category: ProductCategory.KNOWLEDGE_FILE,
      imagesMediaTypes: ['image/jpeg', 'image/png'],
      sourceId: 'not-a-valid-uuid', // UUID không hợp lệ
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isUuid');
  });
});
