import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CartAdminService } from '../services/cart-admin.service';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  CartQueryDto,
  CartAdminResponseDto
} from '../dto';
import { SwaggerApiTag } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';

@ApiTags(SwaggerApiTag.ADMIN_MARKETPLACE_CART)
@ApiExtraModels(ApiResponseDto, CartAdminResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/marketplace/cart')
export class CartAdminController {
  constructor(private readonly cartAdminService: CartAdminService) {}

  /**
   * Lấy danh sách giỏ hàng của tất cả người dùng với tìm kiếm, lọc, sắp xếp và phân trang
   * @param employeeId ID của nhân viên
   * @param queryDto Tham số truy vấn (tìm kiếm, lọc, sắp xếp, phân trang)
   * @returns Danh sách giỏ hàng với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách giỏ hàng của tất cả người dùng',
    description: 'Lấy danh sách giỏ hàng của tất cả người dùng (bao gồm cả admin và user) với tìm kiếm, lọc, sắp xếp và phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách giỏ hàng',
    schema: ApiResponseDto.getPaginatedSchema(CartAdminResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED,
    MARKETPLACE_ERROR_CODES.GENERAL_ERROR
  )
  async getAllCarts(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: CartQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<CartAdminResponseDto>>> {
    const result = await this.cartAdminService.getCarts(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách giỏ hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết giỏ hàng theo ID
   * @param employeeId ID của nhân viên
   * @param cartId ID của giỏ hàng
   * @returns Thông tin chi tiết giỏ hàng
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết giỏ hàng theo ID',
    description: 'Lấy thông tin chi tiết giỏ hàng bao gồm danh sách sản phẩm trong giỏ hàng'
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của giỏ hàng',
    example: 1
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết giỏ hàng',
    schema: ApiResponseDto.getSchema(CartAdminResponseDto)
  })
  @ApiErrorResponse(
    MARKETPLACE_ERROR_CODES.CART_NOT_FOUND,
    MARKETPLACE_ERROR_CODES.CART_RETRIEVAL_FAILED
  )
  async getCartById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) cartId: number
  ): Promise<ApiResponseDto<CartAdminResponseDto>> {
    const result = await this.cartAdminService.getCartById(employeeId, cartId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết giỏ hàng thành công');
  }
}
