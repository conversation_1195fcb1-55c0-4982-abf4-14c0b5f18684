import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_step_connections trong cơ sở dữ liệu
 * Bảng lưu trữ các kết nối gi<PERSON>a các bước trong nhiệm vụ, x<PERSON><PERSON> output của step n<PERSON>o đ<PERSON><PERSON><PERSON> sử dụng làm input cho step nào
 */
@Entity('admin_step_connections')
export class AdminStepConnection {
  /**
   * Khóa chính của bảng admin_step_connections, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'connection_id' })
  connectionId: string;

  /**
   * ID của nhiệm vụ, tham chiếu đến bảng admin_tasks
   */
  @Column({ name: 'task_id', type: 'uuid', nullable: false })
  taskId: string;

  /**
   * ID của step nguồn, tham chiếu đến bảng admin_steps
   */
  @Column({ name: 'from_step_id', type: 'uuid', nullable: false })
  fromStepId: string;

  /**
   * ID của step đích, tham chiế<PERSON> đến bảng admin_steps
   */
  @Column({ name: 'to_step_id', type: 'uuid', nullable: false })
  toStepId: string;

  /**
   * Tên trường trong output của step nguồn
   */
  @Column({ name: 'output_field', type: 'varchar', length: 255, nullable: false })
  outputField: string;

  /**
   * Tên trường trong input của step đích
   */
  @Column({ name: 'input_field', type: 'varchar', length: 255, nullable: false })
  inputField: string;

  /**
   * Thời điểm tạo kết nối (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật kết nối (Unix epoch)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
}
