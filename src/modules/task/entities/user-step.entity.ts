import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { StepConfig, StepType } from '../interfaces/step-config.interface';

/**
 * Entity đại diện cho bảng user_steps trong cơ sở dữ liệu
 * Bảng lưu định nghĩa các bước trong nhiệm vụ
 */
@Entity('user_steps')
@Unique('unique_task_order', ['taskId', 'orderIndex'])
export class UserStep {
  /**
   * Khóa chính của bảng user_steps, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'step_id' })
  stepId: string;

  /**
   * ID của nhiệm vụ, tham chiếu đến bảng user_tasks
   */
  @Column({ name: 'task_id', type: 'uuid', nullable: false })
  taskId: string;

  /**
   * Thứ tự xuất hiện của bước trong nhiệm vụ
   */
  @Column({ name: 'order_index', type: 'int', nullable: false })
  orderIndex: number;

  /**
   * Tên của bước
   */
  @Column({ name: 'step_name', type: 'varchar', length: 255, nullable: false })
  stepName: string;

  /**
   * Mô tả chi tiết của bước
   */
  @Column({ name: 'step_description', type: 'text', nullable: true })
  stepDescription: string;

  /**
   * Loại bước (Prompt, Trigger, Action, Media)
   */
  @Column({
    name: 'step_type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  stepType: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSONB
   */
  @Column({ name: 'step_config', type: 'jsonb', nullable: true })
  stepConfig: StepConfig;

  /**
   * ID xác thực Google của người dùng
   */
  @Column({ name: 'google_user_auth_id', type: 'uuid', nullable: true })
  googleUserAuthId: string;

  /**
   * ID trang Facebook
   */
  @Column({ name: 'facebook_page_id', type: 'uuid', nullable: true })
  facebookPageId: string;

  /**
   * Thời điểm tạo bước (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bước (Unix epoch)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
}
