import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { StepType } from '../interfaces/step-config.interface';

/**
 * Entity đại diện cho bảng admin_steps trong cơ sở dữ liệu
 * Bảng lưu định nghĩa các bước trong nhiệm vụ
 */
@Entity('admin_steps')
@Unique('unique_admin_task_order', ['taskId', 'orderIndex'])
export class AdminStep {
  /**
   * Khóa chính của bảng admin_steps, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'step_id' })
  stepId: string;

  /**
   * ID của nhiệm vụ, tham chiếu đến bảng admin_tasks
   */
  @Column({ name: 'task_id', type: 'uuid', nullable: false })
  taskId: string;

  /**
   * Thứ tự xuất hiện của bước trong nhiệm vụ
   */
  @Column({ name: 'order_index', type: 'int', nullable: false })
  orderIndex: number;

  /**
   * Tên của bước
   */
  @Column({ name: 'step_name', type: 'varchar', length: 255, nullable: false })
  stepName: string;

  /**
   * Mô tả chi tiết của bước
   */
  @Column({ name: 'step_description', type: 'text', nullable: true })
  stepDescription: string;

  /**
   * Loại bước (Prompt, Trigger, Action, Media)
   */
  @Column({
    name: 'step_type',
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  stepType: StepType;

  /**
   * Cấu hình chi tiết của bước ở dạng JSONB
   */
  @Column({ name: 'step_config', type: 'jsonb', nullable: true })
  stepConfig: Record<string, any>;

  /**
   * Thời điểm tạo bước (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bước (Unix epoch)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;
}
