import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến execution
 * Phạm vi mã lỗi: 10300 - 10399
 */
export const EXECUTION_ERROR_CODES = {
  // Lỗi chung
  EXECUTION_NOT_FOUND: new ErrorCode(10300, 'Không tìm thấy phiên thực thi', HttpStatus.NOT_FOUND),
  EXECUTION_CREATION_FAILED: new ErrorCode(10301, 'Tạo phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  EXECUTION_UPDATE_FAILED: new ErrorCode(10302, 'Cập nhật phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  EXECUTION_DELETE_FAILED: new ErrorCode(10303, 'X<PERSON>a phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  EXECUTION_FETCH_FAILED: new ErrorCode(10304, 'Lấy thông tin phiên thực thi thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  EXECUTION_UNAUTHORIZED: new ErrorCode(10310, 'Không có quyền truy cập phiên thực thi này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  EXECUTION_ALREADY_COMPLETED: new ErrorCode(10320, 'Phiên thực thi đã hoàn thành', HttpStatus.BAD_REQUEST),
  EXECUTION_ALREADY_FAILED: new ErrorCode(10321, 'Phiên thực thi đã thất bại', HttpStatus.BAD_REQUEST),
  EXECUTION_ALREADY_CANCELLED: new ErrorCode(10322, 'Phiên thực thi đã bị hủy', HttpStatus.BAD_REQUEST),
  EXECUTION_INVALID_STATUS: new ErrorCode(10323, 'Trạng thái phiên thực thi không hợp lệ', HttpStatus.BAD_REQUEST),
  EXECUTION_CANNOT_RESUME: new ErrorCode(10324, 'Không thể tiếp tục phiên thực thi này', HttpStatus.BAD_REQUEST),
  
  // Lỗi thực thi
  EXECUTION_STEP_FAILED: new ErrorCode(10330, 'Thực thi bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  EXECUTION_MISSING_INPUT: new ErrorCode(10331, 'Thiếu dữ liệu đầu vào cho bước thực thi', HttpStatus.BAD_REQUEST),
  EXECUTION_INVALID_INPUT: new ErrorCode(10332, 'Dữ liệu đầu vào không hợp lệ cho bước thực thi', HttpStatus.BAD_REQUEST),
  EXECUTION_TIMEOUT: new ErrorCode(10333, 'Thực thi bước bị vượt quá thời gian', HttpStatus.REQUEST_TIMEOUT),
  EXECUTION_EXTERNAL_SERVICE_ERROR: new ErrorCode(10334, 'Lỗi dịch vụ bên ngoài khi thực thi bước', HttpStatus.BAD_GATEWAY),
  
  // Lỗi giới hạn
  EXECUTION_LIMIT_EXCEEDED: new ErrorCode(10340, 'Đã vượt quá giới hạn số lượng phiên thực thi', HttpStatus.BAD_REQUEST),
  EXECUTION_RATE_LIMIT: new ErrorCode(10341, 'Đã vượt quá tần suất thực thi nhiệm vụ', HttpStatus.TOO_MANY_REQUESTS),
};
