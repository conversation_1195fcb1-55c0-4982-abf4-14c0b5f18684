import { StepType } from "./step-config.interface";

/**
 * Interface cho dữ liệu step
 */
export interface IStep {
  /**
   * ID của step
   */
  stepId: string;
  
  /**
   * ID của task chứa step
   */
  taskId: string;
  
  /**
   * Thứ tự của step trong task
   */
  orderIndex: number;
  
  /**
   * Tên của step
   */
  stepName: string;
  
  /**
   * Mô tả của step
   */
  stepDescription?: string;
  
  /**
   * Loại của step
   */
  stepType: StepType;
  
  /**
   * Cấu hình của step
   */
  stepConfig?: Record<string, any>;
  
  /**
   * ID xác thực Google của người dùng (nếu cần)
   */
  googleUserAuthId?: string;
  
  /**
   * ID trang Facebook (nếu cần)
   */
  facebookPageId?: string;
  
  /**
   * Thời gian tạo step
   */
  createdAt: number;
  
  /**
   * Thời gian cập nhật step
   */
  updatedAt: number;
}

/**
 * Interface cho cấu hình step loại Prompt
 */
export interface IPromptStepConfig {
  /**
   * Nội dung prompt
   */
  content: string;
  
  /**
   * Các biến trong prompt
   */
  variables?: string[];
  
  /**
   * Các tùy chọn khác
   */
  options?: Record<string, any>;
}

/**
 * Interface cho cấu hình step loại Trigger
 */
export interface ITriggerStepConfig {
  /**
   * Loại trigger
   */
  triggerType: string;
  
  /**
   * Điều kiện trigger
   */
  condition: Record<string, any>;
  
  /**
   * Các tùy chọn khác
   */
  options?: Record<string, any>;
}

/**
 * Interface cho cấu hình step loại Action
 */
export interface IActionStepConfig {
  /**
   * Loại action
   */
  actionType: string;
  
  /**
   * Tham số cho action
   */
  parameters: Record<string, any>;
  
  /**
   * Các tùy chọn khác
   */
  options?: Record<string, any>;
}

/**
 * Interface cho cấu hình step loại Media
 */
export interface IMediaStepConfig {
  /**
   * Loại media
   */
  mediaType: string;
  
  /**
   * Nguồn media
   */
  source: string;
  
  /**
   * Các tùy chọn xử lý
   */
  processing?: Record<string, any>;
  
  /**
   * Các tùy chọn khác
   */
  options?: Record<string, any>;
}
