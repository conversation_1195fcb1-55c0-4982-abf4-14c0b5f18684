import { Injectable, Logger } from '@nestjs/common';
import { UserStepConnectionRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến kết nối giữa các bước trong task của người dùng
 */
@Injectable()
export class UserConnectionService {
  /**
   * Logger cho UserConnectionService
   */
  private readonly logger = new Logger(UserConnectionService.name);

  /**
   * Constructor
   * @param userStepConnectionRepository Repository xử lý dữ liệu kết nối gi<PERSON>a các bước trong task của người dùng
   */
  constructor(
    private readonly userStepConnectionRepository: UserStepConnectionRepository,
  ) {}
}
