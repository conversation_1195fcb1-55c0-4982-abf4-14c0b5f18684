import { JwtUserGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserStepService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến các bước trong task của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_TASK)
@Controller('user/steps')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserStepController {
  /**
   * Constructor
   * @param userStepService Service xử lý logic liên quan đến các bước trong task của người dùng
   */
  constructor(private readonly userStepService: UserStepService) {}
}
