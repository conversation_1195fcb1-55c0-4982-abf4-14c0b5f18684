import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTask,
  AdminStep,
  AdminTaskExecution,
  AdminStepConnection,
} from '../entities';
import {
  AdminTaskRepository,
  AdminStepRepository,
  AdminTaskExecutionRepository,
  AdminStepConnectionRepository,
} from '../repositories';
import {
  AdminTaskController,
  AdminStepController,
  AdminConnectionController
} from './controllers';
import {
  AdminTaskService,
  AdminStepService,
  AdminConnectionService
} from './services';

/**
 * Module quản lý task cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminTask,
      AdminStep,
      AdminTaskExecution,
      AdminStepConnection,
    ])
  ],
  controllers: [
    AdminTaskController,
    AdminStepController,
    AdminConnectionController
  ],
  providers: [
    AdminTaskRepository,
    AdminStepRepository,
    AdminTaskExecutionRepository,
    AdminStepConnectionRepository,
    AdminTaskService,
    AdminStepService,
    AdminConnectionService
  ],
  exports: [
    AdminTaskService,
    AdminStepService,
    AdminConnectionService
  ],
})
export class TaskAdminModule {}
