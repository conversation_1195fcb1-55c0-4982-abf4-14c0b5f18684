import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc gửi dữ liệu form
 */
export class SubmitFormDto {
  /**
   * Dữ liệu form được gửi
   * @example { "name": "Nguyễn Văn A", "email": "nguy<PERSON><EMAIL>", "phone": "0901234567", "message": "Tôi muốn biết thêm thông tin về sản phẩm của công ty." }
   */
  @ApiProperty({
    description: 'Dữ liệu form được gửi',
    example: {
      name: '<PERSON>uy<PERSON>n <PERSON>ăn <PERSON>',
      email: 'nguy<PERSON><EMAIL>',
      phone: '0901234567',
      message: 'Tôi muốn biết thêm thông tin về sản phẩm của công ty.',
    },
  })
  @IsNotEmpty({ message: 'Dữ liệu form không được để trống' })
  @IsObject({ message: 'Dữ liệu form phải là đối tượng JSON' })
  @Type(() => Object)
  data: Record<string, any>;
}
