import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { GenericFormConfig } from '../interfaces/generic-form-config.interface';

/**
 * Entity đại diện cho bảng generic_page_templates trong cơ sở dữ liệu
 * Bảng lưu thông tin các mẫu trang có thể tái sử dụng
 */
@Entity('generic_page_templates')
export class GenericPageTemplate {
  /**
   * ID duy nhất của mẫu trang, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của mẫu trang, hiển thị trong giao diện quản trị
   */
  @Column({ length: 255 })
  name: string;

  /**
   * Mô tả chi tiết về mẫu trang và mục đích sử dụng
   */
  @Column({ type: 'text', nullable: true })
  description: string;

  /**
   * <PERSON><PERSON> mục của mẫu trang, dùng để phân loại và lọc
   */
  @Column({ length: 100, nullable: true })
  category: string;

  /**
   * URL hình thu nhỏ minh họa cho mẫu trang
   */
  @Column({ length: 255, nullable: true })
  thumbnail: string;

  /**
   * Cấu hình mẫu trang dạng JSON, bao gồm layout, components, và các thiết lập khác
   */
  @Column({ type: 'json' })
  config: GenericFormConfig;

  /**
   * Thời điểm tạo mẫu trang, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật mẫu trang gần nhất, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  /**
   * ID của người dùng tạo mẫu trang
   */
  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  /**
   * ID của người dùng cập nhật mẫu trang gần nhất
   */
  @Column({ name: 'updated_by', length: 36 })
  updatedBy: string;
}
