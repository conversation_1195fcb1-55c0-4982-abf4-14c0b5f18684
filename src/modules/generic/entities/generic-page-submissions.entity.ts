import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON>umn, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { GenericPage } from './generic-pages.entity';

export enum SubmissionStatus {
  PENDING = 'pending',
  PROCESSED = 'processed',
  REJECTED = 'rejected',
}

@Entity('generic_page_submissions')
export class GenericPageSubmission {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id: string;

  @Column({ type: 'varchar', length: 36 })
  @Index('idx_generic_page_submissions_page_id')
  page_id: string;

  @Column({ type: 'jsonb' })
  data: Record<string, any>;

  @Column({
    type: 'enum',
    enum: SubmissionStatus,
    default: SubmissionStatus.PENDING,
  })
  @Index('idx_generic_page_submissions_status')
  status: SubmissionStatus;

  @Column({ type: 'bigint' })
  created_at: number;

  @Column({ type: 'bigint' })
  updated_at: number;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ip_address?: string;

  @Column({ type: 'text', nullable: true })
  user_agent?: string;

  @Column({ type: 'varchar', length: 36, nullable: true })
  user_id?: string;
}