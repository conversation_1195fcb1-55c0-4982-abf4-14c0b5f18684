import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { GenericPageStatusEnum } from '../constants/generic-page.enum';
import { GenericFormConfig } from '../interfaces/generic-form-config.interface';

/**
 * Entity đại diện cho bảng generic_pages trong cơ sở dữ liệu
 * Bảng lưu thông tin các trang tùy chỉnh
 */
@Entity('generic_pages')
export class GenericPage {
  /**
   * ID duy nhất của trang, dạng UUID
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của trang, hiển thị trong giao diện quản trị
   */
  @Column({ length: 255 })
  name: string;

  /**
   * Mô tả chi tiết về trang, hỗ trợ cho việc quản lý
   */
  @Column({ type: 'text', nullable: true })
  description: string;

  /**
   * Đường dẫn URL của trang, dùng để truy cập trang từ frontend
   */
  @Column({ length: 255, unique: true })
  path: string;

  /**
   * C<PERSON><PERSON> hình trang dạng JSON, bao gồm layout, components, và các thiết lập khác
   */
  @Column({ type: 'json' })
  config: GenericFormConfig;

  /**
   * Trạng thái của trang: draft (nháp), published (đã xuất bản), archived (đã lưu trữ)
   */
  @Column({
    type: 'enum',
    enum: GenericPageStatusEnum,
    default: GenericPageStatusEnum.DRAFT,
  })
  status: GenericPageStatusEnum;

  /**
   * Thời điểm tạo trang, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật trang gần nhất, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  /**
   * Thời điểm xuất bản trang, dạng Unix timestamp (milliseconds)
   */
  @Column({ name: 'published_at', type: 'bigint', nullable: true })
  publishedAt: number | null;

  /**
   * ID của người dùng tạo trang
   */
  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  /**
   * ID của người dùng cập nhật trang gần nhất
   */
  @Column({ name: 'updated_by', length: 36 })
  updatedBy: string;
}
