/**
 * Interface cho cấu trúc validation
 */
export interface ValidationConfig {
  /** Đ<PERSON> dài tối thiểu */
  minLength?: number;

  /** Đ<PERSON> dài tối đa */
  maxLength?: number;

  /** Mẫu regex để kiểm tra */
  pattern?: string;

  /** Danh sách các giá trị hợp lệ */
  validOptions?: string[];

  /** Số lượng lựa chọn tối thiểu */
  minSelections?: number;

  /** Số lượng lựa chọn tối đa */
  maxSelections?: number;

  /** <PERSON><PERSON>y tối thiểu (ISO format) */
  minDate?: string;

  /** Ngày tối đa (ISO format) */
  maxDate?: string;

  /** Thời gian tối thiểu (HH:mm) */
  minTime?: string;

  /** Thời gian tối đa (HH:mm) */
  maxTime?: string;

  /** <PERSON><PERSON><PERSON> định dạng file đư<PERSON><PERSON> chấp nhận */
  acceptedFormats?: string[];

  /** <PERSON><PERSON>ch thước tối đa (bytes) */
  maxSize?: number;

  /** Gi<PERSON> trị tối thiểu */
  minValue?: number;

  /** Giá trị tối đa */
  maxValue?: number;

  /** Danh sách các màu hợp lệ */
  validColors?: string[];

  /** Danh sách các giá trị boolean được phép */
  allowedValues?: boolean[];
}

/**
 * Interface cho cấu trúc config của component
 */
export interface ComponentConfig {
  /** ID của component */
  id: string;

  /** Nhãn hiển thị của component */
  label: string;

  /** Loại component */
  type?: string;

  /** Trường bắt buộc hay không */
  required?: boolean;

  /** Cấu hình validation */
  validation?: ValidationConfig;

  /** Danh sách các lựa chọn */
  options?: string[];

  /** Giá trị tối thiểu */
  min?: number;

  /** Giá trị tối đa */
  max?: number;

  /** Bước nhảy */
  step?: number;

  /** Giá trị mặc định */
  defaultValue?: any;

  /** Placeholder */
  placeholder?: string;

  /** Biến thể của component */
  variant?: string;

  /** Kích thước của component */
  size?: string;

  /** Component bị vô hiệu hóa hay không */
  disabled?: boolean;

  /** Component chỉ đọc hay không */
  readOnly?: boolean;

  /** Văn bản trợ giúp */
  helperText?: string;
}

/**
 * Interface cho cấu trúc grid
 */
export interface GridConfig {
  /** ID của grid item */
  i: string;

  /** Vị trí X trên grid */
  x: number;

  /** Vị trí Y trên grid */
  y: number;

  /** Chiều rộng trên grid */
  w: number;

  /** Chiều cao trên grid */
  h: number;
}

/**
 * Interface cho cấu trúc field
 */
export interface FieldConfig {
  /** Loại component */
  component: string;

  /** Cấu hình của component */
  config: ComponentConfig;

  /** Cấu hình grid */
  grid: GridConfig;
}

/**
 * Interface cho cấu trúc group
 */
export interface GroupConfig {
  /** ID của group */
  id: string;

  /** Nhãn của group */
  label: string;

  /** Danh sách các field trong group */
  fields: FieldConfig[];
}

/**
 * Interface cho cấu trúc form
 */
export interface FormConfig {
  /** Cấu hình group */
  group: GroupConfig;
}

/**
 * Interface cho form values
 */
export interface FormValues {
  /** Các giá trị của form */
  [key: string]: any;
}

/**
 * Interface cho request tạo form
 */
export interface CreateFormRequest {
  /** Cấu hình form */
  formConfig: FormConfig[];

  /** Giá trị khởi tạo */
  initialValues?: FormValues;

  /** Tiêu đề form */
  title?: string;

  /** Tiêu đề phụ */
  subtitle?: string;
}

/**
 * Interface cho response form
 */
export interface FormResponse {
  /** Cấu hình form */
  formConfig: FormConfig[];

  /** Giá trị của form */
  values?: FormValues;

  /** Tiêu đề form */
  title?: string;

  /** Tiêu đề phụ */
  subtitle?: string;
}

/**
 * Interface cho request submit form
 */
export interface SubmitFormRequest {
  /** ID của form */
  formId: string;

  /** Giá trị của form */
  values: FormValues;
}

/**
 * Interface cho response submit form
 */
export interface SubmitFormResponse {
  /** ID của form đã submit */
  formId: string;

  /** Trạng thái submit */
  success: boolean;

  /** Thông báo */
  message?: string;
}

/**
 * Enum cho các loại component
 */
export enum ComponentType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  DATE = 'date',
  TIME = 'time',
  DATETIME = 'datetime',
  NUMBER = 'number',
  SLIDER = 'slider',
  SWITCH = 'switch',
  FILE = 'file',
  COLOR = 'color',
  AUTOCOMPLETE = 'autocomplete',
  RATING = 'rating',
  BUTTON = 'button'
}

/**
 * Enum cho các kích thước component
 */
export enum ComponentSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

/**
 * Enum cho các biến thể component
 */
export enum ComponentVariant {
  STANDARD = 'standard',
  OUTLINED = 'outlined',
  FILLED = 'filled'
}

/**
 * Enum cho trạng thái form
 */
export enum FormStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}
