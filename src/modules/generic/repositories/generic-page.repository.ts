import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericPage } from '../entities/generic-page.entity';
import { PaginatedResult } from '@/common/response';
import { GenericPageStatusEnum } from '../constants/generic-page.enum';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../exceptions/generic-page-error.code';

@Injectable()
export class GenericPageRepository extends Repository<GenericPage> {
  private readonly logger = new Logger(GenericPageRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericPage, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho GenericPage
   * @returns SelectQueryBuilder<GenericPage>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericPage> {
    return this.createQueryBuilder('genericPage');
  }

  /**
   * Tìm trang theo ID
   * @param id ID của trang
   * @returns Trang nếu tìm thấy
   * @throws AppException nếu không tìm thấy trang
   */
  async findById(id: string): Promise<GenericPage> {
    try {
      const page = await this.createBaseQuery()
        .where('genericPage.id = :id', { id })
        .getOne();

      if (!page) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
          `Không tìm thấy trang với ID ${id}`,
        );
      }

      return page;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding page by ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Lỗi khi tìm trang với ID ${id}`,
      );
    }
  }

  /**
   * Tìm trang theo đường dẫn
   * @param path Đường dẫn của trang
   * @returns Trang nếu tìm thấy
   * @throws AppException nếu không tìm thấy trang
   */
  async findByPath(path: string): Promise<GenericPage> {
    try {
      const page = await this.createBaseQuery()
        .where('genericPage.path = :path', { path })
        .getOne();

      if (!page) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
          `Không tìm thấy trang với đường dẫn ${path}`,
        );
      }

      return page;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding page by path: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Lỗi khi tìm trang với đường dẫn ${path}`,
      );
    }
  }

  /**
   * Kiểm tra xem đường dẫn đã tồn tại chưa
   * @param path Đường dẫn cần kiểm tra
   * @param excludeId ID của trang cần loại trừ (dùng khi cập nhật)
   * @returns true nếu đường dẫn đã tồn tại, false nếu chưa
   */
  async isPathExists(path: string, excludeId?: string): Promise<boolean> {
    try {
      const query = this.createBaseQuery()
        .where('genericPage.path = :path', { path });

      if (excludeId) {
        query.andWhere('genericPage.id != :excludeId', { excludeId });
      }

      const count = await query.getCount();
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking path existence: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_CREATE_ERROR,
        'Lỗi khi kiểm tra đường dẫn',
      );
    }
  }

  /**
   * Tìm trang đã xuất bản theo đường dẫn
   * @param path Đường dẫn của trang
   * @returns Trang nếu tìm thấy
   * @throws AppException nếu không tìm thấy trang hoặc trang chưa xuất bản
   */
  async findPublishedByPath(path: string): Promise<GenericPage> {
    try {
      const page = await this.createBaseQuery()
        .where('genericPage.path = :path', { path })
        .andWhere('genericPage.status = :status', { status: GenericPageStatusEnum.PUBLISHED })
        .getOne();

      if (!page) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
          `Không tìm thấy trang đã xuất bản với đường dẫn ${path}`,
        );
      }

      return page;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding published page by path: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_NOT_FOUND,
        `Lỗi khi tìm trang đã xuất bản với đường dẫn ${path}`,
      );
    }
  }
}
