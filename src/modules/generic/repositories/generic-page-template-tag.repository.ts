import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { GenericPageTemplateTag } from '../entities/generic-page-template-tag.entity';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../exceptions/generic-page-error.code';

@Injectable()
export class GenericPageTemplateTagRepository extends Repository<GenericPageTemplateTag> {
  private readonly logger = new Logger(GenericPageTemplateTagRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericPageTemplateTag, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả tag của một mẫu trang
   * @param templateId ID của mẫu trang
   * @returns Danh sách tag của mẫu trang
   */
  async findTagsByTemplateId(templateId: string): Promise<string[]> {
    try {
      const tags = await this.find({
        where: { templateId },
        select: ['tag'],
      });

      return tags.map((tag) => tag.tag);
    } catch (error) {
      this.logger.error(`Error finding tags by template ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
        `Lỗi khi tìm tag cho mẫu trang với ID ${templateId}`,
      );
    }
  }

  /**
   * Xóa tất cả tag của một mẫu trang
   * @param templateId ID của mẫu trang
   */
  async deleteTagsByTemplateId(templateId: string): Promise<void> {
    try {
      await this.delete({ templateId });
    } catch (error) {
      this.logger.error(`Error deleting tags by template ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_DELETE_ERROR,
        `Lỗi khi xóa tag cho mẫu trang với ID ${templateId}`,
      );
    }
  }

  /**
   * Lưu nhiều tag cho một mẫu trang
   * @param templateId ID của mẫu trang
   * @param tags Danh sách tag
   */
  async saveTags(templateId: string, tags: string[]): Promise<void> {
    try {
      // Xóa tất cả tag cũ
      await this.deleteTagsByTemplateId(templateId);

      // Không có tag mới để lưu
      if (!tags || tags.length === 0) {
        return;
      }

      // Tạo các entity tag mới
      const tagEntities = tags.map((tag) => {
        const tagEntity = new GenericPageTemplateTag();
        tagEntity.templateId = templateId;
        tagEntity.tag = tag;
        return tagEntity;
      });

      // Lưu tất cả tag mới
      await this.save(tagEntities);
    } catch (error) {
      this.logger.error(`Error saving tags for template: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_UPDATE_ERROR,
        `Lỗi khi lưu tag cho mẫu trang với ID ${templateId}`,
      );
    }
  }
}
