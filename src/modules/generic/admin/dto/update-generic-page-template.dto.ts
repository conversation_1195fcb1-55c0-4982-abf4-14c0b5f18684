import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, Min<PERSON>ength, IsArray, IsObject, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';
import { GenericFormConfig } from '../../interfaces/generic-form-config.interface';

/**
 * DTO cho việc cập nhật mẫu trang
 */
export class UpdateGenericPageTemplateDto {
  /**
   * Tên của mẫu trang
   * @example "Mẫu form liên hệ mới"
   */
  @ApiProperty({
    description: 'Tên của mẫu trang',
    example: 'Mẫu form liên hệ mới',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên mẫu trang phải là chuỗi' })
  @MinLength(3, { message: 'Tên mẫu trang phải có ít nhất 3 ký tự' })
  @MaxLength(255, { message: 'Tên mẫu trang không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * <PERSON><PERSON> tả về mẫu trang
   * @example "Mẫu form liên hệ cơ bản với các trường thông tin liên hệ mới"
   */
  @ApiProperty({
    description: 'Mô tả về mẫu trang',
    example: 'Mẫu form liên hệ cơ bản với các trường thông tin liên hệ mới',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Danh mục của mẫu trang
   * @example "Form mới"
   */
  @ApiProperty({
    description: 'Danh mục của mẫu trang',
    example: 'Form mới',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Danh mục phải là chuỗi' })
  @MaxLength(100, { message: 'Danh mục không được vượt quá 100 ký tự' })
  category?: string;

  /**
   * URL hình thu nhỏ minh họa cho mẫu trang
   * @example "/assets/images/templates/contact-form-new.jpg"
   */
  @ApiProperty({
    description: 'URL hình thu nhỏ minh họa cho mẫu trang',
    example: '/assets/images/templates/contact-form-new.jpg',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'URL hình thu nhỏ phải là chuỗi' })
  @MaxLength(255, { message: 'URL hình thu nhỏ không được vượt quá 255 ký tự' })
  thumbnail?: string;

  /**
   * Danh sách tag cho mẫu trang
   * @example ["form", "liên hệ", "cơ bản", "mới"]
   */
  @ApiProperty({
    description: 'Danh sách tag cho mẫu trang',
    example: ['form', 'liên hệ', 'cơ bản', 'mới'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @ArrayMaxSize(10, { message: 'Tối đa 10 tag' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  @MaxLength(50, { each: true, message: 'Mỗi tag không được vượt quá 50 ký tự' })
  tags?: string[];

  /**
   * Cấu hình mẫu trang dạng JSON
   * @example { "formId": "contact-form-template", "title": "Liên hệ với chúng tôi", "groups": [] }
   */
  @ApiProperty({
    description: 'Cấu hình mẫu trang dạng JSON',
    example: {
      formId: 'contact-form-template',
      title: 'Liên hệ với chúng tôi',
      subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
      groups: [],
    },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình mẫu trang phải là đối tượng JSON' })
  @Type(() => Object)
  config?: GenericFormConfig;
}
