import { ApiProperty } from '@nestjs/swagger';
import { GenericFormConfig } from '../../interfaces/generic-form-config.interface';

/**
 * DTO cho phản hồi thông tin mẫu trang
 */
export class GenericPageTemplateResponseDto {
  /**
   * ID của mẫu trang
   * @example "e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b"
   */
  @ApiProperty({
    description: 'ID của mẫu trang',
    example: 'e2f3a4b5-c6d7-4e5f-8a9b-0c1d2e3f4a5b',
  })
  id: string;

  /**
   * Tên của mẫu trang
   * @example "Mẫu form liên hệ"
   */
  @ApiProperty({
    description: 'Tên của mẫu trang',
    example: 'Mẫu form liên hệ',
  })
  name: string;

  /**
   * Mô tả về mẫu trang
   * @example "Mẫu form liên hệ cơ bản với các trường thông tin liên hệ"
   */
  @ApiProperty({
    description: '<PERSON><PERSON> tả về mẫu trang',
    example: 'Mẫu form liên hệ cơ bản với các trường thông tin liên hệ',
    nullable: true,
  })
  description: string | null;

  /**
   * Danh mục của mẫu trang
   * @example "Form"
   */
  @ApiProperty({
    description: 'Danh mục của mẫu trang',
    example: 'Form',
    nullable: true,
  })
  category: string | null;

  /**
   * URL hình thu nhỏ minh họa cho mẫu trang
   * @example "/assets/images/templates/contact-form.jpg"
   */
  @ApiProperty({
    description: 'URL hình thu nhỏ minh họa cho mẫu trang',
    example: '/assets/images/templates/contact-form.jpg',
    nullable: true,
  })
  thumbnail: string | null;

  /**
   * Danh sách tag cho mẫu trang
   * @example ["form", "liên hệ", "cơ bản"]
   */
  @ApiProperty({
    description: 'Danh sách tag cho mẫu trang',
    example: ['form', 'liên hệ', 'cơ bản'],
    type: [String],
  })
  tags: string[];

  /**
   * Cấu hình mẫu trang dạng JSON
   */
  @ApiProperty({
    description: 'Cấu hình mẫu trang dạng JSON',
    example: {
      formId: 'contact-form-template',
      title: 'Liên hệ với chúng tôi',
      subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
      groups: [],
    },
  })
  config: GenericFormConfig;

  /**
   * Thời điểm tạo mẫu trang
   * @example 1672918200000
   */
  @ApiProperty({
    description: 'Thời điểm tạo mẫu trang (Unix timestamp)',
    example: 1672918200000,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật mẫu trang gần nhất
   * @example 1672918200000
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật mẫu trang gần nhất (Unix timestamp)',
    example: 1672918200000,
  })
  updatedAt: number;

  /**
   * ID của người tạo mẫu trang
   * @example "admin-user-id"
   */
  @ApiProperty({
    description: 'ID của người tạo mẫu trang',
    example: 'admin-user-id',
  })
  createdBy: string;

  /**
   * ID của người cập nhật mẫu trang gần nhất
   * @example "admin-user-id"
   */
  @ApiProperty({
    description: 'ID của người cập nhật mẫu trang gần nhất',
    example: 'admin-user-id',
  })
  updatedBy: string;
}
