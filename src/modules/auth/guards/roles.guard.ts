import {
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { JwtUtilService } from './jwt.util';
import { RedisService } from '@shared/services/redis.service';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private readonly jwtUtilService: JwtUtilService,
    private readonly redisService: RedisService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      // Ném lỗi nếu không có token
      throw new AppException(ErrorCode.TOKEN_NOT_FOUND);
    }

    // Kiểm tra xem token có trong blacklist không
    const blacklistKey = `token_blacklist:${token}`;
    const isBlacklisted = await this.redisService.exists(blacklistKey);

    if (isBlacklisted) {
      throw new AppException(
        ErrorCode.TOKEN_INVALID_OR_EXPIRED,
        'Token đã bị vô hiệu hóa. Vui lòng đăng nhập lại.'
      );
    }

    try {
      // Xác thực token
      const payload = this.jwtUtilService.verifyTokenUser(token);

      // Chuẩn hóa payload trước khi gắn vào request
      // Gắn payload đã chuẩn hóa vào request
      request['user'] = {
        // Đảm bảo trường id luôn tồn tại, sử dụng sub nếu có
        id: payload.id || payload.sub,
        // Giữ lại các trường khác của payload
        ...payload,
      }; // Sử dụng request['user'] hoặc định nghĩa interface mở rộng cho Request

      return true; // Cho phép request đi tiếp
    } catch (error) {
      console.error('AuthGuard: Invalid token', error.message);
      // Ném lỗi nếu token không hợp lệ hoặc hết hạn
      throw new AppException(ErrorCode.TOKEN_INVALID_OR_EXPIRED, 'Token không hợp lệ hoặc đã hết hạn');
    }
  }

  // Hàm helper để tách token từ header Authorization
  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
