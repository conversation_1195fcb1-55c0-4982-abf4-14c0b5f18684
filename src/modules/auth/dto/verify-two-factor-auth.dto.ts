import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, Length } from 'class-validator';
import { AuthMethodEnum } from '@/modules/user/enums/auth-method.enum';

/**
 * DTO cho việc xác thực hai lớp
 */
export class VerifyTwoFactorAuthDto {
  @ApiProperty({
    description: 'Token xác thực 2FA',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Mã OTP hoặc mã từ Google Authenticator',
    example: '123456',
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 6, { message: 'Mã OTP phải có 6 ký tự' })
  otp: string;

  @ApiProperty({
    description: '<PERSON><PERSON>ơng thức xác thực 2FA',
    enum: AuthMethodEnum,
    example: AuthMethodEnum.EMAIL,
  })
  @IsNotEmpty()
  @IsEnum(AuthMethodEnum)
  platform: AuthMethodEnum;
}

/**
 * DTO cho response của API xác thực hai lớp
 */
export class VerifyTwoFactorAuthResponseDto {
  @ApiProperty({
    description: 'Access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    example: {
      id: 1,
      email: '<EMAIL>',
      fullName: 'Nguyễn Văn A',
      username: '',
      permissions: ['user:read', 'user:write'],
      status: 'active',
    },
  })
  user: any;
}
