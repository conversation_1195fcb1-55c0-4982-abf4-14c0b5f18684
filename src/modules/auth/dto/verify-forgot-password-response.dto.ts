import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của API xác thực OTP quên mật khẩu
 */
export class VerifyForgotPasswordResponseDto {
  @ApiProperty({
    description: 'Token để đổi mật khẩu',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  changePasswordToken: string;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;
}
