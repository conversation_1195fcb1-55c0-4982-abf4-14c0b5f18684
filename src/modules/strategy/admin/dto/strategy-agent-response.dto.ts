import { ApiProperty } from '@nestjs/swagger';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

/**
 * DTO cho thông tin RankStrategy trong response
 */
export class RankStrategyInfoDto {
  @ApiProperty({
    description: 'ID của cấp bậc',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên của cấp bậc',
    example: 'C<PERSON><PERSON> bậc cao cấp',
  })
  name: string;

  @ApiProperty({
    description: 'URL hình ảnh của cấp bậc',
    example: 'https://cdn.example.com/images/rank-1.png',
  })
  imageUrl: string;
}

/**
 * DTO cho response của Strategy Agent
 */
export class StrategyAgentResponseDto {
  @ApiProperty({
    description: 'ID định danh duy nhất của chiến lược',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của chiến lượ<PERSON> xử lý',
    example: 'Strategy Marketing',
  })
  name: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về chiến lược xử lý',
    example: 'Chiến lược hướng dẫn khách hàng về sản phẩm mới',
  })
  description: string;

  @ApiProperty({
    description: 'Thông tin về cấp bậc của chiến lược',
    type: RankStrategyInfoDto,
  })
  rank: RankStrategyInfoDto;

  @ApiProperty({
    description: 'Danh sách các tag dùng để phân loại và tìm kiếm chiến lược',
    example: ['marketing', 'sales', 'product'],
    type: [String],
  })
  tags: string[];

  @ApiProperty({
    description: 'Trạng thái của chiến lược',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.APPROVED,
  })
  status: StrategyStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1682506892000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1682506892000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID của người tạo',
    example: 1,
  })
  createdBy: number;

  @ApiProperty({
    description: 'ID của người cập nhật gần nhất',
    example: 1,
  })
  updatedBy: number;
}

/**
 * DTO cho response của danh sách Strategy Agent có phân trang
 */
export class PaginatedStrategyAgentResponseDto {
  @ApiProperty({
    description: 'Danh sách các Strategy Agent',
    type: [StrategyAgentResponseDto],
  })
  items: StrategyAgentResponseDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    example: {
      totalItems: 100,
      itemCount: 10,
      itemsPerPage: 10,
      totalPages: 10,
      currentPage: 1,
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
