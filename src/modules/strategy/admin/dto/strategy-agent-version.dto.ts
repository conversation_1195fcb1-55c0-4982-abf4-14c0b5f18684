/**
 * DTO cho StrategyAgentVersion
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

/**
 * DTO tạo mới phiên bản chiến lượ<PERSON>
 */
export class CreateStrategyAgentVersionDto {
  @ApiProperty({
    description: 'ID của chiến lược',
    example: '1',
  })
  @IsNotEmpty()
  @IsString()
  strategyAgentId: string;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0',
  })
  @IsNotEmpty()
  @IsString()
  versionName: string;

  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4',
  })
  @IsNotEmpty()
  @IsString()
  modelId: string;

  @ApiProperty({
    description: 'Cấu hình model',
    example: { temperature: 0.7 },
  })
  @IsNotEmpty()
  @IsObject()
  modelConfig: Record<string, any>;

  @ApiProperty({
    description: 'System prompt',
    example: 'You are a helpful assistant',
  })
  @IsNotEmpty()
  @IsString()
  systemPrompt: string;

  @ApiProperty({
    description: 'Mô tả thay đổi',
    example: 'Initial version',
  })
  @IsNotEmpty()
  @IsString()
  changeDescription: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.APPROVED,
  })
  @IsEnum(StrategyStatusEnum)
  status: StrategyStatusEnum;
}

/**
 * DTO cập nhật phiên bản chiến lược
 */
export class UpdateStrategyAgentVersionDto {
  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.1',
  })
  @IsOptional()
  @IsString()
  versionName?: string;

  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4-turbo',
  })
  @IsOptional()
  @IsString()
  modelId?: string;

  @ApiProperty({
    description: 'Cấu hình model',
    example: { temperature: 0.8 },
  })
  @IsOptional()
  @IsObject()
  modelConfig?: Record<string, any>;

  @ApiProperty({
    description: 'System prompt',
    example: 'You are a very helpful assistant',
  })
  @IsOptional()
  @IsString()
  systemPrompt?: string;

  @ApiProperty({
    description: 'Mô tả thay đổi',
    example: 'Updated version with better prompts',
  })
  @IsOptional()
  @IsString()
  changeDescription?: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.APPROVED,
  })
  @IsOptional()
  @IsEnum(StrategyStatusEnum)
  status?: StrategyStatusEnum;
}

/**
 * DTO phản hồi phiên bản chiến lược
 */
export class StrategyAgentVersionResponseDto {
  @ApiProperty({
    description: 'ID của phiên bản',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của chiến lược',
    example: '1',
  })
  strategyAgentId: string;

  @ApiProperty({
    description: 'Số phiên bản',
    example: 1,
  })
  versionNumber: number;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0',
  })
  versionName: string;

  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4',
  })
  modelId: string;

  @ApiProperty({
    description: 'Cấu hình model',
    example: { temperature: 0.7 },
  })
  modelConfig: Record<string, any>;

  @ApiProperty({
    description: 'System prompt',
    example: 'You are a helpful assistant',
  })
  systemPrompt: string;

  @ApiProperty({
    description: 'Mô tả thay đổi',
    example: 'Initial version',
  })
  changeDescription: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.APPROVED,
  })
  status: StrategyStatusEnum;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1619433344000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1619433344000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID người tạo',
    example: 1,
  })
  createdBy: number;

  @ApiProperty({
    description: 'ID người cập nhật',
    example: 1,
  })
  updatedBy: number;
}
