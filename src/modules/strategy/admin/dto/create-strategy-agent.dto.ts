import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ConfigAgentDto, StrategyStepDto } from './create-strategy-agent-version.dto';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

export class VersionInfoDto {
  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'Phiên bản đầu tiên',
  })
  @IsNotEmpty({ message: 'Tên phiên bản không được để trống' })
  @IsString({ message: 'Tên phiên bản phải là chuỗi' })
  versionName: string;

  @ApiProperty({
    description: 'Trạng thái phiên bản',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.DRAFT,
  })
  @IsNotEmpty({ message: 'Trạng thái phiên bản không được để trống' })
  @IsEnum(StrategyStatusEnum, { message: 'Trạng thái phiên bản không hợp lệ' })
  status: StrategyStatusEnum;
}

/**
 * DTO cho việc tạo mới Strategy Agent
 */
export class CreateStrategyAgentDto {
  @ApiProperty({
    description: 'Tên của chiến lược xử lý',
    example: 'Strategy Marketing',
  })
  @IsNotEmpty({ message: 'Tên chiến lược không được để trống' })
  @IsString({ message: 'Tên chiến lược phải là chuỗi' })
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chiến lược xử lý',
    example: 'Chiến lược hướng dẫn khách hàng về sản phẩm mới',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  @ApiProperty({
    description: 'ID của cấp bậc chiến lược',
    example: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID cấp bậc phải là số' })
  @Type(() => Number)
  rankId?: number = 1;

  @ApiProperty({
    description: 'Danh sách các tag dùng để phân loại và tìm kiếm chiến lược',
    example: ['marketing', 'sales', 'product'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray({ message: 'Tags phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tag phải là chuỗi' })
  tags?: string[];

  @ApiProperty({
    description: 'Cấu hình chi tiết cho agent',
    type: ConfigAgentDto,
  })
  @IsNotEmpty({ message: 'Cấu hình không được để trống' })
  @ValidateNested()
  @Type(() => ConfigAgentDto)
  config: ConfigAgentDto;

  @ApiProperty({
    description: 'Các bước xử lý của chiến lược',
    type: [StrategyStepDto],
  })
  @IsNotEmpty({ message: 'Các bước xử lý không được để trống' })
  @IsArray({ message: 'Các bước xử lý phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => StrategyStepDto)
  steps: StrategyStepDto[];

  @ApiProperty({
    description: 'Thông tin phiên bản',
    type: VersionInfoDto,
  })
  @IsNotEmpty({ message: 'Thông tin phiên bản không được để trống' })
  @ValidateNested()
  @Type(() => VersionInfoDto)
  versionInfo: VersionInfoDto;
}
