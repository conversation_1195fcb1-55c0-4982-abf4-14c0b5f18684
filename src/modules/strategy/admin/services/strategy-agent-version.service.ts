/**
 * Service xử lý logic nghiệp vụ cho StrategyAgentVersion
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AppException } from '@common/exceptions/app.exception';
import { STRATEGY_ERROR_CODES } from '../../exceptions/strategy.error-code';

import { StrategyAgentVersion } from '../../entities/strategy-agent-version.entity';
import { StrategyAgentVersionRepository } from '../../repositories/strategy-agent-version.repository';
import { StrategyAgentRepository } from '../../repositories/strategy-agent.repository';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';
import { CreateStrategyAgentVersionDto, UpdateStrategyAgentVersionDto } from '../dto/strategy-agent-version.dto';

@Injectable()
export class StrategyAgentVersionService {
  private readonly logger = new Logger(StrategyAgentVersionService.name);

  constructor(
    @InjectRepository(StrategyAgentVersion)
    private readonly repository: Repository<StrategyAgentVersion>,
    private readonly versionRepository: StrategyAgentVersionRepository,
    private readonly strategyRepository: StrategyAgentRepository,
  ) {}

  /**
   * Lấy danh sách phiên bản của một strategy agent
   * @param strategyId ID của strategy agent
   * @returns Danh sách phiên bản
   */
  async getVersions(strategyId: string) {
    try {
      return await this.versionRepository.findByStrategyId(strategyId);
    } catch (error) {
      this.logger.error(`Failed to get versions for strategy ${strategyId}: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy chi tiết phiên bản
   * @param versionId ID của phiên bản
   * @returns Chi tiết phiên bản
   */
  async getVersionDetail(versionId: number) {
    try {
      const version = await this.versionRepository.findById(versionId);
      if (!version) {
        throw new AppException(STRATEGY_ERROR_CODES.VERSION_NOT_FOUND);
      }
      return version;
    } catch (error) {
      this.logger.error(`Failed to get version detail for ${versionId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Tạo phiên bản mới cho strategy agent
   * @param employeeId ID của nhân viên tạo
   * @param createDto Thông tin phiên bản mới
   * @returns Phiên bản đã tạo
   */
  async createVersion(employeeId: number, createDto: CreateStrategyAgentVersionDto) {
    try {
      // Kiểm tra strategy agent có tồn tại không
      const strategy = await this.strategyRepository.findById(createDto.strategyAgentId);
      if (!strategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Lấy phiên bản mới nhất để tính số phiên bản tiếp theo
      const latestVersion = await this.versionRepository.findLatestVersion(createDto.strategyAgentId);
      const versionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

      // Tạo phiên bản mới
      const newVersion = this.repository.create({
        ...createDto,
        versionNumber,
        createdBy: employeeId,
        updatedBy: employeeId,
      });

      return await this.repository.save(newVersion);
    } catch (error) {
      this.logger.error(`Failed to create version: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.CREATE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật thông tin phiên bản
   * @param versionId ID của phiên bản
   * @param employeeId ID của nhân viên cập nhật
   * @param updateDto Thông tin cập nhật
   * @returns Phiên bản đã cập nhật
   */
  async updateVersion(versionId: number, employeeId: number, updateDto: UpdateStrategyAgentVersionDto) {
    try {
      const version = await this.versionRepository.findById(versionId);
      if (!version) {
        throw new AppException(STRATEGY_ERROR_CODES.VERSION_NOT_FOUND);
      }

      const updatedVersion = {
        ...version,
        ...updateDto,
        updatedBy: employeeId,
      };

      return await this.repository.save(updatedVersion);
    } catch (error) {
      this.logger.error(`Failed to update version ${versionId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa phiên bản (xóa mềm)
   * @param versionId ID của phiên bản
   */
  async deleteVersion(versionId: number) {
    try {
      const version = await this.versionRepository.findById(versionId);
      if (!version) {
        throw new AppException(STRATEGY_ERROR_CODES.VERSION_NOT_FOUND);
      }

      // Nếu đã xóa rồi thì không cần xóa lại
      if (version.status === StrategyStatusEnum.DELETE) {
        return;
      }

      // Xóa mềm bằng cách cập nhật trạng thái
      version.status = StrategyStatusEnum.DELETE;
      await this.repository.save(version);
    } catch (error) {
      this.logger.error(`Failed to delete version ${versionId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.DELETE_FAILED, error.message);
    }
  }
}
