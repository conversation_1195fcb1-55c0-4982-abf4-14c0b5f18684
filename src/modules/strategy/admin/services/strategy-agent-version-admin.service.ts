import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { StrategyAgentVersion } from '../../entities/strategy-agent-version.entity';
import { StrategyAgentVersionRepository } from '../../repositories/strategy-agent-version.repository';
import { StrategyAgentAdminService } from './strategy-agent-admin.service';
import { CreateStrategyAgentVersionDto } from '../dto';
import { Transactional } from 'typeorm-transactional';
import { StrategyContentStep, ToolsStrategy } from '../../entities';
import { StrategyContentStepRepository, ToolsStrategyRepository } from '../../repositories';
import { AppException } from '@common/exceptions';
import { STRATEGY_ERROR_CODES } from '../../exceptions';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

@Injectable()
export class StrategyAgentVersionAdminService {
  private readonly logger = new Logger(StrategyAgentVersionAdminService.name);

  constructor(
    @InjectRepository(StrategyAgentVersion)
    private readonly strategyAgentVersionRepository: Repository<StrategyAgentVersion>,
    private readonly strategyAgentVersionCustomRepository: StrategyAgentVersionRepository,
    @InjectRepository(StrategyContentStep)
    private readonly strategyContentStepRepository: Repository<StrategyContentStep>,
    private readonly strategyContentStepCustomRepository: StrategyContentStepRepository,
    private readonly toolsStrategyRepository: ToolsStrategyRepository,
    @Inject(forwardRef(() => StrategyAgentAdminService))
    private readonly strategyAgentAdminService: StrategyAgentAdminService,
    private readonly dataSource: DataSource
  ) { }

  /**
   * Tạo mới hoặc cập nhật phiên bản Strategy
   * @param employeeId ID của nhân viên tạo/cập nhật
   * @param strategyId ID của Strategy
   * @param versionDto Thông tin phiên bản
   * @returns Thông tin phiên bản đã tạo/cập nhật
   */
  @Transactional()
  async createOrUpdateVersion(employeeId: number, strategyId: string, versionDto: CreateStrategyAgentVersionDto) {
    try {
      // Kiểm tra Strategy có tồn tại không
      await this.strategyAgentAdminService.getStrategyDetail(strategyId);

      let version: StrategyAgentVersion;
      let isNewVersion = false;

      // Nếu có ID, cập nhật phiên bản hiện có
      if (versionDto.id) {
        const existingVersion = await this.strategyAgentVersionRepository.findOne({ where: { id: versionDto.id, strategyAgentId: strategyId } });
        if (!existingVersion) {
          throw new AppException(STRATEGY_ERROR_CODES.VERSION_NOT_FOUND, `Không tìm thấy phiên bản với ID ${versionDto.id} của Strategy ${strategyId}.`);
        }
        version = existingVersion;

        // Cập nhật thông tin phiên bản
        version.versionName = versionDto.versionName;
        version.status = versionDto.status;
        version.modelId = versionDto.configAgent.modelId;
        version.modelConfig = versionDto.configAgent.modelConfig || { top_p: 1, temperature: 1 };
        version.systemPrompt = versionDto.configAgent.systemPrompt;
        version.changeDescription = versionDto.changeDescription || '';
        version.updatedBy = employeeId;

        version = await this.strategyAgentVersionRepository.save(version);

        // Xóa tất cả các liên kết công cụ cũ
        await this.toolsStrategyRepository.deleteByStrategyVersionId(version.id);
      } else {
        // Tạo phiên bản mới
        isNewVersion = true;

        // Lấy số phiên bản mới nhất
        const latestVersion = await this.strategyAgentVersionRepository.findOne({
          where: { strategyAgentId: strategyId },
          order: { versionNumber: 'DESC' }
        });

        const newVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

        version = new StrategyAgentVersion();
        version.strategyAgentId = strategyId;
        version.versionNumber = newVersionNumber;
        version.versionName = versionDto.versionName;
        version.status = versionDto.status;
        version.modelId = versionDto.configAgent.modelId;
        version.modelConfig = versionDto.configAgent.modelConfig || { top_p: 1, temperature: 1 };
        version.systemPrompt = versionDto.configAgent.systemPrompt;
        version.changeDescription = versionDto.changeDescription || 'Phiên bản mới';
        version.createdBy = employeeId;
        version.updatedBy = employeeId;

        version = await this.strategyAgentVersionRepository.save(version);
      }

      // Lưu thông tin về các công cụ được sử dụng
      if (versionDto.configAgent.functions && versionDto.configAgent.functions.length > 0) {
        const functionIds = versionDto.configAgent.functions.map(func => func.functionId);
        const versionFunctionIds = versionDto.configAgent.functions.map(func => func.versionFunctionId);

        // Kiểm tra sự tồn tại của các công cụ
        if (!await this.validateFunctions(functionIds, versionFunctionIds)) {
          throw new AppException(STRATEGY_ERROR_CODES.INVALID_FUNCTIONS, 'Một số công cụ không tồn tại');
        }

        const toolsValues = versionDto.configAgent.functions.map(func => ({
          strategyVersionId: version.id,
          toolId: func.functionId,
          versionToolId: func.versionFunctionId
        }));

        // Sử dụng repository để insert nhiều bản ghi cùng lúc
        await this.toolsStrategyRepository.createBulk(toolsValues);
      }

      // Xử lý các bước
      if (versionDto.steps && versionDto.steps.length > 0) {
        // Nếu là cập nhật, xóa các bước không còn trong danh sách
        if (!isNewVersion) {
          const stepIds = versionDto.steps
            .filter(step => step.id)
            .map(step => step.id);

          if (stepIds.length > 0) {
            // Xóa các bước không còn trong danh sách
            await this.strategyContentStepRepository.createQueryBuilder()
              .delete()
              .from(StrategyContentStep)
              .where('strategy_agent_version_id = :versionId', { versionId: version.id })
              .andWhere('id NOT IN (:...stepIds)', { stepIds })
              .execute();
          }
        }

        // Tạo hoặc cập nhật các bước
        for (const stepDto of versionDto.steps) {
          if (stepDto.id) {
            // Cập nhật bước hiện có
            await this.strategyContentStepRepository.update(
              { id: stepDto.id, strategyAgentVersionId: version.id },
              {
                stepOrder: stepDto.stepOrder,
                content: stepDto.content,
                example: stepDto.example
              }
            );
          } else {
            // Tạo bước mới
            const step = new StrategyContentStep();
            step.strategyAgentVersionId = version.id;
            step.stepOrder = stepDto.stepOrder;
            step.content = stepDto.content;
            step.example = stepDto.example;

            await this.strategyContentStepRepository.save(step);
          }
        }
      }

      return { version, isNewVersion };
    } catch (error) {
      this.logger.error(`Failed to create/update strategy version: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.VERSION_CREATE_FAILED, error.message);
    }
  }

  /**
   * Kiểm tra xem các công cụ có tồn tại trong database không
   * @param toolIds Danh sách ID của các công cụ cần kiểm tra
   * @param versionToolIds Danh sách ID của các phiên bản công cụ cần kiểm tra
   * @returns true nếu tất cả công cụ đều tồn tại, false nếu có ít nhất một công cụ không tồn tại
   */
  private async validateFunctions(toolIds: string[], versionToolIds: string[]): Promise<boolean> {
    try {
      if (toolIds.length === 0) {
        return true; // Không có công cụ nào cần kiểm tra
      }

      // Tạo tham số cho truy vấn SQL với mảng
      const toolPlaceholders = toolIds.map((_, index) => `$${index + 1}`).join(', ');

      // Kiểm tra toolIds có tồn tại trong bảng admin_tools không
      const toolQuery = `SELECT COUNT(*) as count FROM admin_tools WHERE id IN (${toolPlaceholders})`;
      const toolCount = await this.dataSource.query(toolQuery, toolIds);

      if (parseInt(toolCount[0].count) !== toolIds.length) {
        this.logger.error(`Một số công cụ không tồn tại: ${toolIds.join(', ')}`);
        return false;
      }

      // Tạo tham số cho truy vấn SQL với mảng
      const versionPlaceholders = versionToolIds.map((_, index) => `$${index + 1}`).join(', ');

      // Kiểm tra versionToolIds có tồn tại trong bảng admin_tool_versions không
      const versionQuery = `SELECT COUNT(*) as count FROM admin_tool_versions WHERE id IN (${versionPlaceholders})`;
      const versionCount = await this.dataSource.query(versionQuery, versionToolIds);

      if (parseInt(versionCount[0].count) !== versionToolIds.length) {
        this.logger.error(`Một số phiên bản công cụ không tồn tại: ${versionToolIds.join(', ')}`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra công cụ: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy chi tiết phiên bản Strategy
   * @param strategyId ID của Strategy
   * @param versionId ID của phiên bản
   * @returns Thông tin chi tiết phiên bản và các bước
   */
  async getVersionDetail(strategyId: string, versionId: number) {
    try {
      // Kiểm tra Strategy có tồn tại không
      await this.strategyAgentAdminService.getStrategyDetail(strategyId);

      // Lấy thông tin phiên bản
      const version = await this.strategyAgentVersionCustomRepository.findById(versionId);
      if (!version || version.strategyAgentId !== strategyId) {
        throw new AppException(STRATEGY_ERROR_CODES.VERSION_NOT_FOUND, `Không tìm thấy phiên bản với ID ${versionId} của Strategy ${strategyId}.`);
      }

      // Lấy các bước của phiên bản
      const steps = await this.strategyContentStepRepository.find({
        where: { strategyAgentVersionId: versionId },
        order: { stepOrder: 'ASC' }
      });

      // Trả về kết quả
      return {
        ...version,
        steps
      };
    } catch (error) {
      this.logger.error(`Failed to get strategy version detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }
}
