/**
 * Service xử lý logic nghiệp vụ cho UserStrategyAgent
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AppException } from '@common/exceptions/app.exception';
import { STRATEGY_ERROR_CODES } from '../../exceptions/strategy.error-code';

import { UserStrategyAgent } from '../../../agent/entities/user-strategy-agent.entity';
import { UserStrategyAgentRepository } from '../../repositories/user-strategy-agent.repository';
import { StrategyAgentRepository } from '../../repositories/strategy-agent.repository';
import { QueryDto } from '@common/dto/query.dto';

@Injectable()
export class UserStrategyAgentService {
  private readonly logger = new Logger(UserStrategyAgentService.name);

  constructor(
    @InjectRepository(UserStrategyAgent)
    private readonly repository: Repository<UserStrategyAgent>,
    private readonly userStrategyRepository: UserStrategyAgentRepository,
    private readonly strategyRepository: StrategyAgentRepository,
  ) {}

  /**
   * Lấy danh sách strategy agent của user
   * @param userId ID của user
   * @returns Danh sách strategy agent
   */
  async getUserStrategies(userId: number) {
    try {
      return await this.userStrategyRepository.findByUserId(userId);
    } catch (error) {
      this.logger.error(`Failed to get strategies for user ${userId}: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Gán strategy agent cho user
   * @param userId ID của user
   * @param strategyId ID của strategy agent
   * @returns Thông tin gán strategy
   */
  async assignStrategyToUser(userId: number, strategyId: string) {
    try {
      // Kiểm tra strategy agent có tồn tại không
      const strategy = await this.strategyRepository.findById(strategyId);
      if (!strategy) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Kiểm tra user đã được gán strategy này chưa
      const existingAssignment = await this.userStrategyRepository.findByUserIdAndStrategyId(userId, strategyId);
      if (existingAssignment) {
        return existingAssignment;
      }

      // Tạo gán mới
      const newAssignment = this.repository.create({
        userId,
        strategyAgentId: strategyId,
      });

      return await this.repository.save(newAssignment);
    } catch (error) {
      this.logger.error(`Failed to assign strategy ${strategyId} to user ${userId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.ASSIGN_FAILED, error.message);
    }
  }

  /**
   * Gỡ strategy agent khỏi user
   * @param userId ID của user
   * @param strategyId ID của strategy agent
   */
  async removeStrategyFromUser(userId: number, strategyId: string) {
    try {
      // Kiểm tra user có được gán strategy này không
      const assignment = await this.userStrategyRepository.findByUserIdAndStrategyId(userId, strategyId);
      if (!assignment) {
        throw new AppException(STRATEGY_ERROR_CODES.ASSIGNMENT_NOT_FOUND);
      }

      // Xóa gán
      await this.repository.delete(assignment.id);
    } catch (error) {
      this.logger.error(`Failed to remove strategy ${strategyId} from user ${userId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.REMOVE_FAILED, error.message);
    }
  }

  /**
   * Kiểm tra user có quyền truy cập strategy không
   * @param userId ID của user
   * @param strategyId ID của strategy agent
   * @returns true nếu có quyền, false nếu không
   */
  async checkUserHasAccess(userId: number, strategyId: string): Promise<boolean> {
    try {
      const assignment = await this.userStrategyRepository.findByUserIdAndStrategyId(userId, strategyId);
      return !!assignment;
    } catch (error) {
      this.logger.error(`Failed to check access for user ${userId} to strategy ${strategyId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy danh sách user có quyền truy cập strategy
   * @param strategyId ID của strategy agent
   * @param queryDto Thông tin phân trang
   * @returns Danh sách user có quyền truy cập
   */
  async getUsersWithAccess(strategyId: string, queryDto: QueryDto) {
    try {
      return await this.userStrategyRepository.findPaginated({
        ...queryDto,
        strategyAgentId: strategyId,
      });
    } catch (error) {
      this.logger.error(`Failed to get users with access to strategy ${strategyId}: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }
}
