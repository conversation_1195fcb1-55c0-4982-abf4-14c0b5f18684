import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { STRATEGY_ERROR_CODES } from '../../exceptions';
import { QueryUserStrategyDto } from '../dto';
import { UserStrategyAgentRepository } from '../../repositories';

@Injectable()
export class StrategyAgentUserService {
  private readonly logger = new Logger(StrategyAgentUserService.name);

  constructor(
    private readonly userStrategyAgentRepository: UserStrategyAgentRepository,
  ) { }

  /**
   * L<PERSON>y danh sách Strategy đã mua của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Strategy đã mua với phân trang
   */
  async getUserStrategies(userId: number, queryDto: QueryUserStrategyDto): Promise<PaginatedResult<any>> {
    try {
      // Log để debug
      this.logger.log(`getUserStrategies raw queryDto: ${JSON.stringify(queryDto)}`);

      // Bỏ qua điều kiện lọc theo trạng thái vì bảng không có cột active
      if (queryDto.isActive !== undefined) {
        this.logger.log(`Bỏ qua lọc theo isActive vì bảng không có cột active. Giá trị: ${queryDto.isActive}, type: ${typeof queryDto.isActive}`);
      }

      // Sử dụng repository để lấy dữ liệu
      return await this.userStrategyAgentRepository.getUserStrategies(userId, queryDto);
    } catch (error) {
      this.logger.error(`Failed to get user strategies: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy chi tiết Strategy theo ID
   * @param userId ID của người dùng
   * @param strategyId ID của Strategy
   * @returns Thông tin chi tiết Strategy
   */
  async getStrategyDetail(userId: number, strategyId: string) {
    try {
      // Kiểm tra quyền truy cập
      const userStrategy = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(userId, strategyId);
      if (!userStrategy) {
        throw new AppException(STRATEGY_ERROR_CODES.ACCESS_DENIED, `Bạn không có quyền truy cập Strategy với ID ${strategyId}.`);
      }

      // Lấy thông tin Strategy từ database
      const strategyInfo = await this.userStrategyAgentRepository.getStrategyDetail(userId, strategyId);

      // Lấy các agent đã liên kết với Strategy này
      const linkedAgents: any[] = []; // TODO: Implement this

      // Lấy phiên bản hiện tại
      interface VersionInfo {
        versionId: number;
        versionName: string;
      }
      const currentVersion: VersionInfo[] = []; // TODO: Implement this

      return {
        id: strategyId,
        name: strategyInfo?.name || '',
        description: strategyInfo?.description || '',
        rankId: strategyInfo?.rankId || null,
        rankName: strategyInfo?.rankName || '',
        tags: strategyInfo?.tags || [],
        purchaseDate: userStrategy.purchaseDate,
        status: userStrategy.status,
        currentVersionId: currentVersion.length > 0 ? currentVersion[0].versionId : null,
        currentVersionName: currentVersion.length > 0 ? currentVersion[0].versionName : null,
        linkedAgents
      };
    } catch (error) {
      this.logger.error(`Failed to get strategy detail: ${error.message}`);
      throw error;
    }
  }

  /**
   * Gỡ Strategy khỏi Agent
   * @param userId ID của người dùng
   * @param agentId ID của Agent
   * @param strategyId ID của Strategy
   */
  @Transactional()
  async removeStrategyFromAgent(userId: number, agentId: string, strategyId: string) {
    try {
      // Kiểm tra người dùng có quyền truy cập Strategy này không
      const userStrategy = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(userId, strategyId);

      if (!userStrategy) {
        throw new AppException(STRATEGY_ERROR_CODES.ACCESS_DENIED, `Không tìm thấy Strategy với ID ${strategyId} hoặc bạn không có quyền truy cập.`);
      }

      // Xóa liên kết giữa Agent và Strategy
      const result = await this.userStrategyAgentRepository.query(`
        DELETE FROM user_strategy_version_contents
        WHERE agent_id = $1
      `, [agentId]);

      if (result.affectedRows === 0) {
        throw new AppException(STRATEGY_ERROR_CODES.AGENT_STRATEGY_LINK_NOT_FOUND, `Không tìm thấy liên kết giữa Agent và Strategy.`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove strategy from agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.REMOVE_FROM_AGENT_FAILED, error.message);
    }
  }

  /**
   * Gỡ Strategy khỏi tất cả Agent đang sử dụng
   * @param userId ID của người dùng
   * @param strategyId ID của Strategy
   */
  @Transactional()
  async removeStrategyFromAllAgents(userId: number, strategyId: string) {
    try {
      // Kiểm tra người dùng có quyền truy cập Strategy này không
      const userStrategy = await this.userStrategyAgentRepository.findByUserIdAndStrategyId(userId, strategyId);

      if (!userStrategy) {
        throw new AppException(STRATEGY_ERROR_CODES.ACCESS_DENIED, `Không tìm thấy Strategy với ID ${strategyId} hoặc bạn không có quyền truy cập.`);
      }

      // Xóa tất cả liên kết giữa Strategy này và các Agent
      // Lấy danh sách các agent đã liên kết với strategy này
      const agents = await this.userStrategyAgentRepository.query(`
        SELECT DISTINCT agent_id FROM user_strategy_version_contents
        WHERE strategy_version_id IN (
          SELECT id FROM strategy_agent_versions
          WHERE strategy_agent_id = $1
        )
      `, [strategyId]);

      // Nếu không có agent nào, không cần xóa
      if (!agents || agents.length === 0) {
        this.logger.log(`No agents linked to strategy ${strategyId} for user ${userId}`);
        return;
      }

      // Xóa tất cả liên kết
      const agentIds = agents.map((a: any) => a.agent_id);
      const result = await this.userStrategyAgentRepository.query(`
        DELETE FROM user_strategy_version_contents
        WHERE agent_id IN (${agentIds.map((_: any, i: number) => `$${i + 1}`).join(',')})
      `, agentIds);

      this.logger.log(`Removed strategy ${strategyId} from all agents for user ${userId}. Affected rows: ${result.rowCount || 0}`);
    } catch (error) {
      this.logger.error(`Failed to remove strategy from all agents: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.REMOVE_FROM_AGENT_FAILED, error.message);
    }
  }
}
