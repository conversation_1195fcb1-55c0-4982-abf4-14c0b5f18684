import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { StrategyAgentVersion } from '../../entities/strategy-agent-version.entity';
import { StrategyAgentVersionRepository } from '../../repositories/strategy-agent-version.repository';
import { UserStrategyAgentRepository } from '../../repositories/user-strategy-agent.repository';
import { StrategyAgentUserService } from './strategy-agent-user.service';

@Injectable()
export class StrategyAgentVersionUserService {
  constructor(
    @InjectRepository(StrategyAgentVersion)
    private readonly strategyAgentVersionRepository: Repository<StrategyAgentVersion>,
    private readonly strategyAgentVersionCustomRepository: StrategyAgentVersionRepository,
    private readonly userStrategyAgentCustomRepository: UserStrategyAgentRepository,
    private readonly strategyAgentUserService: StrategyAgentUserService
  ) { }

}
