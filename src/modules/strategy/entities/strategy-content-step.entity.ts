import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng strategy_content_steps trong cơ sở dữ liệu
 * Bảng lưu trữ các bước nội dung của phiên bản chiến lư<PERSON><PERSON> ch<PERSON>h thức, bao gồm cả nội dung ẩn và nội dung có thể chỉnh sửa
 */
@Entity('strategy_content_steps')
@Unique(['strategyAgentVersionId', 'stepOrder'])
export class StrategyContentStep {
  /**
   * ID định danh duy nhất cho mỗi bước nội dung, tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của phiên bản chiến lược agent mà bước này thuộc về, tham chiếu đến bảng strategy_agent_versions
   */
  @Column({ name: 'strategy_agent_version_id' })
  strategyAgentVersionId: number;

  /**
   * Thứ tự của bước trong chuỗi xử lý
   */
  @Column({ name: 'step_order' })
  stepOrder: number;

  /**
   * Nội dung ẩn của bước, không hiển thị cho người dùng nhưng vẫn được sử dụng trong xử lý
   */
  @Column({ name: 'content', type: 'text' })
  content: string;

  /**
   * Nội dung có thể chỉnh sửa của bước, người dùng có thể tùy chỉnh
   */
  @Column({ name: 'example', type: 'text' })
  example: string;
}
