# Strategy Module

## Overview

The Strategy module provides functionality for managing agent strategies in the system. It supports different versions of strategies and allows users to purchase and use these strategies with their agents.

## Database Structure

The module uses the following database tables:

- `strategy_agents`: Main table for all strategy agents in the system
- `strategy_agent_versions`: Versions of strategy agents with different configurations
- `user_strategy_agents`: User access rights to strategy agent versions

## Module Structure

The module is organized into the following components:

### Entities

- `StrategyAgent`: Main entity representing a strategy agent
- `StrategyAgentVersion`: Entity for versions of strategy agents
- `UserStrategyAgent`: Entity for user access rights to strategy agent versions

### DTOs

- `CreateStrategyAgentDto`: DTO for creating a new strategy agent
- `UpdateStrategyAgentDto`: DTO for updating an existing strategy agent
- `CreateStrategyAgentVersionDto`: DTO for creating a new strategy agent version
- `UpdateStrategyAgentVersionDto`: DTO for updating an existing strategy agent version
- `CreateUserStrategyAgentDto`: DTO for creating user access rights
- `UpdateUserStrategyAgentDto`: DTO for updating user access rights

### Repositories

- `StrategyAgentRepository`: Custom repository for strategy agents
- `StrategyAgentVersionRepository`: Custom repository for strategy agent versions
- `UserStrategyAgentRepository`: Custom repository for user access rights

### Services

#### Admin Services

- `StrategyAgentAdminService`: Service for admin operations on strategy agents
- `StrategyAgentVersionAdminService`: Service for admin operations on strategy agent versions
- `UserStrategyAgentAdminService`: Service for admin operations on user access rights

#### User Services

- `StrategyAgentUserService`: Service for user operations on strategy agents
- `StrategyAgentVersionUserService`: Service for user operations on strategy agent versions
- `UserStrategyAgentUserService`: Service for user operations on user access rights

### Controllers

#### Admin Controllers

- `StrategyAgentAdminController`: Controller for admin operations on strategy agents
- `StrategyAgentVersionAdminController`: Controller for admin operations on strategy agent versions
- `UserStrategyAgentAdminController`: Controller for admin operations on user access rights

#### User Controllers

- `StrategyAgentUserController`: Controller for user operations on strategy agents
- `StrategyAgentVersionUserController`: Controller for user operations on strategy agent versions
- `UserStrategyAgentUserController`: Controller for user operations on user access rights

## Usage

### Creating a Strategy Agent (Admin)

Administrators can create and manage strategy agents:

```typescript
const createStrategyAgentDto = {
  name: 'Customer Support Strategy',
  description: 'A strategy for handling customer support requests efficiently',
  tags: ['customer-support', 'quick-response', 'problem-solving'],
  createdBy: 1,
  updatedBy: 1
};

// POST to /admin/strategy-agents
```

### Creating a Strategy Agent Version (Admin)

Administrators can create versions of strategy agents:

```typescript
const createStrategyAgentVersionDto = {
  strategyAgentId: 1,
  versionNumber: 1,
  changeNotes: 'Initial version',
  config: {
    priorityHandling: {
      urgent: 'immediate',
      high: 'within-1-hour',
      medium: 'within-24-hours',
      low: 'within-48-hours'
    },
    responseTemplates: {
      greeting: 'Xin chào, tôi có thể giúp gì cho bạn?',
      farewell: 'Cảm ơn bạn đã liên hệ với chúng tôi!'
    },
    languageSupport: ['vi', 'en', 'fr']
  },
  createdBy: 1,
  status: 'draft',
  price: 99.99
};

// POST to /admin/strategy-agent-versions
```

### Purchasing a Strategy Agent (User)

Users can purchase strategy agents:

```typescript
// POST to /user/user-strategy-agents/user/{userId}/strategy-agent/{strategyAgentId}/version/{versionId}
```

### Checking User Access to Strategy Agent

Users can check if they have access to a strategy agent:

```typescript
// GET to /user/user-strategy-agents/check-access/user/{userId}/strategy-agent/{strategyAgentId}
```

## API Endpoints

See the Swagger documentation for a complete list of API endpoints.
