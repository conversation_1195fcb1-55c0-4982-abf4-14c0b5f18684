import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

/**
 * Entity đại diện cho bảng two_factor_auth trong cơ sở dữ liệu
 * Bảng lưu cài đặt xác thực hai yếu tố của người dùng
 */
@Entity('two_factor_auth')
export class TwoFactorAuth {
    /**
     * ID tự tăng
     */
    @PrimaryGeneratedColumn({ name: 'id' })
    id: number;

    /**
     * ID của người dùng
     */
    @Column({ name: 'user_id', unique: true, comment: 'mã tài khoản' })
    userId: number;

    /**
     * Bật/tắt xác thực SMS
     */
    @Column({ name: 'otp_sms_enabled', type: 'boolean', default: false, comment: 'xác thực sms' })
    otpSmsEnabled: boolean;

    /**
     * Bật/tắt xác thực email
     */
    @Column({ name: 'otp_email_enabled', type: 'boolean', default: false, nullable: false, comment: 'xác thực email' })
    otpEmailEnabled: boolean;

    /**
     * Bật/tắt xác thực Google Authenticator
     */
    @Column({ name: 'google_authenticator_enabled', type: 'boolean', default: false, nullable: false, comment: 'xác thực google authenticator' })
    googleAuthenticatorEnabled: boolean;

    /**
     * Khóa bí mật Google Authenticator
     */
    @Column({ name: 'google_authenticator_secret', length: 255, nullable: true, comment: 'khóa bí mật google authenticator' })
    googleAuthenticatorSecret: string;

    /**
     * Đã xác nhận Google Authenticator
     */
    @Column({ name: 'is_google_authenticator_confirmed', type: 'boolean', default: false, nullable: false, comment: 'confirm google authenticator' })
    isGoogleAuthenticatorConfirmed: boolean;

    /**
     * Thời gian tạo
     */
    @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'thời gian tạo' })
    createdAt: number;

    /**
     * Thời gian cập nhật
     */
    @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'thời gian cập nhật' })
    updatedAt: number;

    /**
     * Quan hệ với bảng users
     */
    // @OneToOne(() => User, user => user.twoFactorAuth)
    // @JoinColumn({ name: 'user_id' })
    // user: User;
}