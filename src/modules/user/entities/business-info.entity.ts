import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';
import { BusinessStatusEnum } from '../enums/business-status.enum';

/**
 * Entity đại diện cho bảng business_info trong cơ sở dữ liệu
 * Bảng lưu thông tin doanh nghiệp của người dùng
 */
@Entity('business_info')
export class BusinessInfo {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', unique: true, comment: 'mã tài khoản' })
  userId: number;

  /**
   * Tên doanh nghiệp
   */
  @Column({ name: 'business_name', length: 255, nullable: false, comment: 'tên công ty' })
  businessName: string;

  /**
   * Email doanh nghiệp
   */
  @Column({ name: 'business_email', length: 100, nullable: false, comment: 'email công ty' })
  businessEmail: string;

  /**
   * Số điện thoại doanh nghiệp
   */
  @Column({ name: 'business_phone', length: 20, nullable: false, comment: 'số điện thoại công ty' })
  businessPhone: string;

  /**
   * URL giấy phép kinh doanh
   */
  @Column({ name: 'business_registration_certificate', length: 255, nullable: true, comment: 'url giấy phép kinh doanh' })
  businessRegistrationCertificate: string;

  /**
   * Mã số thuế
   */
  @Column({ name: 'tax_code', length: 20, nullable: false, comment: 'mã số thuế' })
  taxCode: string;
  
  /**
   * Địa chỉ công ty
   */
  @Column({ name: 'business_address', length: 1000, nullable: false, comment: 'địa chỉ công ty' })
  businessAddress: string;

  /**
   * Tên người đại diện
   */
  @Column({ name: 'representative_name', length: 100, nullable: true, comment: 'họ tên người đại diện' })
  representativeName: string;

  /**
   * Vị trí người đại diện
   */
  @Column({ name: 'representative_position', length: 255, nullable: true, comment: 'vị trí người đại diện' })
  representativePosition: string;

  /**
   * Trạng thái xác thực
   */
  @Column({ name: 'status', length: 20, nullable: false, comment: 'trạng thái xác thực' })
  status: string;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'thời gian chỉnh sửa' })
  updatedAt: number;
}
