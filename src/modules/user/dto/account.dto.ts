export interface BankInfoResponse {
  id: number;
  bankCode: string;
  accountNumber: string;
  accountHolder: string;
  bankBranch?: string;
}

export interface BusinessInfoResponse {
  id: number;
  businessName: string;
  businessEmail: string;
  businessPhone: string;
  businessAddress: string;
  businessRegistrationCertificate: string;
  taxCode: string;
  representativePosition: string;
  representativeName: string;
  status: string;
}
