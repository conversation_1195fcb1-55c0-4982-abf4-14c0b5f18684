import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

/**
 * DTO chứa thông tin cài đặt thông báo của user
 */
export class UserNotificationResponseDto {
  @ApiProperty({
    description: 'ID của cài đặt thông báo',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của user',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: 'Nhận email thông báo hệ thống',
    example: true,
  })
  receiveAccountSystemEmails: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo thanh toán',
    example: true,
  })
  receiveBillingEmails: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tính năng mới',
    example: true,
  })
  receiveNewFeatureEmails: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tiếp thị liên kết',
    example: true,
  })
  receiveAffiliateEmails: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tài liệu',
    example: true,
    nullable: true,
  })
  receiveDocumentationEmails: boolean;

  @ApiProperty({
    description: 'Nhận email quảng cáo',
    example: true,
  })
  receivePromotionalEmails: boolean;
}

/**
 * DTO chứa thông tin cập nhật cài đặt thông báo của user
 */
export class UpdateNotificationSettingsDto {
  @ApiProperty({
    description: 'Nhận email thông báo hệ thống',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  receiveAccountSystemEmails?: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo thanh toán',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  receiveBillingEmails?: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tính năng mới',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  receiveNewFeatureEmails?: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tiếp thị liên kết',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  receiveAffiliateEmails?: boolean;

  @ApiProperty({
    description: 'Nhận email thông báo tài liệu',
    example: true,
    required: false,
    nullable: true,
  })
  @IsBoolean()
  @IsOptional()
  receiveDocumentationEmails?: boolean;

  @ApiProperty({
    description: 'Nhận email quảng cáo',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  receivePromotionalEmails?: boolean;
} 