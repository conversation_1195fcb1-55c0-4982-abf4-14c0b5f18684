import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthVerificationLog } from '../entities/auth-verification-log.entity';
import { AuthMethodEnum } from '../enums/auth-method.enum';
import { AuthStatusEnum } from '../enums/auth-status.enum';

/**
 * Repository cho AuthVerificationLog
 */
@Injectable()
export class AuthVerificationLogRepository {
  constructor(
    @InjectRepository(AuthVerificationLog)
    private readonly repository: Repository<AuthVerificationLog>,
  ) {}

  /**
   * Tạo log xác thực mới
   * @param data Dữ liệu log xác thực
   * @returns Log xác thực đã tạo
   */
  async create(data: Partial<AuthVerificationLog>): Promise<AuthVerificationLog> {
    const log = this.repository.create(data);
    return this.repository.save(log);
  }

  /**
   * Tạo log xác thực đăng nhập thành công
   * @param userId ID của người dùng
   * @param authMethod Phương thức xác thực
   * @param ipAddress Địa chỉ IP
   * @param userAgent User agent
   * @returns Log xác thực đã tạo
   */
  async createLoginSuccessLog(
    userId: number,
    authMethod: AuthMethodEnum,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AuthVerificationLog> {
    const now = Date.now();
    return this.create({
      userId,
      authMethod,
      status: AuthStatusEnum.SUCCESS,
      ipAddress,
      userAgent,
      verifiedAt: now,
      createdAt: now,
    });
  }

  /**
   * Tạo log xác thực đăng nhập thất bại
   * @param userId ID của người dùng (nếu có)
   * @param authMethod Phương thức xác thực
   * @param ipAddress Địa chỉ IP
   * @param userAgent User agent
   * @returns Log xác thực đã tạo
   */
  async createLoginFailedLog(
    userId: number | null,
    authMethod: AuthMethodEnum,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<AuthVerificationLog> {
    const now = Date.now();
    return this.create({
      userId: userId === null ? undefined : userId,
      authMethod,
      status: AuthStatusEnum.FAILED,
      ipAddress,
      userAgent,
      createdAt: now,
    });
  }

  /**
   * Tìm log xác thực theo ID
   * @param id ID của log xác thực
   * @returns Log xác thực
   */
  async findById(id: number): Promise<AuthVerificationLog | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm log xác thực theo ID người dùng
   * @param userId ID của người dùng
   * @returns Danh sách log xác thực
   */
  async findByUserId(userId: number): Promise<AuthVerificationLog[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }
}
