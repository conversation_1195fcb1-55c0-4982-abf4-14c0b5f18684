import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc mở khóa người dùng
 */
export class UnblockUserDto {
  /**
   * Lý do mở khóa người dùng
   */
  @ApiProperty({
    description: 'Lý do mở khóa người dùng',
    example: 'Người dùng đã khắc phục vi phạm',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  reason: string;

  /**
   * Thông tin bổ sung (tùy chọn)
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { details: 'Đã xác minh danh tính', approvedBy: '<EMAIL>' },
    required: false
  })
  @IsOptional()
  info?: Record<string, any>;
}
