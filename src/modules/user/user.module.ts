import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user/controller/user.controller';
import { UserRoleController } from './admin/controller/user-role.controller';
import { UserAdminController } from './admin/controller/user-admin.controller';
import { BankController } from './user/controller/bank.controller';
import { UserBankController } from './user/controller/user-bank.controller';
import { BankService } from './user/service/bank.service';
import { PasswordService } from './share/password.share.service';
import { User } from './entities/user.entity';
import {
  UserManageNotification,
  BusinessInfo,
  TwoFactorAuth,
  AuthVerificationLog,
  UserRole,
  DeviceInfo,
  Bank,
  UserStatusLog,
  UserSettings,
} from '@modules/user/entities';
import { UserHasRole } from './entities/user-has-role.entity';
import { Permission } from './entities/permission.entity';
import { UserRoleHasPermission } from './entities/user-role-has-permission.entity';
import { UserHasRoleRepository, UserRoleRepository, UserRepository, TwoFactorAuthRepository, UserStatusLogRepository } from './repositories';
import { BankRepository } from './repositories/bank.repository';
import { AuthVerificationLogRepository } from './repositories/auth-verification-log.repository';
import { DeviceInfoRepository } from './repositories/device-info.repository';
import { PermissionService } from './user/service/permission.service';
import { AccountService, TwoFactorAuthService, UserRoleService, UserService, UserSettingsService } from './user/service';
import { UserAdminService } from './admin/services';
import { AccountController, UserSettingsController } from './user/controller';
import { ServicesModule } from '@shared/services/services.module';
import { UserSettingsRepository } from './user/repository';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserManageNotification,
      BusinessInfo,
      TwoFactorAuth,
      AuthVerificationLog,
      UserRole,
      DeviceInfo,
      UserHasRole,
      Permission,
      UserRoleHasPermission,
      Bank,
      UserStatusLog,
      UserSettings,
    ]),
    ServicesModule,
  ],
  controllers: [
    UserController,
    UserRoleController,
    UserAdminController,
    BankController,
    UserBankController,
    AccountController,
    UserSettingsController,
  ],
  providers: [
    UserService,
    PermissionService,
    UserRoleService,
    UserAdminService,
    PasswordService,
    UserHasRoleRepository,
    UserRoleRepository,
    UserRepository,
    BankRepository,
    BankService,
    AccountService,
    TwoFactorAuthRepository,
    TwoFactorAuthService,
    AuthVerificationLogRepository,
    DeviceInfoRepository,
    UserStatusLogRepository,
    UserSettingsService,
    UserSettingsRepository,
  ],
  exports: [
    UserService,
    PermissionService,
    UserRoleService,
    UserAdminService,
    PasswordService,
    UserHasRoleRepository,
    UserRoleRepository,
    UserRepository,
    BankRepository,
    BankService,
    AccountService,
    TwoFactorAuthRepository,
    TwoFactorAuthService,
    AuthVerificationLogRepository,
    DeviceInfoRepository,
    UserStatusLogRepository,
    UserSettingsService,
    UserSettingsRepository,
  ],
})
export class UserModule {}
