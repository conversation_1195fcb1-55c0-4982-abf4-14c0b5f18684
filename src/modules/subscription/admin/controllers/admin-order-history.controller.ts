import { <PERSON>, Get, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AdminOrderHistoryService } from '../services';
import { AdminOrderHistoryFilterDto } from '../dto';
import { OrderPlanHistory } from '@modules/subscription/entities';

@ApiTags(SWAGGER_API_TAGS.ADMIN_SUBSCRIPTION_ORDER)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/subscription/order-histories')
export class AdminOrderHistoryController {
  constructor(private readonly adminOrderHistoryService: AdminOrderHistoryService) {}

  /**
   * L<PERSON>y danh sách lịch sử đơn hàng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách lịch sử đơn hàng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách lịch sử đơn hàng thành công'
  })
  async findOrderHistories(
    @Query() filterDto: AdminOrderHistoryFilterDto
  ): Promise<ApiResponseDto<PaginatedResult<OrderPlanHistory>>> {
    const orderHistories = await this.adminOrderHistoryService.findOrderHistories(filterDto);
    return ApiResponseDto.success(orderHistories, 'Lấy danh sách lịch sử đơn hàng thành công');
  }

  /**
   * Lấy thông tin chi tiết lịch sử đơn hàng
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết lịch sử đơn hàng' })
  @ApiParam({ name: 'id', description: 'ID của lịch sử đơn hàng' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết lịch sử đơn hàng thành công'
  })
  async findOrderHistoryById(
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<OrderPlanHistory>> {
    const orderHistory = await this.adminOrderHistoryService.findOrderHistoryById(id);
    return ApiResponseDto.success(orderHistory, 'Lấy thông tin chi tiết lịch sử đơn hàng thành công');
  }

  /**
   * Lấy thống kê doanh thu
   */
  @Get('stats/revenue')
  @ApiOperation({ summary: 'Lấy thống kê doanh thu' })
  @ApiQuery({
    name: 'startDate',
    description: 'Thời gian bắt đầu (Unix timestamp)',
    required: false,
    type: 'number'
  })
  @ApiQuery({
    name: 'endDate',
    description: 'Thời gian kết thúc (Unix timestamp)',
    required: false,
    type: 'number'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê doanh thu thành công'
  })
  async getRevenueStats(
    @Query('startDate') startDate?: number,
    @Query('endDate') endDate?: number
  ): Promise<ApiResponseDto<{
    totalRevenue: number;
    totalOrders: number;
    completedOrders: number;
    averageOrderValue: number;
  }>> {
    const stats = await this.adminOrderHistoryService.getRevenueStats(startDate, endDate);
    return ApiResponseDto.success(stats, 'Lấy thống kê doanh thu thành công');
  }
}
