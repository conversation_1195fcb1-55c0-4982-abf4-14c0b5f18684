import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { PlanPricing, PlanPricingHasRole } from '@modules/subscription/entities';
import { PlanPricingRepository } from '@modules/subscription/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { QueryDto, SortDirection } from '@common/dto';
import { AdminPlanPricingFilterDto, CreatePlanPricingDto, UpdatePlanPricingDto } from '../dto';
import { AdminPlanService } from './admin-plan.service';
import { UserRole } from '@modules/user/entities';

@Injectable()
export class AdminPlanPricingService {
  private readonly logger = new Logger(AdminPlanPricingService.name);

  constructor(
    @InjectRepository(PlanPricing)
    private readonly planPricingRepository: Repository<PlanPricing>,
    @InjectRepository(PlanPricingHasRole)
    private readonly planPricingHasRoleRepository: Repository<PlanPricingHasRole>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    private readonly planPricingCustomRepository: PlanPricingRepository,
    private readonly adminPlanService: AdminPlanService,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Lấy danh sách tùy chọn giá với bộ lọc
   * @param filterDto Bộ lọc
   * @returns Danh sách tùy chọn giá đã phân trang
   */
  async findPlanPricings(filterDto: AdminPlanPricingFilterDto): Promise<PaginatedResult<PlanPricing>> {
    try {
      const { planId, billingCycle, isActive, page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = filterDto;

      // Tạo tham số phân trang
      const paginationParams: QueryDto = {
        page,
        limit,
        sortBy,
        sortDirection
      };

      // Tạo điều kiện tìm kiếm
      const where: any = {};

      if (planId) {
        where.planId = planId;
      }

      if (billingCycle) {
        where.billingCycle = billingCycle;
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // Lấy danh sách tùy chọn giá
      return this.planPricingCustomRepository.findPlanPricing(paginationParams);
    } catch (error) {
      this.logger.error(`Error finding plan pricings: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách tùy chọn giá'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tùy chọn giá
   * @param id ID của tùy chọn giá
   * @returns Thông tin chi tiết tùy chọn giá
   */
  async findPlanPricingById(id: number): Promise<PlanPricing> {
    try {
      const planPricing = await this.planPricingRepository.findOne({ where: { id } });

      if (!planPricing) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy tùy chọn giá với ID ${id}`
        );
      }

      return planPricing;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding plan pricing by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin tùy chọn giá'
      );
    }
  }

  /**
   * Lấy danh sách vai trò được phép sử dụng tùy chọn giá
   * @param planPricingId ID của tùy chọn giá
   * @returns Danh sách ID của các vai trò
   */
  async findRolesByPlanPricingId(planPricingId: number): Promise<number[]> {
    try {
      const roles = await this.planPricingHasRoleRepository.find({
        where: { planPricingId }
      });

      return roles.map(role => role.roleId);
    } catch (error) {
      this.logger.error(`Error finding roles by plan pricing ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách vai trò của tùy chọn giá'
      );
    }
  }

  /**
   * Validate roleIds tồn tại trong bảng user_roles
   * @param roleIds Danh sách ID vai trò cần kiểm tra
   * @throws AppException nếu có roleId không tồn tại
   */
  private async validateRoleIds(roleIds: number[]): Promise<void> {
    if (!roleIds || roleIds.length === 0) {
      return;
    }

    try {
      // Lấy tất cả roles tồn tại với các ID được cung cấp
      const existingRoles = await this.userRoleRepository.find({
        where: roleIds.map(id => ({ id })),
        select: ['id']
      });

      const existingRoleIds = existingRoles.map(role => role.id);
      const missingRoleIds = roleIds.filter(id => !existingRoleIds.includes(id));

      if (missingRoleIds.length > 0) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Các vai trò với ID [${missingRoleIds.join(', ')}] không tồn tại trong hệ thống`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error validating role IDs: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi kiểm tra tính hợp lệ của vai trò'
      );
    }
  }

  /**
   * Tạo tùy chọn giá mới
   * @param createPlanPricingDto Thông tin tùy chọn giá cần tạo
   * @returns Tùy chọn giá đã tạo
   */
  async createPlanPricing(createPlanPricingDto: CreatePlanPricingDto): Promise<PlanPricing> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Kiểm tra gói dịch vụ tồn tại
      await this.adminPlanService.findPlanById(createPlanPricingDto.planId);

      // Validate roleIds tồn tại trong hệ thống
      if (createPlanPricingDto.roleIds && createPlanPricingDto.roleIds.length > 0) {
        await this.validateRoleIds(createPlanPricingDto.roleIds);
      }

      const now = Math.floor(Date.now() / 1000);

      // Tạo tùy chọn giá mới
      const newPlanPricing = this.planPricingRepository.create({
        planId: createPlanPricingDto.planId,
        billingCycle: createPlanPricingDto.billingCycle,
        price: createPlanPricingDto.price,
        usageLimit: createPlanPricingDto.usageLimit,
        usageUnit: createPlanPricingDto.usageUnit,
        isActive: createPlanPricingDto.isActive,
        createdAt: now,
        updatedAt: now
      });

      // Lưu vào database
      const savedPlanPricing = await queryRunner.manager.save(newPlanPricing);

      // Lưu các vai trò được phép sử dụng
      if (createPlanPricingDto.roleIds && createPlanPricingDto.roleIds.length > 0) {
        const planPricingHasRoles = createPlanPricingDto.roleIds.map(roleId => ({
          planPricingId: savedPlanPricing.id,
          roleId
        }));

        await queryRunner.manager.save(PlanPricingHasRole, planPricingHasRoles);
      }

      await queryRunner.commitTransaction();
      return savedPlanPricing;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error creating plan pricing: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo tùy chọn giá mới'
      );
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Cập nhật thông tin tùy chọn giá
   * @param id ID của tùy chọn giá
   * @param updatePlanPricingDto Thông tin cần cập nhật
   * @returns Tùy chọn giá đã cập nhật
   */
  async updatePlanPricing(id: number, updatePlanPricingDto: UpdatePlanPricingDto): Promise<PlanPricing> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Kiểm tra tùy chọn giá tồn tại
      const planPricing = await this.findPlanPricingById(id);

      // Cập nhật thông tin
      const now = Math.floor(Date.now() / 1000);
      const updatedPlanPricing = {
        ...planPricing,
        ...updatePlanPricingDto,
        updatedAt: now
      };

      // Lưu vào database
      await queryRunner.manager.update(PlanPricing, id, updatedPlanPricing);

      // Cập nhật các vai trò được phép sử dụng
      if (updatePlanPricingDto.roleIds !== undefined) {
        // Validate roleIds tồn tại trong hệ thống
        if (updatePlanPricingDto.roleIds.length > 0) {
          await this.validateRoleIds(updatePlanPricingDto.roleIds);
        }

        // Xóa các vai trò cũ
        await queryRunner.manager.delete(PlanPricingHasRole, { planPricingId: id });

        // Thêm các vai trò mới
        if (updatePlanPricingDto.roleIds.length > 0) {
          const planPricingHasRoles = updatePlanPricingDto.roleIds.map(roleId => ({
            planPricingId: id,
            roleId
          }));

          await queryRunner.manager.save(PlanPricingHasRole, planPricingHasRoles);
        }
      }

      await queryRunner.commitTransaction();

      // Trả về thông tin đã cập nhật
      return this.findPlanPricingById(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating plan pricing: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật tùy chọn giá'
      );
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Xóa tùy chọn giá
   * @param id ID của tùy chọn giá
   */
  async deletePlanPricing(id: number): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Kiểm tra tùy chọn giá tồn tại
      const planPricing = await this.findPlanPricingById(id);

      // Xóa các vai trò được phép sử dụng
      await queryRunner.manager.delete(PlanPricingHasRole, { planPricingId: id });

      // Xóa tùy chọn giá
      await queryRunner.manager.remove(planPricing);

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting plan pricing: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa tùy chọn giá'
      );
    } finally {
      await queryRunner.release();
    }
  }
}
