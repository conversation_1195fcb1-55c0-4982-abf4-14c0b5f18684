import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for extending a subscription
 */
export class SubscriptionExtendDto {
  /**
   * ID of the plan pricing to use for extension
   */
  @ApiProperty({
    description: 'ID của tùy chọn giá dùng để gia hạn',
    example: 1,
    required: true
  })
  @IsInt()
  @Type(() => Number)
  planPricingId: number;

  /**
   * Whether to auto-renew the subscription
   */
  @ApiProperty({
    description: 'Tự động gia hạn',
    example: true,
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  autoRenew?: boolean = true;
}
