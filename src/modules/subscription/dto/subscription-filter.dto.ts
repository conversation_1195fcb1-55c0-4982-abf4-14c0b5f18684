import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { SubscriptionStatus } from '@modules/subscription/enums';
import { QueryDto } from '@common/dto';

export class SubscriptionFilterDto extends QueryDto {
  @ApiProperty({
    description: 'Trạng thái đăng ký',
    enum: SubscriptionStatus,
    required: false,
    example: SubscriptionStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;
}
