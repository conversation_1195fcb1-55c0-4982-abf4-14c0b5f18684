import { ApiProperty } from '@nestjs/swagger';
import { UsageLog } from '../entities/usage-log.entity';

export class UsageLogResponseDto {
  @ApiProperty({
    description: 'ID của lịch sử sử dụng',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của đăng ký',
    example: 1,
  })
  subscriptionId: number;

  @ApiProperty({
    description: 'Tính năng sử dụng',
    example: 'API_CALL',
  })
  feature: string;

  @ApiProperty({
    description: 'Số lượng sử dụng',
    example: 5,
  })
  amount: number;

  @ApiProperty({
    description: 'Thời điểm sử dụng (Unix timestamp)',
    example: 1632474086123,
  })
  usageTime: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1632474086123,
  })
  createdAt: number;

  constructor(usageLog: UsageLog) {
    this.id = usageLog.id;
    this.subscriptionId = usageLog.subscriptionId;
    this.feature = usageLog.feature;
    this.amount = usageLog.amount;
    this.usageTime = usageLog.usageTime;
    this.createdAt = usageLog.createdAt;
  }
}
