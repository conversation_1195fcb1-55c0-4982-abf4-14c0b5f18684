import { ApiProperty } from '@nestjs/swagger';
import {OrderStatus} from "@modules/subscription/enums/order-status.enum";

/**
 * DTO for order plan history response
 */
export class OrderPlanHistoryResponseDto {
  /**
   * ID of the order
   */
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 1
  })
  id: number;

  /**
   * ID of the user
   */
  @ApiProperty({
    description: 'ID của người dùng',
    example: 10
  })
  userId: number;

  /**
   * ID of the plan
   */
  @ApiProperty({
    description: 'ID của gói dịch vụ',
    example: 1
  })
  planId: number;

  /**
   * ID of the plan pricing
   */
  @ApiProperty({
    description: 'ID của tùy chọn giá',
    example: 1
  })
  planPricingId: number;

  /**
   * ID of the subscription
   */
  @ApiProperty({
    description: 'ID của đăng ký',
    example: 1,
    nullable: true
  })
  subscriptionId: number | null;

  /**
   * Name of the plan
   */
  @ApiProperty({
    description: 'Tên gói dịch vụ',
    example: 'Basic Plan'
  })
  planName: string;

  /**
   * Points used
   */
  @ApiProperty({
    description: 'Số điểm đã sử dụng',
    example: '100'
  })
  point: string;

  /**
   * Billing cycle
   */
  @ApiProperty({
    description: 'Chu kỳ thanh toán',
    example: 'MONTHLY'
  })
  billingCycle: string;

  /**
   * Usage limit
   */
  @ApiProperty({
    description: 'Giới hạn sử dụng',
    example: '1000',
    nullable: true
  })
  usageLimit: string | null;

  /**
   * Usage unit
   */
  @ApiProperty({
    description: 'Đơn vị sử dụng',
    example: 'API_CALLS',
    nullable: true
  })
  usageUnit: string | null;

  /**
   * Order status
   */
  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    example: 'COMPLETED',
    enum: OrderStatus
  })
  status: OrderStatus;

  /**
   * Created at timestamp
   */
  @ApiProperty({
    description: 'Thời điểm tạo',
    example: '1625097600000'
  })
  createdAt: string;

  /**
   * Whether this is an extension
   */
  @ApiProperty({
    description: 'Đánh dấu đây là gia hạn',
    example: false
  })
  isExtension: boolean;
}