import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PlanPricing } from '@modules/subscription/entities';
import { QueryDto } from '@common/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SqlHelper } from '@common/helpers';

@Injectable()
export class PlanPricingRepository extends Repository<PlanPricing> {
  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(PlanPricing, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  /**
   * Tìm kiếm plan pricing với các bộ lọc
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findPlanPricing(
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<PlanPricing>> {
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'plan_pricing',
    });
  }

  /**
   * Tìm kiếm plan pricing theo billingCycle
   * @param billingCycle Chu kỳ thanh toán
   * @param paginationParams Tham số phân trang
   * @returns Kết quả phân trang
   */
  async findByBillingCycle(
    billingCycle: string,
    paginationParams: QueryDto,
  ): Promise<PaginatedResult<PlanPricing>> {
    // Sử dụng helper function để lấy kết quả phân trang
    return this.sqlHelper.getPaginatedData(this, paginationParams, {
      alias: 'plan_pricing',
      customize: qb => {
        qb.where('plan_pricing.billing_cycle = :billingCycle', { billingCycle });
        return qb;
      }
    });
  }
}
