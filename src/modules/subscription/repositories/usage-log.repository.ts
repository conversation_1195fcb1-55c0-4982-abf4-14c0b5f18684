import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UsageLog } from '../entities/usage-log.entity';
import { QueryDto } from '@common/dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SqlHelper } from '@common/helpers';

@Injectable()
export class UsageLogRepository extends Repository<UsageLog> {
  private sqlHelper: SqlHelper;

  constructor(private dataSource: DataSource) {
    super(UsageLog, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  async findBySubscription(
    subscriptionId: number,
    startDate: number | null,
    endDate: number | null,
    query: QueryDto,
  ): Promise<PaginatedResult<UsageLog>> {
    return this.sqlHelper.getPaginatedData(this, query, {
      alias: 'log',
      selectFields: ['id', 'subscriptionId', 'usageTime'], // hoặc bỏ nếu cần all fields
      searchFields: [], // bạn có thể mở rộng
      customize: (qb) => {
        qb.where('log.subscriptionId = :subscriptionId', { subscriptionId });

        if (startDate && endDate) {
          qb.andWhere('log.usageTime BETWEEN :start AND :end', {
            start: startDate,
            end: endDate,
          });
        } else if (startDate) {
          qb.andWhere('log.usageTime >= :start', { start: startDate });
        } else if (endDate) {
          qb.andWhere('log.usageTime <= :end', { end: endDate });
        }

        return qb;
      },
    });
  }
}
