import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('plan_pricing') // Bảng lưu các mức gi<PERSON> khác nhau cho mỗi plan theo chu kỳ thanh toán
export class PlanPricing {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  planId: number; // Khóa ngoại tới bảng plans

  @Column({ length: 50 })
  billingCycle: string; // Chu <PERSON>ỳ thanh toán (month, 6_months, year,...)

  @Column({ type: 'numeric' })
  price: number; // Giá của plan ứng với chu kỳ thanh toán này

  @Column({ type: 'bigint' })
  createdAt: number; // Thời điểm tạo (Unix timestamp)

  @Column({ type: 'bigint', nullable: true })
  updatedAt: number; // Thời điểm cập nhật (Unix timestamp)

  @Column({ type: 'bigint', nullable: true })
  usageLimit: number; // Giới hạn dung lượng (có thể là NULL nếu là TIME_ONLY). Dùng BIGINT để lưu giá trị lớn (vd: bytes, requests)

  @Column({ length: 50, nullable: true })
  usageUnit: string; // Đơn vị của giá trị gói

  @Column({ type: 'boolean' })
  isActive: boolean; // Có hoạt động hay không
}
