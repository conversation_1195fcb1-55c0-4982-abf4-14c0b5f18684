# API Documentation - Integration User Module

## Tổng quan

Module Integration User cung cấp các API cho phép người dùng quản lý các tích hợp với các dịch vụ bên ngoài như:
- API keys cho các nhà cung cấp AI (OpenAI, Claude, v.v.)
- <PERSON><PERSON>u hình máy chủ email (SMTP)
- Quản lý website
- Tích hợp cổng thanh toán
- T<PERSON>ch hợp Facebook Page
- Tích hợp SMS
- Tích hợp Google Ads

Tất cả các API trong module này đều yêu cầu xác thực JWT và được bảo vệ bởi `JwtUserGuard`.

## Cấu trúc phản hồi chung

Tất cả các API đều trả về cấu trúc phản hồi thống nhất theo định dạng `ApiResponseDto`:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    // Dữ liệu trả về
  }
}
```

<PERSON><PERSON>i với các API trả về danh sách có phân trang, cấu trúc phản hồi sẽ là:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    "items": [
      // Mảng các đối tượng
    ],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

---

## 1. User Key API

Quản lý các API key của người dùng cho các nhà cung cấp AI.

### 1.1. Lấy danh sách API key

```
GET /integration/user-key
```

**Mô tả:** Lấy danh sách API key của người dùng hiện tại với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| providerKey | String | Không | Tìm kiếm theo tên nhà cung cấp (ví dụ: "openai") |

**DTO:**

```typescript
// UserKeyQueryDto
{
  page?: number;        // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;       // Số lượng bản ghi trên mỗi trang
  providerKey?: string; // Tìm kiếm theo tên nhà cung cấp
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách API key thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "providerId": 1,
        "provider": {
          "id": 1,
          "providerKey": "openai",
          "name": "OpenAI",
          "icon": "https://example.com/openai-icon.png"
        },
        "credentials": {
          "api_key": "********abcd",
          "organization_id": "org-******"
        },
        "settings": {
          "is_active": true,
          "is_default": true
        },
        "createdAt": 1625097600000,
        "updatedAt": 1625097600000
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 1.2. Lấy thông tin chi tiết API key

```
GET /integration/user-key/:id
```

**Mô tả:** Lấy thông tin chi tiết của một API key cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của API key |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết API key thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "providerId": 1,
    "provider": {
      "id": 1,
      "providerKey": "openai",
      "name": "OpenAI",
      "icon": "https://example.com/openai-icon.png"
    },
    "credentials": {
      "api_key": "********abcd",
      "organization_id": "org-******"
    },
    "settings": {
      "is_active": true,
      "is_default": true
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000
  }
}
```

### 1.3. Tạo API key mới

```
POST /integration/user-key
```

**Mô tả:** Tạo một API key mới cho người dùng.

**Body:**

```typescript
// CreateUserKeyDto
{
  providerId: number;                // ID của nhà cung cấp AI
  credentials: Record<string, any>;  // Thông tin xác thực API
  settings?: Record<string, any>;    // Cài đặt người dùng (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "providerId": 1,
  "credentials": {
    "api_key": "sk-abcdefghijklmnopqrstuvwxyz",
    "organization_id": "org-123456789"
  },
  "settings": {
    "is_active": true,
    "is_default": true
  }
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Tạo API key mới thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "providerId": 1,
    "provider": {
      "id": 1,
      "providerKey": "openai",
      "name": "OpenAI",
      "icon": "https://example.com/openai-icon.png"
    },
    "credentials": {
      "api_key": "********abcd",
      "organization_id": "org-******"
    },
    "settings": {
      "is_active": true,
      "is_default": true
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000
  }
}
```

### 1.4. Cập nhật API key

```
PUT /integration/user-key/:id
```

**Mô tả:** Cập nhật thông tin của một API key hiện có.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của API key |

**Body:**

```typescript
// UpdateUserKeyDto
{
  credentials?: Record<string, any>;  // Thông tin xác thực API (tùy chọn)
  settings?: Record<string, any>;     // Cài đặt người dùng (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "credentials": {
    "api_key": "sk-newapikeyvalue",
    "organization_id": "org-neworgid"
  },
  "settings": {
    "is_active": false,
    "is_default": false
  }
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Cập nhật API key thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "providerId": 1,
    "provider": {
      "id": 1,
      "providerKey": "openai",
      "name": "OpenAI",
      "icon": "https://example.com/openai-icon.png"
    },
    "credentials": {
      "api_key": "********newv",
      "organization_id": "org-******"
    },
    "settings": {
      "is_active": false,
      "is_default": false
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097700000
  }
}
```

### 1.5. Xóa API key

```
DELETE /integration/user-key/:id
```

**Mô tả:** Xóa một API key của người dùng.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của API key |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Xóa API key thành công",
  "result": null
}
```

### 1.6. Lấy danh sách nhà cung cấp AI

```
GET /integration/user-key/providers/list
```

**Mô tả:** Lấy danh sách các nhà cung cấp AI được hỗ trợ.

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách nhà cung cấp AI thành công",
  "result": [
    {
      "id": 1,
      "providerKey": "openai",
      "name": "OpenAI",
      "icon": "https://example.com/openai-icon.png",
      "baseUrl": "https://api.openai.com/v1"
    },
    {
      "id": 2,
      "providerKey": "anthropic",
      "name": "Anthropic",
      "icon": "https://example.com/anthropic-icon.png",
      "baseUrl": "https://api.anthropic.com"
    }
  ]
}
```

### 1.7. Kiểm tra API key

```
POST /integration/user-key/test
```

**Mô tả:** Kiểm tra tính hợp lệ của một API key trước khi lưu.

**Body:**

```typescript
// TestUserKeyDto
{
  providerKey: string;               // Key nội bộ định danh nhà cung cấp
  credentials: Record<string, any>;  // Thông tin xác thực API
  model?: string;                    // Mô hình để kiểm tra (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "providerKey": "openai",
  "credentials": {
    "api_key": "sk-abcdefghijklmnopqrstuvwxyz",
    "organization_id": "org-123456789"
  },
  "model": "gpt-3.5-turbo"
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Kiểm tra API key thành công",
  "result": {
    "success": true,
    "message": "API key hợp lệ và có thể sử dụng"
  }
}
```

---

## 2. Email Server Configuration API

Quản lý cấu hình máy chủ email (SMTP) của người dùng.

### 2.1. Lấy danh sách cấu hình máy chủ email

```
GET /user/integration/email-server
```

**Mô tả:** Lấy danh sách tất cả các cấu hình máy chủ email của người dùng.

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách cấu hình máy chủ email thành công",
  "result": [
    {
      "id": 1,
      "userId": 123,
      "serverName": "Gmail SMTP",
      "host": "smtp.gmail.com",
      "port": 587,
      "username": "<EMAIL>",
      "useSsl": true,
      "additionalSettings": {
        "auth": "login",
        "tls": {
          "rejectUnauthorized": false
        }
      },
      "createdAt": 1625097600000,
      "updatedAt": 1625097600000
    }
  ]
}
```

### 2.2. Lấy thông tin chi tiết cấu hình máy chủ email

```
GET /user/integration/email-server/:id
```

**Mô tả:** Lấy thông tin chi tiết của một cấu hình máy chủ email cụ thể.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy thông tin chi tiết cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "serverName": "Gmail SMTP",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000
  }
}
```

### 2.3. Tạo mới cấu hình máy chủ email

```
POST /user/integration/email-server
```

**Mô tả:** Tạo một cấu hình máy chủ email mới cho người dùng.

**Body:**

```typescript
// CreateEmailServerDto
{
  serverName: string;                  // Tên hiển thị của cấu hình
  host: string;                        // Địa chỉ máy chủ SMTP
  port: number;                        // Cổng SMTP
  username: string;                    // Tên đăng nhập
  password: string;                    // Mật khẩu
  useSsl: boolean;                     // Sử dụng SSL/TLS
  additionalSettings?: Record<string, any>; // Cấu hình nâng cao (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "serverName": "Gmail SMTP",
  "host": "smtp.gmail.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "app-password-or-token",
  "useSsl": true,
  "additionalSettings": {
    "auth": "login",
    "tls": {
      "rejectUnauthorized": false
    }
  }
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Tạo mới cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "serverName": "Gmail SMTP",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000
  }
}
```

### 2.4. Cập nhật cấu hình máy chủ email

```
PUT /user/integration/email-server/:id
```

**Mô tả:** Cập nhật thông tin của một cấu hình máy chủ email hiện có.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Body:**

```typescript
// UpdateEmailServerDto
{
  serverName?: string;                  // Tên hiển thị của cấu hình
  host?: string;                        // Địa chỉ máy chủ SMTP
  port?: number;                        // Cổng SMTP
  username?: string;                    // Tên đăng nhập
  password?: string;                    // Mật khẩu
  useSsl?: boolean;                     // Sử dụng SSL/TLS
  additionalSettings?: Record<string, any>; // Cấu hình nâng cao
}
```

**Ví dụ:**

```json
{
  "serverName": "Gmail SMTP Updated",
  "port": 465,
  "useSsl": true
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Cập nhật cấu hình máy chủ email thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "serverName": "Gmail SMTP Updated",
    "host": "smtp.gmail.com",
    "port": 465,
    "username": "<EMAIL>",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": {
        "rejectUnauthorized": false
      }
    },
    "createdAt": 1625097600000,
    "updatedAt": 1625097700000
  }
}
```

### 2.5. Xóa cấu hình máy chủ email

```
DELETE /user/integration/email-server/:id
```

**Mô tả:** Xóa một cấu hình máy chủ email của người dùng.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Xóa cấu hình máy chủ email thành công",
  "result": {
    "message": "Cấu hình máy chủ email đã được xóa"
  }
}
```

### 2.6. Kiểm tra kết nối máy chủ email

```
POST /user/integration/email-server/:id/test
```

**Mô tả:** Kiểm tra kết nối đến máy chủ email và gửi email thử nghiệm.

**Tham số đường dẫn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| id | Number | Có | ID của cấu hình máy chủ email |

**Body:**

```typescript
// TestEmailServerDto
{
  recipientEmail: string;  // Địa chỉ email nhận thư kiểm tra
  subject?: string;        // Tiêu đề email kiểm tra (tùy chọn)
}
```

**Ví dụ:**

```json
{
  "recipientEmail": "<EMAIL>",
  "subject": "Test Email Connection"
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Kiểm tra kết nối máy chủ email thành công",
  "result": {
    "success": true,
    "message": "Kết nối thành công! Email kiểm tra đã được gửi.",
    "details": null
  }
}
```

---

## 3. Website API

Quản lý các website của người dùng.

### 3.1. Lấy danh sách website

```
GET /integration/website
```

**Mô tả:** Lấy danh sách website của người dùng với phân trang.

**Tham số truy vấn:**

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| page | Number | Không | Số trang hiện tại (bắt đầu từ 1) |
| limit | Number | Không | Số lượng bản ghi trên mỗi trang |
| search | String | Không | Từ khóa tìm kiếm theo tên website |
| verify | Boolean | Không | Lọc theo trạng thái xác minh |
| sortBy | String | Không | Trường sắp xếp (websiteName, host, createdAt, verify) |
| sortDirection | String | Không | Hướng sắp xếp (ASC, DESC) |

**DTO:**

```typescript
// WebsiteQueryDto
{
  page?: number;                // Số trang hiện tại (bắt đầu từ 1)
  limit?: number;               // Số lượng bản ghi trên mỗi trang
  search?: string;              // Từ khóa tìm kiếm theo tên website
  verify?: boolean;             // Lọc theo trạng thái xác minh
  sortBy?: string;              // Trường sắp xếp
  sortDirection?: SortDirection; // Hướng sắp xếp
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Lấy danh sách website thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "websiteName": "Website của tôi",
        "host": "example.com",
        "verify": false,
        "createdAt": 1625097600000,
        "updatedAt": 1625097600000
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 3.2. Tạo mới website

```
POST /integration/website
```

**Mô tả:** Tạo một website mới cho người dùng.

**Body:**

```typescript
// CreateWebsiteDto
{
  websiteName: string;  // Tên website do người dùng đặt
  host: string;         // Tên miền hoặc địa chỉ host của website
}
```

**Ví dụ:**

```json
{
  "websiteName": "Website của tôi",
  "host": "example.com"
}
```

**Phản hồi:**

```json
{
  "code": 200,
  "message": "Tạo website thành công",
  "result": {
    "id": 1,
    "userId": 123,
    "websiteName": "Website của tôi",
    "host": "example.com",
    "verify": false,
    "createdAt": 1625097600000,
    "updatedAt": 1625097600000
  }
}
```

## Lỗi chung

Tất cả các API đều có thể trả về các mã lỗi sau:

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Bad Request - Dữ liệu không hợp lệ |
| 401 | Unauthorized - Không có quyền truy cập |
| 403 | Forbidden - Không có quyền thực hiện hành động |
| 404 | Not Found - Không tìm thấy tài nguyên |
| 500 | Internal Server Error - Lỗi máy chủ |

Cấu trúc phản hồi lỗi:

```json
{
  "code": 12000, // Mã lỗi cụ thể của module
  "message": "Thông báo lỗi chi tiết",
  "result": null
}
```
