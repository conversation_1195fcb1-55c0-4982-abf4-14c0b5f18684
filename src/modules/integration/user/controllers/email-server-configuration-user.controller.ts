import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { EmailServerConfigurationUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse, PaginatedResult } from '@/common/response';
import { CreateEmailServerDto, EmailServerResponseDto, TestEmailServerDto, TestEmailServerResponseDto, UpdateEmailServerDto, TestEmailServerWithConfigDto, EmailServerQueryDto } from '../dto';
import { EmailServerConfiguration } from '@modules/integration/entities';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('user/integration/email-server')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class EmailServerConfigurationUserController {
  constructor(
    private readonly emailServerConfigurationUserService: EmailServerConfigurationUserService,
  ) {}

  /**
   * Lấy danh sách cấu hình máy chủ email của người dùng
   * @param user Thông tin người dùng hiện tại
   * @returns Danh sách cấu hình máy chủ email
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách cấu hình máy chủ email' })
  @ApiResponse({ status: 200, description: 'Danh sách cấu hình máy chủ email', type: [EmailServerResponseDto] })
  async findAll(@CurrentUser() user: JwtPayload): Promise<AppApiResponse<EmailServerConfiguration[]>> {
    const emailServers = await this.emailServerConfigurationUserService.findAllByUserId(user.id);
    return AppApiResponse.success(emailServers, 'Lấy danh sách cấu hình máy chủ email thành công');
  }

  /**
   * Lấy danh sách cấu hình máy chủ email của người dùng có phân trang
   * @param queryDto Tham số truy vấn
   * @param user Thông tin người dùng hiện tại
   * @returns Danh sách cấu hình máy chủ email có phân trang
   */
  @Get('paginated')
  @ApiOperation({ summary: 'Lấy danh sách cấu hình máy chủ email có phân trang' })
  @ApiResponse({ status: 200, description: 'Danh sách cấu hình máy chủ email có phân trang', type: PaginatedResult })
  async findAllPaginated(
    @Query() queryDto: EmailServerQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<PaginatedResult<EmailServerConfiguration>>> {
    const result = await this.emailServerConfigurationUserService.findAllPaginated(queryDto, user.id);
    return AppApiResponse.paginated(result, 'Lấy danh sách cấu hình máy chủ email có phân trang thành công');
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param user Thông tin người dùng hiện tại
   * @returns Thông tin chi tiết của cấu hình máy chủ email
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({ status: 200, description: 'Thông tin chi tiết của cấu hình máy chủ email', type: EmailServerResponseDto })
  async findOne(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: JwtPayload): Promise<AppApiResponse<EmailServerConfiguration>> {
    const emailServer = await this.emailServerConfigurationUserService.findOne(id, user.id);
    return AppApiResponse.success(emailServer, 'Lấy thông tin chi tiết cấu hình máy chủ email thành công');
  }

  /**
   * Tạo mới cấu hình máy chủ email
   * @param createEmailServerDto Thông tin cấu hình máy chủ email cần tạo
   * @param user Thông tin người dùng hiện tại
   * @returns Cấu hình máy chủ email đã được tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới cấu hình máy chủ email' })
  @ApiResponse({ status: 201, description: 'Cấu hình máy chủ email đã được tạo', type: EmailServerResponseDto })
  async create(@Body() createEmailServerDto: CreateEmailServerDto, @CurrentUser() user: JwtPayload): Promise<AppApiResponse<EmailServerConfiguration>> {
    const emailServer = await this.emailServerConfigurationUserService.create(createEmailServerDto, user.id);
    return AppApiResponse.success(emailServer, 'Tạo mới cấu hình máy chủ email thành công');
  }

  /**
   * Cập nhật thông tin cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param updateEmailServerDto Thông tin cần cập nhật
   * @param user Thông tin người dùng hiện tại
   * @returns Cấu hình máy chủ email đã được cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({ status: 200, description: 'Cấu hình máy chủ email đã được cập nhật', type: EmailServerResponseDto })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmailServerDto: UpdateEmailServerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<EmailServerConfiguration>> {
    const emailServer = await this.emailServerConfigurationUserService.update(id, updateEmailServerDto, user.id);
    return AppApiResponse.success(emailServer, 'Cập nhật cấu hình máy chủ email thành công');
  }

  /**
   * Xóa cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param user Thông tin người dùng hiện tại
   * @returns Thông báo kết quả
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa cấu hình máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({ status: 200, description: 'Thông báo kết quả xóa' })
  async remove(@Param('id', ParseIntPipe) id: number, @CurrentUser() user: JwtPayload): Promise<AppApiResponse<{ message: string }>> {
    const result = await this.emailServerConfigurationUserService.remove(id, user.id);
    return AppApiResponse.success(result, 'Xóa cấu hình máy chủ email thành công');
  }

  /**
   * Kiểm tra kết nối máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param testEmailServerDto Thông tin kiểm tra
   * @param user Thông tin người dùng hiện tại
   * @returns Kết quả kiểm tra
   */
  @Post(':id/test')
  @ApiOperation({ summary: 'Kiểm tra kết nối máy chủ email' })
  @ApiParam({ name: 'id', description: 'ID của cấu hình máy chủ email', type: Number })
  @ApiResponse({ status: 200, description: 'Kết quả kiểm tra kết nối', type: TestEmailServerResponseDto })
  async testConnection(
    @Param('id', ParseIntPipe) id: number,
    @Body() testEmailServerDto: TestEmailServerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<AppApiResponse<{ success: boolean; message: string; details?: any }>> {
    const result = await this.emailServerConfigurationUserService.testConnection(id, testEmailServerDto, user.id);
    return AppApiResponse.success(result, 'Kiểm tra kết nối máy chủ email thành công');
  }

  /**
   * Kiểm tra kết nối máy chủ email với cấu hình trực tiếp
   * @param testEmailServerWithConfigDto Thông tin cấu hình và kiểm tra
   * @param user Thông tin người dùng hiện tại
   * @returns Kết quả kiểm tra
   */
  @Post('test-with-config')
  @ApiOperation({ summary: 'Kiểm tra kết nối máy chủ email với cấu hình trực tiếp' })
  @ApiResponse({ status: 200, description: 'Kết quả kiểm tra kết nối', type: TestEmailServerResponseDto })
  async testConnectionWithConfig(   
    @Body() testEmailServerWithConfigDto: TestEmailServerWithConfigDto,
  ): Promise<AppApiResponse<{ success: boolean; message: string; details?: any }>> {
    const result = await this.emailServerConfigurationUserService.testConnectionWithConfig(testEmailServerWithConfigDto);
    return AppApiResponse.success(result, 'Kiểm tra kết nối máy chủ email thành công');
  }
}
