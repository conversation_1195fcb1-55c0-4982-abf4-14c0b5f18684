import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@/common/dto/query.dto';
import { Type } from 'class-transformer';

/**
 * DTO cho query parameters khi lấy danh sách website
 */
export class WebsiteQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['websiteName', 'host', 'createdAt', 'verify'],
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;

  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo trạng thái xác minh',
    example: 'true',
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  verify?: boolean;

  @ApiProperty({
    description: 'Lọc theo trạng thái gán agent (true: đã gán agent, false: chưa gán agent)',
    example: 'true',
    required: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  hasAgent?: boolean;
}
