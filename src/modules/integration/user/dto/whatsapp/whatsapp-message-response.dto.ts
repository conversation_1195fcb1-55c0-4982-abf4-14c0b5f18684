import { ApiProperty } from '@nestjs/swagger';
import { WhatsAppMessageType } from './send-whatsapp-message.dto';

/**
 * DTO cho việc trả về thông tin tin nhắn WhatsApp
 */
export class WhatsAppMessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID tài khoản WhatsApp',
    example: 1,
  })
  whatsappAccountId: number;

  @ApiProperty({
    description: 'ID tin nhắn trên WhatsApp',
    example: 'wamid.abcdefghijklmnopqrstuvwxyz',
  })
  messageId: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận/gửi',
    example: '+***********',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Tên người nhận/gửi (nếu có)',
    example: '<PERSON>uy<PERSON><PERSON>',
    nullable: true,
  })
  contactName: string | null;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Cảm ơn bạn đã liên hệ với RedAI.',
    nullable: true,
  })
  content: string | null;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: WhatsAppMessageType,
    example: WhatsAppMessageType.TEXT,
  })
  messageType: WhatsAppMessageType;

  @ApiProperty({
    description: 'Metadata của tin nhắn',
    example: {
      templateName: 'welcome_message',
      mediaUrl: 'https://example.com/image.jpg',
    },
  })
  metadata: Record<string, any>;

  @ApiProperty({
    description: 'Hướng tin nhắn',
    example: 'outgoing',
    enum: ['incoming', 'outgoing'],
  })
  direction: string;

  @ApiProperty({
    description: 'Trạng thái gửi tin nhắn',
    example: 'sent',
    enum: ['sent', 'delivered', 'read', 'failed'],
    nullable: true,
  })
  status: string | null;

  @ApiProperty({
    description: 'Thời điểm gửi tin nhắn (Unix timestamp)',
    example: 1623456789,
  })
  sentAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật trạng thái (Unix timestamp)',
    example: 1623456789,
  })
  updatedAt: number;
}
