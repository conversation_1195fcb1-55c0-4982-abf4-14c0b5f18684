import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsObject, IsEnum, Matches } from 'class-validator';

/**
 * Enum cho loại tin nhắn WhatsApp
 */
export enum WhatsAppMessageType {
  TEXT = 'text',
  TEMPLATE = 'template',
  IMAGE = 'image',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  VIDEO = 'video',
}

/**
 * DTO cho việc gửi tin nhắn WhatsApp
 */
export class SendWhatsAppMessageDto {
  @ApiProperty({
    description: 'Số điện thoại người nhận (định dạng quốc tế)',
    example: '+84987654321',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message: 'Số điện thoại phải ở định dạng quốc tế (bắt đầu bằng dấu +)',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Loại tin nhắn',
    enum: WhatsAppMessageType,
    example: WhatsAppMessageType.TEXT,
  })
  @IsNotEmpty()
  @IsEnum(WhatsAppMessageType)
  messageType: WhatsAppMessageType;

  @ApiProperty({
    description: 'Nội dung tin nhắn văn bản (chỉ dùng khi messageType là text)',
    example: 'Xin chào! Cảm ơn bạn đã liên hệ với RedAI.',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'Thông tin mẫu tin nhắn (chỉ dùng khi messageType là template)',
    example: {
      name: 'welcome_message',
      language: {
        code: 'vi',
      },
      components: [
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: 'Nguyễn Văn A',
            },
          ],
        },
      ],
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  template?: Record<string, any>;

  @ApiProperty({
    description: 'Thông tin media (chỉ dùng khi messageType là image, document, audio, video)',
    example: {
      link: 'https://example.com/image.jpg',
      caption: 'Hình ảnh sản phẩm',
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  media?: Record<string, any>;
}
