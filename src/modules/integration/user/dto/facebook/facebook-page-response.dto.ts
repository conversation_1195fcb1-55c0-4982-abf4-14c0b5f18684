import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin agent đ<PERSON><PERSON><PERSON> kết nối với trang Facebook
 */
export class AgentInfoDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent của tôi'
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiProperty({
    description: 'URL avatar của agent',
    example: 'https://cdn.example.com/agents/avatars/agent-123.jpg',
    nullable: true
  })
  avatar?: string | null;
}

/**
 * DTO cho response của trang Facebook
 */
export class FacebookPageResponseDto {
  /**
   * ID duy nhất của trang Facebook
   */
  @ApiProperty({
    description: 'ID duy nhất của trang Facebook',
    example: '123456789012345'
  })
  facebookPageId: string;

  /**
   * ID của tài kho<PERSON>n Facebook cá nhân liên kết
   */
  @ApiProperty({
    description: 'ID của tài khoản Facebook cá nhân liên kết',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  facebookPersonalId: string;

  /**
   * Access token của trang Facebook
   */
  @ApiProperty({
    description: 'Tên tài khoản Facebook cá nhân liên kết',
    example: 'Nguyen Van A'
  })
  facebookPersonalName: string;

  /**
   * Tên trang Facebook
   */
  @ApiProperty({
    description: 'Tên trang Facebook',
    example: 'Trang Facebook của tôi'
  })
  pageName: string;

  /**
   * URL avatar của trang Facebook
   */
  @ApiProperty({
    description: 'URL avatar của trang Facebook',
    example: 'https://cdn.example.com/facebook/profile/2023/05/page-123456789012345-avatar.jpg'
  })
  avatarPage: string | null;

  /**
   * Trạng thái hoạt động của trang
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của trang',
    example: true
  })
  isActive: boolean;

  /**
   * ID agent được kết nối với trang Facebook
   */
  @ApiProperty({
    description: 'ID agent được kết nối với trang Facebook',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    nullable: true
  })
  agentId: string | null | undefined;

  /**
   * Trạng thái lỗi của trang Facebook
   */
  @ApiProperty({
    description: 'Trạng thái lỗi của trang Facebook',
    example: false
  })
  isError: boolean;

  /**
   * Thông tin agent được kết nối với trang Facebook
   */
  @ApiProperty({
    description: 'Thông tin agent được kết nối với trang Facebook',
    nullable: true,
    type: AgentInfoDto
  })
  agent?: AgentInfoDto;
}
