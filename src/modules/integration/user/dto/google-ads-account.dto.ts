import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * Enum trạng thái tài khoản Google Ads
 */
export enum GoogleAdsAccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

/**
 * DTO cho response thông tin tài khoản Google Ads
 */
export class GoogleAdsAccountResponseDto {
  @ApiProperty({
    description: 'ID của tài khoản',
    example: 1,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Customer ID của tài khoản Google Ads',
    example: '**********',
  })
  @IsString()
  customerId: string;

  @ApiProperty({
    description: 'Tên tài khoản',
    example: 'Tài khoản quảng cáo chính',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    enum: GoogleAdsAccountStatus,
    example: GoogleAdsAccountStatus.ACTIVE,
  })
  @IsEnum(GoogleAdsAccountStatus)
  status: string;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********,
  })
  @IsNumber()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: **********,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  updatedAt?: number;
}

/**
 * DTO cho request cập nhật tài khoản Google Ads
 */
export class UpdateGoogleAdsAccountDto {
  @ApiProperty({
    description: 'Tên tài khoản',
    example: 'Tài khoản quảng cáo chính',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    enum: GoogleAdsAccountStatus,
    example: GoogleAdsAccountStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(GoogleAdsAccountStatus)
  status?: GoogleAdsAccountStatus;
}
