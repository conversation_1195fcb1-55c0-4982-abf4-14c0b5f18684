import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, IsNumber, IsBoolean, IsOptional, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc kiểm tra kết nối máy chủ email
 */
export class TestEmailServerDto {
  @ApiProperty({
    description: 'Địa chỉ email nhận thư kiểm tra',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Địa chỉ email không được để trống' })
  @IsEmail({}, { message: 'Địa chỉ email không hợp lệ' })
  recipientEmail: string;

  @ApiProperty({
    description: 'Tiêu đề email kiểm tra',
    example: 'Test Email Connection',
    required: false,
  })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  subject?: string;
}

/**
 * DTO cho cấu hình máy chủ email trong việc test kết nối
 */
export class EmailServerConfigDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"',
    example: 'Gmail SMTP',
  })
  @IsNotEmpty({ message: 'Tên máy chủ không được để trống' })
  @IsString({ message: 'Tên máy chủ phải là chuỗi' })
  serverName: string;

  @ApiProperty({
    description: 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…',
    example: 'smtp.gmail.com',
  })
  @IsNotEmpty({ message: 'Địa chỉ máy chủ không được để trống' })
  @IsString({ message: 'Địa chỉ máy chủ phải là chuỗi' })
  host: string;

  @ApiProperty({
    description: 'Cổng SMTP, ví dụ: 465, 587, …',
    example: 587,
  })
  @IsNotEmpty({ message: 'Cổng không được để trống' })
  @IsNumber({}, { message: 'Cổng phải là số' })
  port: number;

  @ApiProperty({
    description: 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  username: string;

  @ApiProperty({
    description: 'Mật khẩu hoặc token xác thực cho SMTP',
    example: 'your-app-password',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  password: string;

  @ApiProperty({
    description: 'Xác định có sử dụng SSL/TLS hay không',
    example: true,
  })
  @IsNotEmpty({ message: 'Trường useSsl không được để trống' })
  @IsBoolean({ message: 'Trường useSsl phải là boolean' })
  useSsl: boolean;

  @ApiProperty({
    description: 'Xác định có sử dụng STARTTLS hay không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường useStartTls phải là boolean' })
  useStartTls?: boolean;

  @ApiProperty({
    description: 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.',
    example: { auth: 'login', tls: { rejectUnauthorized: false } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình nâng cao phải là đối tượng' })
  additionalSettings?: Record<string, any>;
}

/**
 * DTO cho việc kiểm tra kết nối máy chủ email với cấu hình trực tiếp
 */
export class TestEmailServerWithConfigDto {
  @ApiProperty({
    description: 'Cấu hình máy chủ email',
    type: EmailServerConfigDto,
  })
  @ValidateNested()
  @Type(() => EmailServerConfigDto)
  emailServerConfig: EmailServerConfigDto;

  @ApiProperty({
    description: 'Thông tin kiểm tra email',
    type: TestEmailServerDto,
  })
  @ValidateNested()
  @Type(() => TestEmailServerDto)
  testInfo: TestEmailServerDto;
}
