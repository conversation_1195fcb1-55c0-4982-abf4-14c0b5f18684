import { Injectable, Logger } from '@nestjs/common';
import { UserProviderShipmentRepository } from '../../repositories';
import { EncryptionService } from '../../services/encryption.service';
import { GHNValidationService } from '../../services/providers/ghn-validation.service';
import { GHTKValidationService } from '../../services/providers/ghtk-validation.service';
import { AhamoveValidationService } from '../../services/providers/ahamove-validation.service';
import { JTValidationService } from '../../services/providers/jt-validation.service';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import {
  CreateShipmentProviderDto,
  UpdateShipmentProviderDto,
  ShipmentProviderQueryDto,
  ShipmentProviderResponseDto,
  TestShipmentProviderDto,
  TestShipmentProviderResponseDto
} from '../dto/shipment';
import { UserProviderShipment } from '../../entities/user-provider-shipment.entity';
import { ProviderShipmentType } from '../../constants/provider-shipment-type.enum';
import {
  GHNProviderConfig,
  GHTKProviderConfig,
  AhamoveProviderConfig,
  JTProviderConfig
} from '../../interfaces';
import { DataMaskingUtil } from '../../utils/data-masking.util';

@Injectable()
export class ShipmentProviderUserService {
  private readonly logger = new Logger(ShipmentProviderUserService.name);

  constructor(
    private readonly userProviderShipmentRepository: UserProviderShipmentRepository,
    private readonly encryptionService: EncryptionService,
    private readonly ghnValidationService: GHNValidationService,
    private readonly ghtkValidationService: GHTKValidationService,
    private readonly ahamoveValidationService: AhamoveValidationService,
    private readonly jtValidationService: JTValidationService,
  ) {}

  /**
   * Lấy danh sách cấu hình nhà cung cấp vận chuyển của user
   */
  async findUserShipmentProviders(
    userId: number,
    queryDto: ShipmentProviderQueryDto
  ): Promise<PaginatedResult<ShipmentProviderResponseDto>> {
    try {
      const { page = 1, limit = 10, type } = queryDto;

      const result = await this.userProviderShipmentRepository.findByUserIdWithPagination(
        userId,
        page,
        limit,
        type
      );

      const items = result.items.map(item => this.mapToResponseDto(item));

      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Error finding user shipment providers: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách cấu hình vận chuyển'
      );
    }
  }

  /**
   * Lấy chi tiết cấu hình nhà cung cấp vận chuyển
   */
  async findShipmentProviderById(userId: number, id: string): Promise<ShipmentProviderResponseDto> {
    try {
      const shipmentProvider = await this.userProviderShipmentRepository.findByIdAndUserId(id, userId);

      if (!shipmentProvider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình vận chuyển với ID ${id}`
        );
      }

      return this.mapToResponseDto(shipmentProvider);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding shipment provider by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin cấu hình vận chuyển'
      );
    }
  }

  /**
   * Tạo cấu hình nhà cung cấp vận chuyển mới
   */
  async createShipmentProvider(
    userId: number,
    createDto: CreateShipmentProviderDto
  ): Promise<ShipmentProviderResponseDto> {
    try {
      // Kiểm tra xem user đã có cấu hình cho type này chưa
      const existingConfig = await this.userProviderShipmentRepository.existsByTypeAndUserId(
        createDto.type,
        userId
      );

      if (existingConfig) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Bạn đã có cấu hình cho nhà cung cấp ${createDto.type}. Vui lòng cập nhật cấu hình hiện có.`
        );
      }

      // Validate cấu hình theo từng type
      this.validateProviderConfig(createDto.type, createDto.config);

      await this.testShipmentProvider({
        type: createDto.type,
        config: createDto.config
      });

      // Mã hóa dữ liệu nhạy cảm
      const encryptedKey = this.encryptionService.encrypt(createDto.config);

      // Tạo entity mới
      const newShipmentProvider = this.userProviderShipmentRepository.create({
        userId,
        name: createDto.name || `${createDto.type} Configuration`,
        type: createDto.type,
        key: encryptedKey
      });

      // Lưu vào database
      const savedProvider = await this.userProviderShipmentRepository.save(newShipmentProvider);

      return this.mapToResponseDto(savedProvider);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error creating shipment provider: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo cấu hình vận chuyển mới'
      );
    }
  }

  /**
   * Cập nhật cấu hình nhà cung cấp vận chuyển
   */
  async updateShipmentProvider(
    userId: number,
    id: string,
    updateDto: UpdateShipmentProviderDto
  ): Promise<ShipmentProviderResponseDto> {
    try {
      // Kiểm tra cấu hình tồn tại
      const shipmentProvider = await this.userProviderShipmentRepository.findByIdAndUserId(id, userId);

      if (!shipmentProvider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình vận chuyển với ID ${id}`
        );
      }

      // Cập nhật tên nếu có
      if (updateDto.name !== undefined) {
        shipmentProvider.name = updateDto.name;
      }

      // Cập nhật cấu hình nếu có
      if (updateDto.config) {
        // Validate cấu hình mới
        this.validateProviderConfig(shipmentProvider.type, updateDto.config);

        // Mã hóa dữ liệu mới
        shipmentProvider.key = this.encryptionService.encrypt(updateDto.config);
      }

      // Lưu vào database
      const updatedProvider = await this.userProviderShipmentRepository.save(shipmentProvider);

      return this.mapToResponseDto(updatedProvider);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating shipment provider: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật cấu hình vận chuyển'
      );
    }
  }

  /**
   * Xóa cấu hình nhà cung cấp vận chuyển
   */
  async deleteShipmentProvider(userId: number, id: string): Promise<void> {
    try {
      const deleted = await this.userProviderShipmentRepository.deleteByIdAndUserId(id, userId);

      if (!deleted) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy cấu hình vận chuyển với ID ${id}`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting shipment provider: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa cấu hình vận chuyển'
      );
    }
  }

  /**
   * Test kết nối với nhà cung cấp vận chuyển
   */
  async testShipmentProvider(testDto: TestShipmentProviderDto): Promise<TestShipmentProviderResponseDto> {
    try {
      // Validate cấu hình
      this.validateProviderConfig(testDto.type, testDto.config);

      // Test kết nối theo từng type
      switch (testDto.type) {
        case ProviderShipmentType.GHN:
          return await this.testGHNConnection(testDto.config as any);
        case ProviderShipmentType.GHTK:
          return await this.testGHTKConnection(testDto.config as any);
        case ProviderShipmentType.AHAMOVE:
          return await this.testAhamoveConnection(testDto.config as any);
        case ProviderShipmentType.JT:
          return await this.testJTConnection(testDto.config as any);
        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Không hỗ trợ test kết nối cho nhà cung cấp ${testDto.type}`
          );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error testing shipment provider: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Lỗi khi test kết nối: ${error.message}`
      };
    }
  }

  /**
   * Validate cấu hình theo từng nhà cung cấp
   */
  private validateProviderConfig(type: ProviderShipmentType, config: any): void {
    switch (type) {
      case ProviderShipmentType.GHN:
        this.validateGHNConfig(config);
        break;
      case ProviderShipmentType.GHTK:
        this.validateGHTKConfig(config);
        break;
      case ProviderShipmentType.AHAMOVE:
        this.validateAhamoveConfig(config);
        break;
      case ProviderShipmentType.JT:
        this.validateJTConfig(config);
        break;
      default:
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Loại nhà cung cấp ${type} không được hỗ trợ`
        );
    }
  }

  /**
   * Validate cấu hình GHN
   */
  private validateGHNConfig(config: GHNProviderConfig): void {
    if (!config.token || !config.shopId) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'GHN yêu cầu token và shopId'
      );
    }
  }

  /**
   * Validate cấu hình GHTK
   */
  private validateGHTKConfig(config: GHTKProviderConfig): void {
    if (!config.token) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'GHTK yêu cầu token'
      );
    }
  }

  /**
   * Validate cấu hình Ahamove
   */
  private validateAhamoveConfig(config: AhamoveProviderConfig): void {
    if (!config.apiKey || !config.mobile) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Ahamove yêu cầu apiKey và mobile'
      );
    }
  }

  /**
   * Validate cấu hình J&T
   */
  private validateJTConfig(config: JTProviderConfig): void {
    if (!config.username || !config.apiKey) {
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'J&T yêu cầu username và apiKey'
      );
    }
  }

  /**
   * Map entity sang response DTO
   */
  private mapToResponseDto(entity: UserProviderShipment): ShipmentProviderResponseDto {
    // Giải mã và ẩn thông tin nhạy cảm
    let maskedConfig: any = {};

    try {
      const decryptedConfig = this.encryptionService.decrypt(entity.key);
      maskedConfig = this.maskSensitiveData(entity.type, decryptedConfig);
    } catch (error) {
      this.logger.warn(`Failed to decrypt config for ${entity.id}: ${error.message}`);
      maskedConfig = { error: 'Failed to decrypt configuration' };
    }

    return {
      id: entity.id,
      userId: entity.userId,
      name: entity.name,
      type: entity.type,
      config: maskedConfig,
      createdAt: entity.createdAt
    };
  }

  /**
   * Ẩn thông tin nhạy cảm trong config sử dụng DataMaskingUtil
   */
  private maskSensitiveData(type: ProviderShipmentType, config: any): any {
    // Sử dụng auto masking để tự động detect và mask các field nhạy cảm
    const autoMasked = DataMaskingUtil.autoMaskObject(config);

    // Thêm masking cụ thể cho từng provider nếu cần
    switch (type) {
      case ProviderShipmentType.GHN:
        if (autoMasked.token) {
          autoMasked.token = DataMaskingUtil.maskToken(autoMasked.token);
        }
        if (autoMasked.shopId) {
          autoMasked.shopId = DataMaskingUtil.maskShopId(autoMasked.shopId);
        }
        break;
      case ProviderShipmentType.GHTK:
        if (autoMasked.token) {
          autoMasked.token = DataMaskingUtil.maskToken(autoMasked.token);
        }
        if (autoMasked.partnerCode) {
          autoMasked.partnerCode = DataMaskingUtil.maskSensitiveData(autoMasked.partnerCode);
        }
        break;
      case ProviderShipmentType.AHAMOVE:
        if (autoMasked.apiKey) {
          autoMasked.apiKey = DataMaskingUtil.maskApiKey(autoMasked.apiKey);
        }
        if (autoMasked.token) {
          autoMasked.token = DataMaskingUtil.maskToken(autoMasked.token);
        }
        if (autoMasked.refreshToken) {
          autoMasked.refreshToken = DataMaskingUtil.maskToken(autoMasked.refreshToken);
        }
        if (autoMasked.mobile) {
          autoMasked.mobile = DataMaskingUtil.maskPhoneNumber(autoMasked.mobile);
        }
        break;
      case ProviderShipmentType.JT:
        if (autoMasked.apiKey) {
          autoMasked.apiKey = DataMaskingUtil.maskApiKey(autoMasked.apiKey);
        }
        if (autoMasked.secretKey) {
          autoMasked.secretKey = DataMaskingUtil.maskSensitiveData(autoMasked.secretKey);
        }
        break;
    }

    return autoMasked;
  }



  /**
   * Test connection methods - delegate to validation services
   */
  private async testGHNConnection(config: GHNProviderConfig): Promise<TestShipmentProviderResponseDto> {
    return await this.ghnValidationService.testConnection(config);
  }

  private async testGHTKConnection(config: GHTKProviderConfig): Promise<TestShipmentProviderResponseDto> {
    return await this.ghtkValidationService.testConnection(config);
  }

  private async testAhamoveConnection(config: AhamoveProviderConfig): Promise<TestShipmentProviderResponseDto> {
    return await this.ahamoveValidationService.testConnection(config);
  }

  private async testJTConnection(config: JTProviderConfig): Promise<TestShipmentProviderResponseDto> {
    return await this.jtValidationService.testConnection(config);
  }
}
