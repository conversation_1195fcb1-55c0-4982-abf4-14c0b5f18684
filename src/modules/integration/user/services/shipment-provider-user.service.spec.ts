import { Test, TestingModule } from '@nestjs/testing';
import { ShipmentProviderUserService } from './shipment-provider-user.service';
import { UserProviderShipmentRepository } from '../../repositories';
import { EncryptionService } from '../../services/encryption.service';
import { UserProviderShipment } from '../../entities/user-provider-shipment.entity';
import { ProviderShipmentType } from '../../constants/provider-shipment-type.enum';

describe('ShipmentProviderUserService - Data Masking', () => {
  let service: ShipmentProviderUserService;
  let mockRepository: jest.Mocked<UserProviderShipmentRepository>;
  let mockEncryptionService: jest.Mocked<EncryptionService>;

  beforeEach(async () => {
    const mockRepo = {
      findByIdAndUserId: jest.fn(),
      findByUserIdWithPagination: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      deleteByIdAndUserId: jest.fn(),
      existsByTypeAndUserId: jest.fn(),
    };

    const mockEncryption = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ShipmentProviderUserService,
        {
          provide: UserProviderShipmentRepository,
          useValue: mockRepo,
        },
        {
          provide: EncryptionService,
          useValue: mockEncryption,
        },
      ],
    }).compile();

    service = module.get<ShipmentProviderUserService>(ShipmentProviderUserService);
    mockRepository = module.get(UserProviderShipmentRepository);
    mockEncryptionService = module.get(EncryptionService);
  });

  describe('Data Masking in Response DTOs', () => {
    it('should mask GHN provider sensitive data correctly', async () => {
      // Arrange
      const mockEntity: UserProviderShipment = {
        id: 'test-id',
        userId: 1,
        name: 'GHN Config',
        type: ProviderShipmentType.GHN,
        key: 'encrypted-data',
        createdAt: Date.now(),
        user: null,
      };

      const mockDecryptedConfig = {
        token: 'ghn_token_1234567890abcdef',
        shopId: 'shop_987654321',
        environment: 'production'
      };

      mockRepository.findByIdAndUserId.mockResolvedValue(mockEntity);
      mockEncryptionService.decrypt.mockReturnValue(mockDecryptedConfig);

      // Act
      const result = await service.findShipmentProviderById(1, 'test-id');

      // Assert
      expect(result.config.token).toBe('ghn_**********cdef');
      expect(result.config.shopId).toBe('shop******21');
      expect(result.config.environment).toBe('production'); // Non-sensitive field should not be masked
    });

    it('should mask GHTK provider sensitive data correctly', async () => {
      // Arrange
      const mockEntity: UserProviderShipment = {
        id: 'test-id',
        userId: 1,
        name: 'GHTK Config',
        type: ProviderShipmentType.GHTK,
        key: 'encrypted-data',
        createdAt: Date.now(),
        user: null,
      };

      const mockDecryptedConfig = {
        apiToken: 'ghtk_api_token_1234567890',
        partnerCode: 'partner_code_abcdef',
        environment: 'sandbox'
      };

      mockRepository.findByIdAndUserId.mockResolvedValue(mockEntity);
      mockEncryptionService.decrypt.mockReturnValue(mockDecryptedConfig);

      // Act
      const result = await service.findShipmentProviderById(1, 'test-id');

      // Assert
      expect(result.config.apiToken).toBe('ghtk**********90');
      expect(result.config.partnerCode).toBe('part**********ef');
      expect(result.config.environment).toBe('sandbox');
    });

    it('should mask Ahamove provider sensitive data correctly', async () => {
      // Arrange
      const mockEntity: UserProviderShipment = {
        id: 'test-id',
        userId: 1,
        name: 'Ahamove Config',
        type: ProviderShipmentType.AHAMOVE,
        key: 'encrypted-data',
        createdAt: Date.now(),
        user: null,
      };

      const mockDecryptedConfig = {
        apiKey: 'ahamove_api_key_1234567890',
        token: 'access_token_abcdefghijk',
        refreshToken: 'refresh_token_xyz123456',
        mobile: '**********',
        environment: 'production'
      };

      mockRepository.findByIdAndUserId.mockResolvedValue(mockEntity);
      mockEncryptionService.decrypt.mockReturnValue(mockDecryptedConfig);

      // Act
      const result = await service.findShipmentProviderById(1, 'test-id');

      // Assert
      expect(result.config.apiKey).toBe('aham**********90');
      expect(result.config.token).toBe('acce**********jk');
      expect(result.config.refreshToken).toBe('refr**********56');
      expect(result.config.mobile).toBe('012***6789');
      expect(result.config.environment).toBe('production');
    });

    it('should mask J&T provider sensitive data correctly', async () => {
      // Arrange
      const mockEntity: UserProviderShipment = {
        id: 'test-id',
        userId: 1,
        name: 'J&T Config',
        type: ProviderShipmentType.JT,
        key: 'encrypted-data',
        createdAt: Date.now(),
        user: null,
      };

      const mockDecryptedConfig = {
        username: 'jt_username',
        apiKey: 'jt_api_key_1234567890abcdef',
        secretKey: 'jt_secret_key_xyz123456789',
        environment: 'production'
      };

      mockRepository.findByIdAndUserId.mockResolvedValue(mockEntity);
      mockEncryptionService.decrypt.mockReturnValue(mockDecryptedConfig);

      // Act
      const result = await service.findShipmentProviderById(1, 'test-id');

      // Assert
      expect(result.config.username).toBe('jt_username'); // Username is not auto-detected as sensitive
      expect(result.config.apiKey).toBe('jt_a**********ef');
      expect(result.config.secretKey).toBe('jt_s**********89');
      expect(result.config.environment).toBe('production');
    });

    it('should handle decryption errors gracefully', async () => {
      // Arrange
      const mockEntity: UserProviderShipment = {
        id: 'test-id',
        userId: 1,
        name: 'Test Config',
        type: ProviderShipmentType.GHN,
        key: 'invalid-encrypted-data',
        createdAt: Date.now(),
        user: null,
      };

      mockRepository.findByIdAndUserId.mockResolvedValue(mockEntity);
      mockEncryptionService.decrypt.mockImplementation(() => {
        throw new Error('Decryption failed');
      });

      // Act
      const result = await service.findShipmentProviderById(1, 'test-id');

      // Assert
      expect(result.config).toEqual({ error: 'Failed to decrypt configuration' });
    });

    it('should mask data in paginated results', async () => {
      // Arrange
      const mockEntities: UserProviderShipment[] = [
        {
          id: 'test-id-1',
          userId: 1,
          name: 'GHN Config',
          type: ProviderShipmentType.GHN,
          key: 'encrypted-data-1',
          createdAt: Date.now(),
          user: null,
        },
        {
          id: 'test-id-2',
          userId: 1,
          name: 'GHTK Config',
          type: ProviderShipmentType.GHTK,
          key: 'encrypted-data-2',
          createdAt: Date.now(),
          user: null,
        }
      ];

      const mockPaginatedResult = {
        items: mockEntities,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1
        }
      };

      mockRepository.findByUserIdWithPagination.mockResolvedValue(mockPaginatedResult);
      mockEncryptionService.decrypt
        .mockReturnValueOnce({ token: 'ghn_token_1234567890' })
        .mockReturnValueOnce({ apiToken: 'ghtk_api_token_1234567890' });

      // Act
      const result = await service.findUserShipmentProviders(1, { page: 1, limit: 10 });

      // Assert
      expect(result.items).toHaveLength(2);
      expect(result.items[0].config.token).toBe('ghn_******90');
      expect(result.items[1].config.apiToken).toBe('ghtk******90');
    });
  });
});
