import { Injectable, Logger } from '@nestjs/common';
import {
  PaymentGatewayRepository,
  UserCompanyInSepayRepository,
} from '../../repositories';
import { SepayHubService } from '@shared/services/sepay-hub';
import { RedisService } from '@shared/services/redis.service';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  AccountLookupRequestDto,
  BankAccountConfirmRequestDto,
  BankAccountCreateRequestDto,
  BankAccountDto,
  BankDto,
  CompanyCreateRequestDto,
  CompanyDto,
  CreateVARequestDto,
  OtpRequestDto
} from '@shared/interface/sepay-hub';
import {
  AccountLookupDto,
  BankAccountResponseDto,
  BankListResponseDto,
  CompanyResponseDto,
  ConfirmDeleteBankAccountDto,
  CreateBankAccountDto,
  CreateBankAccountIndividualDto,
  CreateCompanyDto,
  EligibleVAAccountResponseDto,
  OtpConfirmDto
} from '../dto';
import { PaymentGateway, UserCompanyInSepay } from '../../entities';

@Injectable()
export class PaymentGatewayUserService {
  private readonly logger = new Logger(PaymentGatewayUserService.name);

  constructor(
    private readonly paymentGatewayRepository: PaymentGatewayRepository,
    private readonly userCompanyInSepayRepository: UserCompanyInSepayRepository,
    private readonly sepayHubService: SepayHubService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Lấy danh sách ngân hàng
   * @returns Danh sách ngân hàng
   */
  async getBanks(): Promise<BankListResponseDto[]> {
    try {
      const banks = await this.sepayHubService.getBanks();

      return banks.map(bank => ({
        id: bank.id,
        brandName: bank.brand_name,
        fullName: bank.full_name,
        shortName: bank.short_name,
        code: bank.code,
        bin: bank.bin,
        logoPath: bank.logo_path,
        icon: bank.icon,
        active: bank.active === '1'
      }));
    } catch (error) {
      this.logger.error(`Error getting banks: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách ngân hàng'
      );
    }
  }

  /**
   * Tra cứu thông tin tài khoản ngân hàng
   * @param accountLookupDto Thông tin tài khoản cần tra cứu
   * @returns Thông tin chủ tài khoản
   */
  async lookupAccountHolder(accountLookupDto: AccountLookupDto): Promise<{ accountHolderName: string }> {
    try {
      const request: AccountLookupRequestDto = {
        bank_id: accountLookupDto.bankId,
        account_number: accountLookupDto.accountNumber
      };

      // Kiểm tra ngân hàng để gọi API phù hợp
      const bank = await this.sepayHubService.getBanks().then(
        banks => banks.find(b => b.id === accountLookupDto.bankId)
      );

      if (!bank) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không hợp lệ'
        );
      }

      let result;
      if (bank.code === 'MB') {
        result = await this.sepayHubService.getAccountHolderNameMB(request);
      } else if (bank.code === 'OCB') {
        result = await this.sepayHubService.lookUpAccountHolderNameOCB(request);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ tra cứu tên chủ tài khoản'
        );
      }

      return {
        accountHolderName: result.account_holder_name
      };
    } catch (error) {
      this.logger.error(`Error looking up account holder: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tra cứu thông tin tài khoản ngân hàng'
      );
    }
  }

  /**
   * Tạo công ty mới
   * @param userId ID của người dùng
   * @param createCompanyDto Thông tin công ty cần tạo
   * @returns Thông tin công ty đã tạo
   */
  async createCompany(userId: number, createCompanyDto: CreateCompanyDto): Promise<CompanyResponseDto> {
    try {
      // Kiểm tra xem người dùng đã có công ty chưa
      const existingCompany = await this.userCompanyInSepayRepository.findOne({
        where: { userId }
      });

      if (existingCompany) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Người dùng đã có công ty trên SePay Hub'
        );
      }

      // Tạo request để gửi đến SePay Hub
      const request: CompanyCreateRequestDto = {
        full_name: createCompanyDto.fullName,
        short_name: createCompanyDto.shortName
      };

      // Gọi API tạo công ty
      const company = await this.sepayHubService.createCompany(request);

      // Lưu thông tin công ty vào database
      const userCompanyInSepay = this.userCompanyInSepayRepository.create({
        userId,
        companyId: company.id,
        createdAt: Math.floor(Date.now() / 1000),
        updatedAt: Math.floor(Date.now() / 1000)
      });

      await this.userCompanyInSepayRepository.save(userCompanyInSepay);

      // Trả về thông tin công ty
      return {
        id: company.id,
        fullName: company.full_name,
        shortName: company.short_name,
        status: company.status,
        createdAt: company.created_at,
        updatedAt: company.updated_at
      };
    } catch (error) {
      this.logger.error(`Error creating company: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo công ty mới'
      );
    }
  }

  /**
   * Lấy thông tin công ty của người dùng
   * @param userId ID của người dùng
   * @returns Thông tin công ty
   */
  async getCompany(userId: number): Promise<CompanyResponseDto> {
    try {
      // Lấy thông tin công ty từ database
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Người dùng chưa có công ty trên SePay Hub'
        );
      }

      // Lấy thông tin chi tiết từ SePay Hub
      const company = await this.sepayHubService.getCompanyDetails(userCompanyInSepay.companyId);

      // Trả về thông tin công ty
      return {
        id: company.id,
        fullName: company.full_name,
        shortName: company.short_name,
        status: company.status,
        createdAt: company.created_at,
        updatedAt: company.updated_at
      };
    } catch (error) {
      this.logger.error(`Error getting company: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin công ty'
      );
    }
  }

  /**
   * Tạo tài khoản ngân hàng mới
   * @param userId ID của người dùng
   * @param createBankAccountDto Thông tin tài khoản ngân hàng cần tạo
   * @returns Thông tin tài khoản ngân hàng đã tạo và request ID
   */
  async createBankAccount(
    userId: number,
    createBankAccountDto: CreateBankAccountDto | CreateBankAccountIndividualDto
  ): Promise<{ bankAccount: BankAccountResponseDto; requestId?: string }> {
    try {
      // Lấy thông tin công ty từ database
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Người dùng chưa có công ty trên SePay Hub'
        );
      }

      // Kiểm tra ngân hàng
      const bank = await this.sepayHubService.getBanks().then(
        banks => banks.find(b => b.id === createBankAccountDto.bankId)
      );

      if (!bank) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không hợp lệ'
        );
      }

      // Tạo request cơ bản để gửi đến SePay Hub
      const request: any = {
        company_id: userCompanyInSepay.companyId,
        bank_id: createBankAccountDto.bankId,
        account_holder_name: createBankAccountDto.accountHolderName,
        account_number: createBankAccountDto.accountNumber,
        label: createBankAccountDto.label
      };

      // Kiểm tra xem có phải là DTO mở rộng không
      const individualDto = createBankAccountDto as CreateBankAccountIndividualDto;
      if (individualDto.identificationNumber) {
        request.identification_number = individualDto.identificationNumber;
      }
      if (individualDto.phoneNumber) {
        request.phone_number = individualDto.phoneNumber;
      }

      // Gọi API tạo tài khoản ngân hàng
      let response: any;
      if (bank.code === 'MB') {
        response = await this.sepayHubService.createBankAccountMB(request);
      } else if (bank.code === 'OCB') {
        response = await this.sepayHubService.createBankAccountOCB(request);
      } else if (bank.code === 'ACB') {
        response = await this.sepayHubService.createBankAccountACB(request);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ'
        );
      }

      // Lưu thông tin tài khoản ngân hàng vào database
      const paymentGateway = this.paymentGatewayRepository.create({
        accountId: response.data.bank_account.id,
        companyId: parseInt(userCompanyInSepay.companyId),
        bankCode: bank.code,
        accountNumber: createBankAccountDto.accountNumber,
        accountHolderName: createBankAccountDto.accountHolderName,
        label: createBankAccountDto.label,
        status: 'ACTIVE',
        requestId: response.data.request_id,
        isVa: false,
        canCreateVa: bank.code === 'OCB'
      });

      // Thêm các trường bổ sung nếu có
      if (individualDto.identificationNumber) {
        paymentGateway.identificationNumber = individualDto.identificationNumber;
      }
      if (individualDto.phoneNumber) {
        paymentGateway.phoneNumber = individualDto.phoneNumber;
      }
      if (individualDto.merchantName) {
        paymentGateway.merchantName = individualDto.merchantName;
      }
      if (individualDto.merchantAddress) {
        paymentGateway.merchantAddress = individualDto.merchantAddress;
      }

      await this.paymentGatewayRepository.save(paymentGateway);

      // Trả về thông tin tài khoản ngân hàng
      const bankAccount = this.mapToBankAccountResponseDto(response.data.bank_account);

      return {
        bankAccount,
        requestId: response.data.request_id
      };
    } catch (error) {
      this.logger.error(`Error creating bank account: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi tạo tài khoản ngân hàng mới'
      );
    }
  }

  /**
   * Xác nhận kết nối API ngân hàng
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @param requestId ID của yêu cầu
   * @param otpConfirmDto Thông tin OTP
   * @returns Kết quả xác nhận
   */
  async confirmBankAccountConnection(
    userId: number,
    bankAccountId: string,
    requestId: string,
    otpConfirmDto: OtpConfirmDto
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Tạo request để gửi đến SePay Hub
      const request: BankAccountConfirmRequestDto = {
        otp: otpConfirmDto.otp
      };

      // Gọi API xác nhận kết nối
      let result: any;
      if (paymentGateway.bankCode === 'MB') {
        result = await this.sepayHubService.confirmBankAccountConnectionMB(requestId, request);
      } else if (paymentGateway.bankCode === 'ACB') {
        result = await this.sepayHubService.confirmApiConnectionACB(requestId, request);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ xác nhận kết nối API'
        );
      }

      // Cập nhật trạng thái tài khoản ngân hàng
      if (result.success) {
        paymentGateway.status = 'CONNECTED';
        await this.paymentGatewayRepository.save(paymentGateway);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error confirming bank account connection: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xác nhận kết nối API ngân hàng'
      );
    }
  }

  /**
   * Lấy danh sách tài khoản ngân hàng của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản ngân hàng
   */
  async getBankAccounts(userId: number): Promise<BankAccountResponseDto[]> {
    try {
      // Lấy thông tin công ty từ database
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId }
      });

      if (!userCompanyInSepay) {
        return [];
      }

      // Lấy danh sách tài khoản ngân hàng từ SePay Hub
      const { data: bankAccounts } = await this.sepayHubService.getBankAccounts({
        company_id: userCompanyInSepay.companyId
      });

      // Trả về danh sách tài khoản ngân hàng
      return bankAccounts.map(bankAccount => this.mapToBankAccountResponseDto(bankAccount));
    } catch (error) {
      this.logger.error(`Error getting bank accounts: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách tài khoản ngân hàng'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết tài khoản ngân hàng
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Thông tin chi tiết tài khoản ngân hàng
   */
  async getBankAccountDetails(userId: number, bankAccountId: string): Promise<BankAccountResponseDto> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Lấy thông tin chi tiết từ SePay Hub
      const bankAccount = await this.sepayHubService.getBankAccountDetails(bankAccountId);

      // Trả về thông tin tài khoản ngân hàng
      return this.mapToBankAccountResponseDto(bankAccount);
    } catch (error) {
      this.logger.error(`Error getting bank account details: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin chi tiết tài khoản ngân hàng'
      );
    }
  }

  /**
   * Yêu cầu kết nối API ngân hàng
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns ID của yêu cầu
   */
  async requestBankAccountConnection(userId: number, bankAccountId: string): Promise<{ requestId: string; message: string }> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Gọi API yêu cầu kết nối
      let result: any;
      if (paymentGateway.bankCode === 'MB') {
        result = await this.sepayHubService.requestApiConnectionMB(bankAccountId);
      } else if (paymentGateway.bankCode === 'ACB') {
        result = await this.sepayHubService.requestApiConnectionACB(bankAccountId);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ yêu cầu kết nối API'
        );
      }

      // Cập nhật requestId
      paymentGateway.requestId = result.request_id;
      await this.paymentGatewayRepository.save(paymentGateway);

      return result;
    } catch (error) {
      this.logger.error(`Error requesting bank account connection: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi yêu cầu kết nối API ngân hàng'
      );
    }
  }

  /**
   * Yêu cầu xóa tài khoản ngân hàng
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @returns Kết quả yêu cầu xóa
   */
  async requestDeleteBankAccount(userId: number, bankAccountId: string): Promise<{ message: string; requestId?: string }> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Xử lý theo loại ngân hàng
      if (paymentGateway.bankCode === 'MB') {
        // Kiểm tra trạng thái tài khoản
        if (paymentGateway.status === 'ACTIVE' || paymentGateway.status === 'CHUA_XAC_THUC') {
          // Xóa trực tiếp nếu chưa xác thực
          const response = await this.sepayHubService.forceDeleteMB(bankAccountId);
          if (response.success) {
            // Xóa tài khoản khỏi database
            await this.paymentGatewayRepository.delete(paymentGateway.id);
            return {
              message: 'Xóa tài khoản thành công'
            };
          } else {
            throw new AppException(
              ErrorCode.EXTERNAL_SERVICE_ERROR,
              'Không thể xóa tài khoản ngân hàng'
            );
          }
        } else {
          // Yêu cầu xóa nếu đã xác thực
          const result = await this.sepayHubService.requestDeleteMB(bankAccountId);

          // Lưu requestId vào Redis
          const redisKey = `DELETE_REQUEST_ID_${bankAccountId}`;
          await this.redisService.setWithExpiry(redisKey, result.request_id, 15 * 60); // 15 phút

          return {
            message: 'Đã gửi yêu cầu xóa thành công, hãy xác nhận OTP',
            requestId: result.request_id
          };
        }
      } else if (paymentGateway.bankCode === 'ACB') {
        const result = await this.sepayHubService.requestDeleteApiConnectionACB(bankAccountId);

        // Lưu requestId vào Redis
        const redisKey = `DELETE_REQUEST_ID_${bankAccountId}`;
        await this.redisService.setWithExpiry(redisKey, result.request_id, 15 * 60); // 15 phút

        return {
          message: 'Đã gửi yêu cầu xóa thành công, hãy xác nhận OTP',
          requestId: result.request_id
        };
      } else if (paymentGateway.bankCode === 'OCB') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng OCB không hỗ trợ tính năng xóa tài khoản'
        );
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ'
        );
      }
    } catch (error) {
      this.logger.error(`Error requesting delete bank account: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi yêu cầu xóa tài khoản ngân hàng'
      );
    }
  }

  /**
   * Xác nhận xóa tài khoản ngân hàng
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @param confirmDeleteDto Thông tin xác nhận xóa
   * @returns Kết quả xác nhận xóa
   */
  async confirmDeleteBankAccount(
    userId: number,
    bankAccountId: string,
    confirmDeleteDto: ConfirmDeleteBankAccountDto
  ): Promise<{ message: string }> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Kiểm tra trạng thái tài khoản
      if (paymentGateway.status !== 'CONNECTED' && paymentGateway.status !== 'DA_XAC_THUC') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tài khoản ngân hàng chưa được xác thực'
        );
      }

      // Lấy requestId từ Redis
      const redisKey = `DELETE_REQUEST_ID_${bankAccountId}`;
      const requestId = await this.redisService.get(redisKey);

      if (!requestId) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Yêu cầu xóa đã hết hạn hoặc không tồn tại'
        );
      }

      // Tạo request OTP
      const otpRequest: OtpRequestDto = {
        otp: confirmDeleteDto.otp
      };

      // Xử lý theo loại ngân hàng
      let result: { success: boolean; message: string };
      if (paymentGateway.bankCode === 'MB') {
        result = await this.sepayHubService.confirmDeleteMB(requestId, otpRequest);
      } else if (paymentGateway.bankCode === 'ACB') {
        result = await this.sepayHubService.confirmDeleteApiConnectionACB(requestId, otpRequest);
      } else {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không được hỗ trợ'
        );
      }

      // Xóa requestId khỏi Redis
      await this.redisService.del(redisKey);

      // Xóa tài khoản khỏi database nếu xác nhận thành công
      if (result.success) {
        await this.paymentGatewayRepository.delete(paymentGateway.id);
        return {
          message: 'Đã hủy liên kết API thành công'
        };
      } else {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          result.message || 'Không thể xóa tài khoản ngân hàng'
        );
      }
    } catch (error) {
      this.logger.error(`Error confirming delete bank account: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xác nhận xóa tài khoản ngân hàng'
      );
    }
  }

  /**
   * Yêu cầu liên kết tài khoản VA
   * @param userId ID của người dùng
   * @param bankAccountId ID của tài khoản ngân hàng
   * @param email Email nhận thông báo
   * @returns Kết quả yêu cầu liên kết
   */
  async requestVAConnection(
    userId: number,
    bankAccountId: string,
    email: string
  ): Promise<{ message: string; requestId?: string }> {
    try {
      // Lấy thông tin tài khoản ngân hàng từ database
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { accountId: bankAccountId }
      });

      if (!paymentGateway) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản ngân hàng'
        );
      }

      // Kiểm tra xem tài khoản ngân hàng có thuộc về người dùng không
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          ErrorCode.UNAUTHORIZED_ACCESS,
          'Không có quyền truy cập tài khoản ngân hàng này'
        );
      }

      // Kiểm tra xem ngân hàng có hỗ trợ tài khoản VA không
      if (paymentGateway.bankCode !== 'OCB') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Ngân hàng không hỗ trợ tài khoản VA'
        );
      }

      // Kiểm tra trạng thái tài khoản
      if (paymentGateway.status !== 'ACTIVE' && paymentGateway.status !== 'DA_XAC_THUC') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tài khoản ngân hàng chưa được xác thực'
        );
      }

      // Tạo VA ngẫu nhiên (10 chữ số)
      const va = Math.floor(********** + Math.random() * **********).toString();

      // Tạo request để gửi đến SePay Hub
      const request: CreateVARequestDto = {
        bank_account_id: bankAccountId,
        label: `VA-${va}`,
        email: email,
        merchant_name: paymentGateway.merchantName || '',
        merchant_address: paymentGateway.merchantAddress || '',
        company_id: userCompanyInSepay.companyId,
        va: va
      };

      // Gọi API tạo tài khoản VA
      const response = await this.sepayHubService.requestCreateVAOCB(request);

      // Cập nhật requestId
      paymentGateway.requestId = response.request_id;
      await this.paymentGatewayRepository.save(paymentGateway);

      return {
        message: 'Đã gửi yêu cầu liên kết tài khoản VA thành công, hãy xác nhận OTP',
        requestId: response.request_id
      };
    } catch (error) {
      this.logger.error(`Error requesting VA connection: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi yêu cầu liên kết tài khoản VA'
      );
    }
  }

  /**
   * Lấy danh sách tài khoản ngân hàng đủ điều kiện tạo tài khoản VA
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản ngân hàng đủ điều kiện
   */
  async getEligibleVAAccounts(userId: number): Promise<EligibleVAAccountResponseDto[]> {
    try {
      // Lấy danh sách tài khoản ngân hàng đủ điều kiện
      const accounts = await this.paymentGatewayRepository.getEligibleVAAccounts(userId);

      // Lấy danh sách ngân hàng
      const banks = await this.sepayHubService.getBanks();

      // Chuyển đổi sang DTO
      return accounts.map(account => {
        const bank = banks.find(b => b.code === account.bankCode);
        return {
          id: account.id,
          accountId: account.accountId,
          iconBank: bank?.logo_path || '',
          bankCode: account.bankCode,
          accountNumber: account.accountNumber,
          accountName: account.accountHolderName,
          status: account.status,
          isVA: account.isVa,
          isDelete: account.bankCode !== 'OCB'
        };
      });
    } catch (error) {
      this.logger.error(`Error getting eligible VA accounts: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách tài khoản đủ điều kiện tạo tài khoản VA'
      );
    }
  }

  /**
   * Lấy danh sách tài khoản ngân hàng đủ điều kiện liên kết với chatbot
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản ngân hàng đủ điều kiện
   */
  async getEligiblePaymentAccountsForChatBot(userId: number): Promise<EligibleVAAccountResponseDto[]> {
    try {
      // Lấy danh sách tài khoản ngân hàng đủ điều kiện
      const accounts = await this.paymentGatewayRepository.findActiveByUserId(userId, 'DA_XAC_THUC');

      // Lấy danh sách ngân hàng
      const banks = await this.sepayHubService.getBanks();

      // Chuyển đổi sang DTO
      return accounts.map(account => {
        const bank = banks.find(b => b.code === account.bankCode);
        return {
          id: account.id,
          accountId: account.accountId,
          iconBank: bank?.logo_path || '',
          bankCode: account.bankCode,
          accountNumber: account.accountNumber,
          accountName: account.accountHolderName,
          status: account.status,
          isVA: account.isVa,
          isDelete: account.bankCode !== 'OCB'
        };
      });
    } catch (error) {
      this.logger.error(`Error getting eligible payment accounts for chatbot: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách tài khoản đủ điều kiện liên kết với chatbot'
      );
    }
  }

  /**
   * Chuyển đổi từ BankAccountDto sang BankAccountResponseDto
   * @param bankAccount BankAccountDto
   * @returns BankAccountResponseDto
   */
  private mapToBankAccountResponseDto(bankAccount: BankAccountDto): BankAccountResponseDto {
    return {
      id: bankAccount.id,
      companyId: bankAccount.company_id,
      bankId: bankAccount.bank_id,
      accountHolderName: bankAccount.account_holder_name,
      accountNumber: bankAccount.account_number,
      accumulated: bankAccount.accumulated,
      label: bankAccount.label,
      bankApiConnected: bankAccount.bank_api_connected === '1',
      lastTransaction: bankAccount.last_transaction,
      createdAt: bankAccount.created_at,
      updatedAt: bankAccount.updated_at
    };
  }
}