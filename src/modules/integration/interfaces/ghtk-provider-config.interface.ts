/**
 * Interface cho GHTK (Giao Hàng Tiết Kiệm)
 * <PERSON><PERSON><PERSON> cầu: Token và Partner Code
 */
export interface GHTKProviderConfig {
  /** Token từ GHTK dashboard */
  token: string;
  /** Partner Code (mã đối tác) - optional */
  partnerCode?: string;
  /** M<PERSON>i trường (staging/production) */
  environment?: 'staging' | 'production';
  /** Tên hiển thị của cấu hình */
  name?: string;
  /** Trạng thái kích hoạt */
  isActive?: boolean;
  /** Ghi chú */
  notes?: string;
}
