import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { HttpClientService } from '../http-client.service';
import { JTProviderConfig } from '../../interfaces/jt-provider-config.interface';
import { TestShipmentProviderResponseDto } from '../../user/dto/shipment';
import {
  getProviderBaseUrl,
  getProviderTimeout,
  PROVIDER_TEST_ENDPOINTS,
  PROVIDER_TEST_DATA
} from '../../constants/provider-config';

/**
 * Service validation cho J&T Express
 */
@Injectable()
export class JTValidationService {
  private readonly logger = new Logger(JTValidationService.name);
  private readonly baseUrl: string;
  private readonly timeout: number;

  constructor(
    private readonly httpClient: HttpClientService,
    private readonly configService: ConfigService,
  ) {
    // J&T chỉ có 1 environment
    const envUrl = this.configService.get<string>('JT_BASE_URL');
    this.baseUrl = envUrl || getProviderBaseUrl('JT');
    this.timeout = getProviderTimeout('JT');

    this.logger.log(`J&T Validation Service initialized with URL: ${this.baseUrl}`);
  }

  /**
   * Test connection với J&T API
   */
  async testConnection(config: JTProviderConfig): Promise<TestShipmentProviderResponseDto> {
    try {
      this.logger.debug('Testing J&T connection', { username: config.username });

      // Validate input
      if (!config.username || !config.apiKey) {
        return {
          success: false,
          message: 'Username và API Key là bắt buộc cho J&T Express',
        };
      }

      // Test bằng cách gọi API check tariff
      const tariffResponse = await this.checkTariff(config);

      if (!tariffResponse.success) {
        return tariffResponse;
      }

      this.logger.log('J&T connection test successful', {
        username: config.username
      });

      return {
        success: true,
        message: `Kết nối J&T Express thành công. Username: ${config.username}`,
        data: tariffResponse.data,
      };

    } catch (error) {
      this.logger.error('J&T connection test failed', error);

      return {
        success: false,
        message: `Lỗi kết nối J&T Express: ${error.message}`,
      };
    }
  }

  /**
   * Test bằng cách gọi API check tariff
   */
  private async checkTariff(config: JTProviderConfig): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.JT.CHECK_TARIFF}`;

      // Tạo test data
      const testData = {
        ...PROVIDER_TEST_DATA.JT.TEST_TARIFF_DATA,
        cusName: config.username,
      };

      // Tạo signature
      const dataString = JSON.stringify(testData);
      const signature = this.generateSignature(dataString, config.apiKey);

      // Tạo form data
      const formData = new URLSearchParams();
      formData.append('data', dataString);
      formData.append('sign', signature);

      const response = await this.httpClient.postForm(url, formData, {
        timeout: this.timeout,
      });

      // Kiểm tra response structure theo J&T API format
      if (response.status === 200 && response.data?.is_success === 'true') {
        let tariffData = null;

        try {
          // Parse content nếu có
          if (response.data.content) {
            tariffData = JSON.parse(response.data.content);
          }
        } catch (parseError) {
          this.logger.warn('Could not parse J&T tariff content', parseError);
        }

        return {
          success: true,
          message: 'J&T API test successful',
          data: {
            tariff: tariffData,
            testData: testData,
          },
        };
      }

      // Handle J&T API errors
      const errorMessage = response.data?.message || 'Unknown J&T API error';

      this.logger.warn('J&T API returned error', {
        status: response.status,
        is_success: response.data?.is_success,
        message: errorMessage
      });

      // Map common J&T error messages
      if (errorMessage.includes('Invalid signature') || errorMessage.includes('sign')) {
        return {
          success: false,
          message: 'Chữ ký API không hợp lệ. Vui lòng kiểm tra API Key.',
        };
      }

      if (errorMessage.includes('Invalid customer') || errorMessage.includes('cusName')) {
        return {
          success: false,
          message: 'Username không hợp lệ hoặc không tồn tại.',
        };
      }

      return {
        success: false,
        message: `Lỗi J&T API: ${errorMessage}`,
      };

    } catch (error) {
      this.logger.error('Error calling J&T tariff API', error);

      // Handle network/timeout errors
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return {
          success: false,
          message: 'Hết thời gian chờ kết nối đến J&T Express',
        };
      }

      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Thông tin xác thực J&T không hợp lệ',
        };
      }

      if (error.response?.status >= 500) {
        return {
          success: false,
          message: 'Dịch vụ J&T Express đang bảo trì, vui lòng thử lại sau',
        };
      }

      return {
        success: false,
        message: `Lỗi kết nối J&T Express: ${error.message}`,
      };
    }
  }

  /**
   * Tạo signature cho J&T API theo documentation
   * Signature = base64(md5(data + apiKey))
   */
  private generateSignature(data: string, apiKey: string): string {
    try {
      // Kết hợp data và apiKey
      const combined = data + apiKey;

      // Tạo MD5 hash
      const md5Hash = crypto.createHash('md5').update(combined, 'utf8').digest('hex');

      // Encode base64
      const signature = Buffer.from(md5Hash).toString('base64');

      this.logger.debug('Generated J&T signature', {
        dataLength: data.length,
        signatureLength: signature.length
      });

      return signature;
    } catch (error) {
      this.logger.error('Error generating J&T signature', error);
      throw new Error(`Lỗi tạo chữ ký J&T: ${error.message}`);
    }
  }

  /**
   * Validate signature format (for testing)
   */
  validateSignatureFormat(signature: string): boolean {
    try {
      // Signature phải là base64 valid
      const decoded = Buffer.from(signature, 'base64').toString('hex');
      // MD5 hash luôn có độ dài 32 ký tự hex
      return decoded.length === 32;
    } catch (error) {
      return false;
    }
  }
}
