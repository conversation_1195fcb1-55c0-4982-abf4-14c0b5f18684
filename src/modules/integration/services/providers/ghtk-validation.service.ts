import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpClientService } from '../http-client.service';
import { GHTKProviderConfig } from '../../interfaces/ghtk-provider-config.interface';
import { TestShipmentProviderResponseDto } from '../../user/dto/shipment';
import {
  getProviderBaseUrl,
  getProviderTimeout,
  PROVIDER_TEST_ENDPOINTS
} from '../../constants/provider-config';

/**
 * Service validation cho GHTK (Giao Hàng T<PERSON>ết Kiệm)
 */
@Injectable()
export class GHTKValidationService {
  private readonly logger = new Logger(GHTKValidationService.name);
  private readonly baseUrl: string;
  private readonly timeout: number;

  constructor(
    private readonly httpClient: HttpClientService,
    private readonly configService: ConfigService,
  ) {
    // GHTK chỉ có 1 environment
    const envUrl = this.configService.get<string>('GHTK_BASE_URL');
    this.baseUrl = envUrl || getProviderBaseUrl('GHTK');
    this.timeout = getProviderTimeout('GHTK');

    this.logger.log(`GHTK Validation Service initialized with URL: ${this.baseUrl}`);
  }

  /**
   * Test connection với GHTK API
   */
  async testConnection(config: GHTKProviderConfig): Promise<TestShipmentProviderResponseDto> {
    try {
      this.logger.debug('Testing GHTK connection');

      // Validate input
      if (!config.token) {
        return {
          success: false,
          message: 'Token là bắt buộc cho GHTK',
        };
      }

      // Test bằng cách gọi API lấy danh sách địa chỉ pickup
      const pickupResponse = await this.getPickupAddresses(config);

      if (!pickupResponse.success) {
        return pickupResponse;
      }

      const pickupAddresses = pickupResponse.data || [];

      this.logger.log('GHTK connection test successful', {
        addressCount: pickupAddresses.length
      });

      return {
        success: true,
        message: `Kết nối GHTK thành công. Tìm thấy ${pickupAddresses.length} địa chỉ lấy hàng.`,
        data: {
          pickupAddresses: pickupAddresses.slice(0, 3), // Chỉ trả về 3 địa chỉ đầu
          totalAddresses: pickupAddresses.length,
        }
      };

    } catch (error) {
      this.logger.error('GHTK connection test failed', error);

      return {
        success: false,
        message: `Lỗi kết nối GHTK: ${error.message}`,
      };
    }
  }

  /**
   * Lấy danh sách địa chỉ pickup từ GHTK
   */
  private async getPickupAddresses(config: GHTKProviderConfig): Promise<{
    success: boolean;
    message: string;
    data?: any[];
  }> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.GHTK.GET_PICKUP_ADDRESSES}`;

      const headers: Record<string, string> = {
        'Token': config.token,
        'Content-Type': 'application/json',
      };

      // Thêm X-Client-Source nếu có
      if (config.partnerCode) {
        headers['X-Client-Source'] = config.partnerCode;
      }

      const response = await this.httpClient.get(url, {
        headers,
        timeout: this.timeout,
      });

      // Kiểm tra response structure theo GHTK API format
      if (response.status === 200 && response.data?.success === true) {
        return {
          success: true,
          message: 'Success',
          data: response.data.data || [],
        };
      }

      // Handle GHTK API errors
      const errorMessage = response.data?.message || 'Unknown GHTK API error';

      this.logger.warn('GHTK API returned error', {
        status: response.status,
        success: response.data?.success,
        message: errorMessage
      });

      return {
        success: false,
        message: `Lỗi GHTK API: ${errorMessage}`,
      };

    } catch (error) {
      this.logger.error('Error calling GHTK pickup addresses API', error);

      // Handle network/timeout errors
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        return {
          success: false,
          message: 'Hết thời gian chờ kết nối đến GHTK',
        };
      }

      if (error.response?.status === 401) {
        return {
          success: false,
          message: 'Token GHTK không hợp lệ',
        };
      }

      if (error.response?.status === 403) {
        return {
          success: false,
          message: 'Không có quyền truy cập GHTK API',
        };
      }

      if (error.response?.status >= 500) {
        return {
          success: false,
          message: 'Dịch vụ GHTK đang bảo trì, vui lòng thử lại sau',
        };
      }

      return {
        success: false,
        message: `Lỗi kết nối GHTK: ${error.message}`,
      };
    }
  }

  /**
   * Test thêm bằng cách gọi API tính phí (optional)
   */
  async testCalculateFee(config: GHTKProviderConfig): Promise<boolean> {
    try {
      const url = `${this.baseUrl}${PROVIDER_TEST_ENDPOINTS.GHTK.CALCULATE_FEE}`;

      // Test data cơ bản
      const testParams = {
        pick_province: 'Hà Nội',
        pick_district: 'Quận Hai Bà Trưng',
        province: 'Hà Nội',
        district: 'Quận Cầu Giấy',
        weight: 1000, // 1kg
        value: 100000, // 100k VND
        deliver_option: 'none',
      };

      const headers: Record<string, string> = {
        'Token': config.token,
        'Content-Type': 'application/json',
      };

      if (config.partnerCode) {
        headers['X-Client-Source'] = config.partnerCode;
      }

      const response = await this.httpClient.get(url, {
        headers,
        params: testParams,
        timeout: this.timeout,
      });

      return response.status === 200 && response.data?.success === true;
    } catch (error) {
      this.logger.debug('GHTK fee calculation test failed', error.message);
      return false;
    }
  }
}
