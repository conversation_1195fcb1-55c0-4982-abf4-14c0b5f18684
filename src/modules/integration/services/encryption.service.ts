import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * Service xử lý mã hóa/giải mã dữ liệu nhạy cảm
 * Sử dụng AES-256-GCM để mã hóa thông tin nhạy cảm của nhà cung cấp vận chuyển
 */
@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  private readonly algorithm = 'aes-256-gcm';
  private readonly secretKey: string;

  constructor(private readonly configService: ConfigService) {
    // Lấy secret key từ biến môi trường SHIPMENT_SECRET_KEY
    const secretKey = this.configService.get<string>('SHIPMENT_SECRET_KEY');

    if (!secretKey) {
      throw new Error('SHIPMENT_SECRET_KEY environment variable is required');
    }

    // Đ<PERSON>m bảo secret key có độ dài phù hợp (32 bytes cho AES-256)
    if (secretKey.length !== 32) {
      throw new Error('SHIPMENT_SECRET_KEY must be exactly 32 characters long');
    }

    this.secretKey = secretKey;
  }

  /**
   * Mã hóa dữ liệu
   * @param data Dữ liệu cần mã hóa (object hoặc string)
   * @returns Chuỗi đã mã hóa (base64)
   */
  encrypt(data: any): string {
    try {
      // Chuyển đổi data thành JSON string
      const text = typeof data === 'string' ? data : JSON.stringify(data);

      // Tạo IV (Initialization Vector) ngẫu nhiên
      const iv = crypto.randomBytes(16);

      // Tạo cipher
      const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv);
      cipher.setAAD(Buffer.from('shipment-data', 'utf8'));

      // Mã hóa dữ liệu
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Lấy authentication tag
      const authTag = cipher.getAuthTag();

      // Kết hợp IV, authTag và encrypted data
      const result = {
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        encrypted: encrypted
      };

      // Trả về dưới dạng base64
      return Buffer.from(JSON.stringify(result)).toString('base64');
    } catch (error) {
      this.logger.error(`Error encrypting data: ${error.message}`, error.stack);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Giải mã dữ liệu
   * @param encryptedData Chuỗi đã mã hóa (base64)
   * @returns Dữ liệu gốc
   */
  decrypt(encryptedData: string): any {
    try {
      // Giải mã base64
      const dataStr = Buffer.from(encryptedData, 'base64').toString('utf8');
      const data = JSON.parse(dataStr);

      // Lấy các thành phần
      const iv = Buffer.from(data.iv, 'hex');
      const authTag = Buffer.from(data.authTag, 'hex');
      const encrypted = data.encrypted;

      // Tạo decipher
      const decipher = crypto.createDecipheriv(this.algorithm, this.secretKey, iv);
      decipher.setAAD(Buffer.from('shipment-data', 'utf8'));
      decipher.setAuthTag(authTag);

      // Giải mã
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Thử parse JSON, nếu không được thì trả về string
      try {
        return JSON.parse(decrypted);
      } catch {
        return decrypted;
      }
    } catch (error) {
      this.logger.error(`Error decrypting data: ${error.message}`, error.stack);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Mã hóa dữ liệu với phương pháp đơn giản hơn (sử dụng AES-256-CBC)
   * @param data Dữ liệu cần mã hóa
   * @returns Chuỗi đã mã hóa
   */
  encryptSimple(data: any): string {
    try {
      const text = typeof data === 'string' ? data : JSON.stringify(data);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', this.secretKey, iv);

      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return iv.toString('hex') + ':' + encrypted;
    } catch (error) {
      this.logger.error(`Error in simple encryption: ${error.message}`, error.stack);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Giải mã dữ liệu đơn giản
   * @param encryptedData Chuỗi đã mã hóa
   * @returns Dữ liệu gốc
   */
  decryptSimple(encryptedData: string): any {
    try {
      const parts = encryptedData.split(':');
      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];

      const decipher = crypto.createDecipheriv('aes-256-cbc', this.secretKey, iv);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      try {
        return JSON.parse(decrypted);
      } catch {
        return decrypted;
      }
    } catch (error) {
      this.logger.error(`Error in simple decryption: ${error.message}`, error.stack);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Tạo hash cho dữ liệu (để so sánh)
   * @param data Dữ liệu cần hash
   * @returns Hash string
   */
  createHash(data: string): string {
    return crypto.createHash('sha256').update(data + this.secretKey).digest('hex');
  }

  /**
   * Xác minh hash
   * @param data Dữ liệu gốc
   * @param hash Hash để so sánh
   * @returns True nếu khớp
   */
  verifyHash(data: string, hash: string): boolean {
    const computedHash = this.createHash(data);
    return computedHash === hash;
  }
}
