/**
 * Utility functions để mask dữ liệu nh<PERSON>y cảm trong API responses
 */
export class DataMaskingUtil {
  /**
   * Mask dữ liệu nh<PERSON><PERSON> cảm, chỉ hiển thị 4 ký tự đầu và 4 ký tự cuối
   * @param data - Dữ liệu cần mask
   * @param maskChar - Ký tự dùng để mask (mặc định là '*')
   * @returns Dữ liệu đã được mask
   */
  static maskSensitiveData(data: string, maskChar: string = '*'): string {
    if (!data || typeof data !== 'string') {
      return '';
    }

    // Nếu chuỗi có độ dài <= 8 ký tự, mask toàn bộ trừ ký tự đầu và cuối
    if (data.length <= 8) {
      if (data.length <= 2) {
        return maskChar.repeat(data.length);
      }

      const firstChar = data.charAt(0);
      const lastChar = data.charAt(data.length - 1);
      const middleMask = maskChar.repeat(data.length - 2);

      return `${firstChar}${middleMask}${lastChar}`;
    }

    // Nếu chuỗi > 8 ký tự, hiển thị 4 ký tự đầu và 4 ký tự cuối
    const firstFour = data.substring(0, 4);
    const lastFour = data.substring(data.length - 4);
    const middleLength = data.length - 8;
    const middleMask = maskChar.repeat(middleLength);

    return `${firstFour}${middleMask}${lastFour}`;
  }

  /**
   * Mask API key hoặc token
   * @param apiKey - API key cần mask
   * @returns API key đã được mask
   */
  static maskApiKey(apiKey: string): string {
    return this.maskSensitiveData(apiKey);
  }

  /**
   * Mask shop ID hoặc merchant ID
   * @param shopId - Shop ID cần mask
   * @returns Shop ID đã được mask
   */
  static maskShopId(shopId: string): string {
    return this.maskSensitiveData(shopId);
  }

  /**
   * Mask token hoặc secret
   * @param token - Token cần mask
   * @returns Token đã được mask
   */
  static maskToken(token: string): string {
    return this.maskSensitiveData(token);
  }

  /**
   * Mask phone number
   * @param phone - Số điện thoại cần mask
   * @returns Số điện thoại đã được mask
   */
  static maskPhoneNumber(phone: string): string {
    if (!phone || typeof phone !== 'string') {
      return '';
    }

    // Loại bỏ các ký tự không phải số
    const cleanPhone = phone.replace(/\D/g, '');

    if (cleanPhone.length < 4) {
      return '*'.repeat(cleanPhone.length);
    }

    if (cleanPhone.length <= 8) {
      const firstTwo = cleanPhone.substring(0, 2);
      const lastTwo = cleanPhone.substring(cleanPhone.length - 2);
      const middleMask = '*'.repeat(cleanPhone.length - 4);
      return `${firstTwo}${middleMask}${lastTwo}`;
    }

    const firstThree = cleanPhone.substring(0, 3);
    const lastFour = cleanPhone.substring(cleanPhone.length - 4);
    const middleMask = '*'.repeat(cleanPhone.length - 7);

    return `${firstThree}${middleMask}${lastFour}`;
  }

  /**
   * Mask email address
   * @param email - Email cần mask
   * @returns Email đã được mask
   */
  static maskEmail(email: string): string {
    if (!email || typeof email !== 'string' || !email.includes('@')) {
      return '';
    }

    const [localPart, domain] = email.split('@');

    if (localPart.length <= 2) {
      return `${localPart}@${domain}`;
    }

    const maskedLocal = localPart.length <= 4
      ? `${localPart.charAt(0)}${'*'.repeat(localPart.length - 2)}${localPart.charAt(localPart.length - 1)}`
      : `${localPart.substring(0, 2)}${'*'.repeat(localPart.length - 4)}${localPart.substring(localPart.length - 2)}`;

    return `${maskedLocal}@${domain}`;
  }

  /**
   * Mask object chứa dữ liệu nhạy cảm
   * @param obj - Object cần mask
   * @param sensitiveFields - Danh sách các field nhạy cảm cần mask
   * @returns Object đã được mask
   */
  static maskObject<T extends Record<string, any>>(
    obj: T,
    sensitiveFields: string[]
  ): T {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const maskedObj = { ...obj } as any;

    sensitiveFields.forEach(field => {
      if (maskedObj[field] && typeof maskedObj[field] === 'string') {
        maskedObj[field] = this.maskSensitiveData(maskedObj[field]);
      }
    });

    return maskedObj as T;
  }

  /**
   * Mask array của objects
   * @param arr - Array cần mask
   * @param sensitiveFields - Danh sách các field nhạy cảm cần mask
   * @returns Array đã được mask
   */
  static maskArray<T extends Record<string, any>>(
    arr: T[],
    sensitiveFields: string[]
  ): T[] {
    if (!Array.isArray(arr)) {
      return arr;
    }

    return arr.map(item => this.maskObject(item, sensitiveFields));
  }

  /**
   * Kiểm tra xem field có phải là sensitive data không
   * @param fieldName - Tên field
   * @returns true nếu là sensitive field
   */
  static isSensitiveField(fieldName: string): boolean {
    const sensitiveKeywords = [
      'key', 'token', 'secret', 'password', 'api_key', 'apikey',
      'shop_id', 'shopid', 'merchant_id', 'merchantid',
      'client_id', 'clientid', 'client_secret', 'clientsecret',
      'access_token', 'accesstoken', 'refresh_token', 'refreshtoken'
    ];

    const lowerFieldName = fieldName.toLowerCase();
    return sensitiveKeywords.some(keyword => lowerFieldName.includes(keyword));
  }

  /**
   * Auto mask object dựa trên tên field
   * @param obj - Object cần mask
   * @returns Object đã được mask
   */
  static autoMaskObject<T extends Record<string, any>>(obj: T): T {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const maskedObj = { ...obj } as any;

    Object.keys(maskedObj).forEach(key => {
      if (this.isSensitiveField(key) && typeof maskedObj[key] === 'string') {
        maskedObj[key] = this.maskSensitiveData(maskedObj[key]);
      }
    });

    return maskedObj as T;
  }
}
