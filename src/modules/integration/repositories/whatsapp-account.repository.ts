import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WhatsAppAccount } from '../entities';

/**
 * Repository quản lý tài khoản WhatsApp Business
 */
@Injectable()
export class WhatsAppAccountRepository {
  constructor(
    @InjectRepository(WhatsAppAccount)
    private readonly repository: Repository<WhatsAppAccount>,
  ) {}

  /**
   * Tìm tài khoản WhatsApp theo ID
   * @param id ID của tài khoản WhatsApp
   * @returns Thông tin tài khoản WhatsApp
   */
  async findById(id: number): Promise<WhatsAppAccount | null> {
    return this.repository.findOne({
      where: { id },
    });
  }

  /**
   * Tìm tài khoản WhatsApp theo ID người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản WhatsApp của người dùng
   */
  async findByUserId(userId: number): Promise<WhatsAppAccount[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tài khoản WhatsApp theo ID agent
   * @param agentId ID của agent
   * @returns Danh sách tài khoản WhatsApp được kết nối với agent
   */
  async findByAgentId(agentId: string): Promise<WhatsAppAccount[]> {
    return this.repository.find({
      where: { agentId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tài khoản WhatsApp theo số điện thoại
   * @param phoneNumber Số điện thoại WhatsApp
   * @returns Thông tin tài khoản WhatsApp
   */
  async findByPhoneNumber(phoneNumber: string): Promise<WhatsAppAccount | null> {
    return this.repository.findOne({
      where: { phoneNumber },
    });
  }

  /**
   * Tạo mới tài khoản WhatsApp
   * @param data Dữ liệu tài khoản WhatsApp
   * @returns Tài khoản WhatsApp đã tạo
   */
  async create(data: Partial<WhatsAppAccount>): Promise<WhatsAppAccount> {
    const account = this.repository.create(data);
    return this.repository.save(account);
  }

  /**
   * Cập nhật tài khoản WhatsApp
   * @param id ID của tài khoản WhatsApp
   * @param data Dữ liệu cập nhật
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async update(id: number, data: Partial<WhatsAppAccount>): Promise<WhatsAppAccount | null> {
    await this.repository.update(id, {
      ...data,
      updatedAt: Math.floor(Date.now() / 1000),
    });
    return this.findById(id);
  }

  /**
   * Xóa tài khoản WhatsApp
   * @param id ID của tài khoản WhatsApp
   * @returns Kết quả xóa
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Kết nối tài khoản WhatsApp với agent
   * @param id ID của tài khoản WhatsApp
   * @param agentId ID của agent
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async connectAgent(id: number, agentId: string): Promise<WhatsAppAccount | null> {
    return this.update(id, { agentId });
  }

  /**
   * Ngắt kết nối tài khoản WhatsApp với agent
   * @param id ID của tài khoản WhatsApp
   * @returns Tài khoản WhatsApp đã cập nhật
   */
  async disconnectAgent(id: number): Promise<WhatsAppAccount | null> {
    return this.update(id, { agentId: undefined });
  }
}
