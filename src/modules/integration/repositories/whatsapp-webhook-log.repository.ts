import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WhatsAppWebhookLog } from '../entities';

/**
 * Repository quản lý log webhook từ WhatsApp
 */
@Injectable()
export class WhatsAppWebhookLogRepository {
  constructor(
    @InjectRepository(WhatsAppWebhookLog)
    private readonly repository: Repository<WhatsAppWebhookLog>,
  ) {}

  /**
   * Tìm log webhook theo ID
   * @param id ID của log webhook
   * @returns Thông tin log webhook
   */
  async findById(id: number): Promise<WhatsAppWebhookLog | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['whatsappAccount'],
    });
  }

  /**
   * Tạo mới log webhook
   * @param data Dữ liệu log webhook
   * @returns Log webhook đã tạo
   */
  async create(data: Partial<WhatsAppWebhookLog>): Promise<WhatsAppWebhookLog> {
    const log = this.repository.create(data);
    return this.repository.save(log);
  }

  /**
   * Lấy danh sách log webhook theo tài khoản WhatsApp
   * @param whatsappAccountId ID của tài khoản WhatsApp
   * @param page Số trang
   * @param limit Số lượng log mỗi trang
   * @returns Danh sách log webhook và tổng số
   */
  async findByAccountId(
    whatsappAccountId: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<[WhatsAppWebhookLog[], number]> {
    return this.repository.findAndCount({
      where: { whatsappAccountId },
      order: { receivedAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });
  }

  /**
   * Xóa log webhook cũ
   * @param days Số ngày giữ lại log
   * @returns Số lượng log đã xóa
   */
  async cleanupOldLogs(days: number = 30): Promise<number> {
    const cutoffTime = Math.floor(Date.now() / 1000) - (days * 24 * 60 * 60);
    const result = await this.repository.delete({
      receivedAt: { $lt: cutoffTime } as any,
    });
    return result.affected || 0;
  }
}
