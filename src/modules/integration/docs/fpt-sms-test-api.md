# API Test FPT SMS

## Giớ<PERSON> thiệu

API Test FPT SMS cung cấp các endpoint để kiểm tra kết nối và chức năng của dịch vụ FPT SMS. Các API này giúp quản trị viên có thể kiểm tra việc cấu hình và hoạt động của dịch vụ FPT SMS trước khi triển khai vào môi trường sản xuất.

## Yêu cầu

- Tài khoản quản trị viên (admin) với token JWT hợp lệ
- Thông tin cấu hình FPT SMS (Client ID, Client Secret, v.v.)

## Các API

### 1. Test kết nối với FPT SMS

Kiểm tra kết nối với dịch vụ FPT SMS bằng cách thử lấy token truy cập.

**Endpoint:** `POST /admin/integration/sms-test/fpt/test-connection`

**Headers:**
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "scope": "send_brandname_otp send_brandname",
  "apiUrl": "http://api.fpt.net/api"
}
```

**Các tham số:**
- `clientId` (tùy chọn): Client ID dùng cho xác thực OAuth. Nếu không cung cấp, sẽ sử dụng giá trị từ biến môi trường.
- `clientSecret` (tùy chọn): Client Secret dùng cho xác thực OAuth. Nếu không cung cấp, sẽ sử dụng giá trị từ biến môi trường.
- `scope` (tùy chọn): Phạm vi quyền truy cập cho xác thực OAuth. Mặc định: "send_brandname_otp send_brandname".
- `apiUrl` (tùy chọn): URL cơ sở của API FPT SMS. Mặc định: "http://api.fpt.net/api".

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "success": true,
    "message": "Kết nối thành công",
    "details": {
      "accessToken": "example-access-token",
      "expiresIn": 3600,
      "tokenType": "Bearer",
      "scope": "send_brandname_otp send_brandname"
    }
  }
}
```

### 2. Test gửi SMS qua FPT SMS

Gửi tin nhắn SMS thử nghiệm qua dịch vụ FPT SMS.

**Endpoint:** `POST /admin/integration/sms-test/fpt/send-sms`

**Headers:**
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "phoneNumber": "0901234567",
  "message": "Đây là tin nhắn test từ hệ thống",
  "brandName": "FPTSHOP"
}
```

**Các tham số:**
- `phoneNumber` (bắt buộc): Số điện thoại người nhận.
- `message` (bắt buộc): Nội dung tin nhắn.
- `brandName` (tùy chọn): Tên thương hiệu (brandname). Nếu không cung cấp, sẽ sử dụng giá trị từ biến môi trường.

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "success": true,
    "messageId": "123456789",
    "rawResponse": {
      "errorid": 0,
      "requestid": "123456789",
      "errordes": ""
    }
  }
}
```

### 3. Test gửi OTP qua FPT SMS

Gửi mã OTP thử nghiệm qua dịch vụ FPT SMS.

**Endpoint:** `POST /admin/integration/sms-test/fpt/send-otp`

**Headers:**
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "phoneNumber": "0901234567",
  "otpCode": "123456",
  "brandName": "FPTSHOP",
  "template": "Mã xác thực của bạn là: {code}. Vui lòng không chia sẻ mã này cho người khác."
}
```

**Các tham số:**
- `phoneNumber` (bắt buộc): Số điện thoại người nhận.
- `otpCode` (bắt buộc): Mã OTP cần gửi.
- `brandName` (tùy chọn): Tên thương hiệu (brandname). Nếu không cung cấp, sẽ sử dụng giá trị từ biến môi trường.
- `template` (tùy chọn): Mẫu tin nhắn OTP. Sử dụng {code} để chèn mã OTP. Mặc định: "Mã xác thực của bạn là: {code}".

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "success": true,
    "messageId": "123456789",
    "rawResponse": {
      "errorid": 0,
      "requestid": "123456789",
      "errordes": ""
    }
  }
}
```

### 4. Test kiểm tra trạng thái tin nhắn

Kiểm tra trạng thái của tin nhắn đã gửi qua dịch vụ FPT SMS.

**Endpoint:** `POST /admin/integration/sms-test/fpt/check-status`

**Headers:**
- `Authorization: Bearer <jwt_token>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "messageId": "123456789"
}
```

**Các tham số:**
- `messageId` (bắt buộc): ID của tin nhắn cần kiểm tra.

**Response:**
```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "messageId": "123456789",
    "status": "DELIVERED",
    "updatedAt": "2023-12-31T12:00:00.000Z",
    "details": "",
    "rawResponse": {
      "errorid": 0,
      "errordes": ""
    }
  }
}
```

## Xử lý lỗi

Khi có lỗi xảy ra, API sẽ trả về phản hồi với cấu trúc như sau:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "success": false,
    "errorCode": "1001",
    "errorMessage": "Lỗi khi gửi SMS: Thông tin xác thực không hợp lệ",
    "rawResponse": {
      "errorid": 1001,
      "errordes": "Thông tin xác thực không hợp lệ"
    }
  }
}
```

## Lưu ý

- Các API này chỉ dành cho mục đích kiểm tra và không nên sử dụng trong môi trường sản xuất.
- Đảm bảo rằng thông tin cấu hình FPT SMS (Client ID, Client Secret, v.v.) được bảo mật.
- Nên sử dụng số điện thoại test để tránh gửi tin nhắn đến người dùng thật trong quá trình kiểm tra.
