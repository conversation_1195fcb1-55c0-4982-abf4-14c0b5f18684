# User Provider Shipment API Examples

## Overview
API để quản lý cấu hình shipping provider (GHTK, GHN) của người dùng.

## Base URL
```
/v1/user/provider-shipments
```

## Authentication
Tất cả API đều yêu cầu JWT token trong header:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. T<PERSON>o cấu hình GHTK

**POST** `/v1/user/provider-shipments`

**Request Body:**
```json
{
  "name": "C<PERSON>u hình GHTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
    "timeout": 30000,
    "isTestMode": true
  }
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "<PERSON><PERSON><PERSON> hì<PERSON>HTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 30000,
    "isTestMode": true,
    "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
    "hasToken": true
  },
  "createdAt": 1640995200000
}
```

### 2. Tạo cấu hình GHN

**POST** `/v1/user/provider-shipments`

**Request Body:**
```json
{
  "name": "Cấu hình GHN chính",
  "type": "GHN",
  "ghnConfig": {
    "token": "42d0fc57-402d-11f0-9b81-222185cb68c8",
    "shopId": "196768",
    "timeout": 30000,
    "isTestMode": true
  }
}
```

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Cấu hình GHN chính",
  "type": "GHN",
  "ghnConfig": {
    "baseUrl": "https://dev-online-gateway.ghn.vn",
    "timeout": 30000,
    "isTestMode": true,
    "token": "42d0fc57-402d-11f0-9b81-222185cb68c8",
    "shopId": "196768",
    "hasToken": true,
    "hasShopId": true
  },
  "createdAt": 1640995200000
}
```

### 3. Lấy danh sách cấu hình

**GET** `/v1/user/provider-shipments?page=1&limit=10&type=GHTK`

**Response:**
```json
{
  "items": [
    {
      "id": "uuid-string",
      "name": "Cấu hình GHTK chính",
      "type": "GHTK",
      "ghtkConfig": {
        "baseUrl": "https://services-staging.ghtklab.com",
        "timeout": 30000,
        "isTestMode": true,
        "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
        "hasToken": true
      },
      "createdAt": 1640995200000
    }
  ],
  "meta": {
    "totalItems": 1,
    "itemCount": 1,
    "itemsPerPage": 10,
    "totalPages": 1,
    "currentPage": 1
  }
}
```

### 4. Lấy cấu hình theo ID

**GET** `/v1/user/provider-shipments/{id}`

**Response:**
```json
{
  "id": "uuid-string",
  "name": "Cấu hình GHTK chính",
  "type": "GHTK",
  "ghtkConfig": {
    "baseUrl": "https://services-staging.ghtklab.com",
    "timeout": 30000,
    "isTestMode": true,
    "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
    "hasToken": true
  },
  "createdAt": 1640995200000
}
```

### 5. Cập nhật cấu hình

**PUT** `/v1/user/provider-shipments/{id}`

**Request Body:**
```json
{
  "name": "Cấu hình GHTK chính - Updated",
  "ghtkConfig": {
    "token": "new-token-here",
    "timeout": 25000,
    "isTestMode": false
  }
}
```

### 6. Xóa cấu hình

**DELETE** `/v1/user/provider-shipments/{id}`

**Response:**
```json
{
  "message": "Xóa cấu hình thành công"
}
```

### 7. Lấy danh sách loại provider đã cấu hình

**GET** `/v1/user/provider-shipments/configured/types`

**Response:**
```json
{
  "types": ["GHTK", "GHN"]
}
```



## Error Responses

### 400 Bad Request
```json
{
  "code": 400,
  "message": "Cấu hình GHTK là bắt buộc khi type = GHTK"
}
```

### 404 Not Found
```json
{
  "code": 404,
  "message": "Không tìm thấy cấu hình"
}
```

### 500 Internal Server Error
```json
{
  "code": 500,
  "message": "Lỗi khi tạo cấu hình: Database connection failed"
}
```



## Provider Configuration Differences

### GHTK Configuration
- **Required Fields**: `token`
- **Optional Fields**: `timeout`, `isTestMode`
- **Token**: API token từ GHTK dashboard
- **Example**:
  ```json
  {
    "name": "Cấu hình GHTK chính",
    "type": "GHTK",
    "ghtkConfig": {
      "token": "8VQzUGUMWXltL3U0VC6A44SZU1Vi1SMZr3pdou",
      "timeout": 30000,
      "isTestMode": true
    }
  }
  ```

### GHN Configuration
- **Required Fields**: `token`, `shopId`
- **Optional Fields**: `timeout`, `isTestMode`
- **Token**: API token từ GHN dashboard (https://dev-online-gateway.ghn.vn/ > Settings > API)
- **Shop ID**: Shop ID từ GHN dashboard
- **Example**:
  ```json
  {
    "name": "Cấu hình GHN chính",
    "type": "GHN",
    "ghnConfig": {
      "token": "42d0fc57-402d-11f0-9b81-222185cb68c8",
      "shopId": "196768",
      "timeout": 30000,
      "isTestMode": true
    }
  }
  ```

## Important Notes

### Automatic BASE_URL Configuration
- **BASE_URL được tự động thiết lập** dựa trên `isTestMode`:
  - `isTestMode: true` → Sử dụng TEST environment URL
  - `isTestMode: false` → Sử dụng PRODUCTION environment URL
- **GHTK URLs**:
  - Test: `https://services-staging.ghtklab.com`
  - Production: `https://services.giaohangtietkiem.vn`
- **GHN URLs**:
  - Test: `https://dev-online-gateway.ghn.vn`
  - Production: `https://online-gateway.ghn.vn`
- **Không cần truyền `baseUrl`** trong request, hệ thống sẽ tự động chọn URL phù hợp

### Mode Switching
- Để chuyển đổi giữa test/production mode, sử dụng PUT API với `isTestMode: true/false`
- Hệ thống sẽ tự động cập nhật BASE_URL tương ứng

## Security Notes

1. **Mã hóa**: Tất cả thông tin nhạy cảm (token, API key) được mã hóa trước khi lưu database
2. **Token hiển thị**: Response trả về token và shopId đã được giải mã để user có thể xem và kiểm tra
3. **User isolation**: Mỗi user chỉ có thể truy cập cấu hình của chính mình
4. **JWT Authentication**: Tất cả API đều yêu cầu JWT token hợp lệ
5. **Bảo mật truy cập**: Chỉ chủ sở hữu cấu hình mới có thể xem token đã giải mã
