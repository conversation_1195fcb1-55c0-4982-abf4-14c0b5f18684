# Module Integration

## Tổng quan

Module Integration cung cấp các chức năng tích hợp với các dịch vụ bên ngoài như cổng thanh toán, email server, SMS server, Facebook, website và các nhà cung cấp vận chuyển. Module này giúp người dùng kết nối và quản lý các tích hợp của họ với các dịch vụ bên thứ ba.

## Cấu trúc module

```
integration/
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   └── services/           # Services xử lý logic nghiệp vụ cho admin
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   └── services/           # Services xử lý logic nghiệp vụ cho người dùng
├── dto/                    # Data Transfer Objects
├── entities/               # Entities mapping với database
├── repositories/           # Repositories tương tác với database
└── integration.module.ts   # Module definition
```

## Các entity chính

1. **PaymentGateway**: Quản lý tài khoản ngân hàng liên kết
2. **UserCompanyInSepay**: Đại diện company trên sepay-hub
3. **EmailServerConfiguration**: Cấu hình máy chủ Email (SMTP)
4. **FacebookPage**: Quản lý trang Facebook được kết nối với agent
5. **FacebookPersonal**: Quản lý tài khoản Facebook cá nhân
6. **UserWebsite**: Quản lý website của người dùng
7. **UserKey**: Quản lý key API của người dùng cho các nhà cung cấp
8. **SmsServerConfiguration**: Cấu hình máy chủ SMS

## Chức năng chính

### Quản lý cổng thanh toán (Payment Gateway)
- Tạo, cập nhật, xóa tài khoản ngân hàng liên kết
- Quản lý trạng thái tài khoản (PENDING, APPROVED, REJECTED)
- Hỗ trợ tài khoản VA (Virtual Account)

### Quản lý cấu hình Email Server
- Tạo, cập nhật, xóa cấu hình máy chủ Email
- Kiểm tra kết nối đến máy chủ Email
- Hỗ trợ cấu hình SSL/TLS và các cài đặt nâng cao

### Quản lý tích hợp Facebook
- Kết nối tài khoản Facebook cá nhân
- Quản lý trang Facebook
- Kết nối trang Facebook với agent

### Quản lý website
- Đăng ký và xác minh quyền sở hữu website
- Kết nối website với agent
- Quản lý danh sách website

### Quản lý key API
- Quản lý key API cho các nhà cung cấp vận chuyển
- Đặt key mặc định và trạng thái hoạt động
- Kiểm tra kết nối đến nhà cung cấp

### Quản lý cấu hình SMS Server
- Tạo, cập nhật, xóa cấu hình máy chủ SMS
- Kiểm tra kết nối đến máy chủ SMS
- Hỗ trợ các cài đặt nâng cao

## API Endpoints

### User Endpoints

- `GET /integration/payment-gateway` - Lấy danh sách payment gateway
- `GET /integration/email-server` - Lấy danh sách cấu hình email server
- `GET /integration/facebook-page` - Lấy danh sách trang Facebook
- `GET /integration/website` - Lấy danh sách website
- `GET /integration/user-key` - Lấy danh sách key API
- `GET /integration/sms-server` - Lấy danh sách cấu hình SMS server

### Admin Endpoints

- `GET /admin/integration/payment-gateway` - Lấy danh sách payment gateway (admin)
- `GET /admin/integration/email-server` - Lấy danh sách cấu hình email server (admin)
- `GET /admin/integration/facebook-page` - Lấy danh sách trang Facebook (admin)
- `GET /admin/integration/website` - Lấy danh sách website (admin)
- `GET /admin/integration/user-key` - Lấy danh sách key API (admin)
- `GET /admin/integration/sms-server` - Lấy danh sách cấu hình SMS server (admin)

## Tính năng bảo mật

- Che giấu thông tin nhạy cảm (mật khẩu, token, API key, v.v.)
- Kiểm tra quyền sở hữu trước khi thực hiện các thao tác
- Xác thực người dùng thông qua JWT
- Phân quyền admin/user

## Cách sử dụng

### Tích hợp với cổng thanh toán

```typescript
// Tạo mới payment gateway
const paymentGateway = await paymentGatewayService.create({
  accountId: 'ACC123456',
  bankCode: 'VCB',
  accountNumber: '**********',
  accountHolderName: 'Nguyen Van A'
}, userId);
```

### Tích hợp với Facebook

```typescript
// Kết nối trang Facebook với agent
const facebookPage = await facebookPageService.connectAgent(
  'facebook_page_id',
  'agent_id',
  userId
);
```

### Tích hợp với website

```typescript
// Xác minh website
const website = await userWebsiteService.verifyWebsite(websiteId, userId);
```

## Liên kết với các module khác

- **Agent Module**: Kết nối các tích hợp với agent
- **User Module**: Quản lý quyền sở hữu của người dùng
- **Auth Module**: Xác thực và phân quyền
