# Module Agent

## Tổng quan

Module Agent cung cấp các chức năng quản lý và tương tác với các agent AI trong hệ thống. Module này cho phép người dùng tạo, c<PERSON>u hình và sử dụng các agent AI cho các mục đích khác nhau.

## Cấu trúc module

```
agent/
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   ├── services/           # Services xử lý logic nghiệp vụ cho admin
│   └── dto/                # DTO định nghĩa cấu trúc request và response
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   ├── services/           # Services xử lý logic nghiệp vụ cho người dùng
│   └── dto/                # DTO định nghĩa cấu trúc request và response
├── entities/               # Entities mapping với database
├── repositories/           # Repositories tương tác với database
├── constants/              # Các hằng số và enum
├── exceptions/             # Các mã lỗi của module
└── agent.module.ts         # Module definition
```

## Các entity chính

1. **Agent**: Entity chính lưu trữ thông tin cơ bản của agent
2. **UserAgent**: Thông tin chi tiết của agent thuộc về người dùng
3. **AdminAgent**: Thông tin chi tiết của agent do admin tạo
4. **TypeAgent**: Loại agent (chatbot, assistant, v.v.)
5. **AgentUrl**: URL liên kết với agent
6. **AgentMedia**: Tài nguyên media của agent
7. **AgentProduct**: Sản phẩm liên kết với agent
8. **TypeAgentMapping**: Mapping giữa agent và loại agent

## Chức năng chính

### Quản lý Agent
- Tạo, cập nhật, xóa agent
- Quản lý trạng thái hoạt động của agent
- Cấu hình model AI cho agent

### Quản lý Loại Agent
- Quản lý các loại agent trong hệ thống
- Phân loại agent theo chức năng

### Quản lý Tài nguyên Agent
- Quản lý URL liên kết với agent
- Quản lý media của agent
- Quản lý sản phẩm liên kết với agent

## API Endpoints

### User Endpoints

- `GET /agent` - Lấy danh sách agent của người dùng
- `GET /agent/:id` - Lấy thông tin chi tiết agent
- `POST /agent` - Tạo mới agent
- `PUT /agent/:id` - Cập nhật thông tin agent
- `DELETE /agent/:id` - Xóa agent

### Admin Endpoints

- `GET /admin/agent` - Lấy danh sách tất cả agent
- `GET /admin/agent/:id` - Lấy thông tin chi tiết agent
- `POST /admin/agent` - Tạo mới agent
- `PUT /admin/agent/:id` - Cập nhật thông tin agent
- `DELETE /admin/agent/:id` - Xóa agent

## Cách sử dụng

### Tạo mới agent

```typescript
// Trong controller
@Post()
@ApiOperation({summary: 'Tạo agent mới'})
@ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Agent đã được tạo thành công',
    schema: ApiResponseDto.getSchema(AgentDetailResponseDto)
})
async
createAgent(
    @CurrentUser('id')
userId: number,
@Body()
createAgentDto: CreateAgentDto
):
Promise < ApiResponseDto < AgentDetailResponseDto >> {
    const agent = await this.agentUserService.createAgent(userId, createAgentDto);
    return ApiResponseDto.success(agent, 'Tạo agent thành công');
}

// Trong service
async
createAgent(
    userId
:
number,
    createAgentDto
:
CreateAgentDto
):
Promise < AgentDetailResponseDto > {
    try {
        // Kiểm tra loại agent có tồn tại không
        const typeAgent = await this.typeAgentRepository.findActiveById(
            createAgentDto.typeAgentId
        );

        if(!typeAgent
)
{
    throw new AppException(
        AGENT_ERROR_CODES.AGENT_TYPE_NOT_FOUND,
        `Không tìm thấy loại agent với ID ${createAgentDto.typeAgentId}`,
        {typeAgentId: createAgentDto.typeAgentId, userId}
    );
}

// Tạo agent mới
const agent = new Agent();
agent.name = createAgentDto.name;
agent.sourceType = AgentSourceType.USER;
agent.agentTypeId = createAgentDto.typeAgentId;
agent.modelConfig = createAgentDto.modelConfig || {};
agent.status = AgentStatusEnum.ACTIVE;

// Lưu agent vào database
const savedAgent = await this.agentRepository.save(agent);

// Tạo liên kết với người dùng
await this.userAgentRepository.createUserAgent(userId, savedAgent.id);

// Trả về thông tin chi tiết của agent
return this.findOne(userId, savedAgent.id);
} catch
(error)
{
    this.logger.error(`Lỗi khi tạo agent: ${error.message}`, error.stack);
    if (error instanceof AppException) {
        throw error;
    }
    throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Không thể tạo agent: ${error.message}`,
        {userId, error: error.message}
    );
}
}
```

### Cập nhật loại agent

```typescript
// Trong controller
@Patch(':id/type')
@ApiOperation({ summary: 'Cập nhật loại agent' })
@ApiResponse({
  status: HttpStatus.OK,
  description: 'Loại agent đã được cập nhật thành công',
  schema: ApiResponseDto.getSchema(AgentDetailResponseDto)
})
async updateAgentType(
  @CurrentUser('id') userId: number,
  @Param('id') agentId: string,
  @Body() updateDto: UpdateAgentTypeDto
): Promise<ApiResponseDto<AgentDetailResponseDto>> {
  const agent = await this.agentUserService.updateAgentType(userId, agentId, updateDto);
  return ApiResponseDto.success(agent, 'Cập nhật loại agent thành công');
}
```

## Liên kết với các module khác

- **User Module**: Quản lý quyền sở hữu của người dùng
- **Auth Module**: Xác thực và phân quyền
- **Integration Module**: Tích hợp agent với các dịch vụ bên ngoài

## Quy chuẩn áp dụng

### Repository
- Sử dụng QueryBuilder thay vì raw SQL
- Tạo các custom repository class cho mỗi entity
- Xử lý lỗi với AppException và mã lỗi cụ thể
- Trả về Promise với kiểu dữ liệu cụ thể

### Entity
- Sử dụng JSDoc đầy đủ bằng tiếng Việt có dấu
- Sử dụng kiểu dữ liệu cụ thể (Record<string, any> thay vì any)
- Không sử dụng relationship decorators, sử dụng các trường tham chiếu rõ ràng

### Service
- Sử dụng repositories cho các thao tác với database
- Xử lý lỗi với AppException và mã lỗi cụ thể
- Trả về Promise với kiểu dữ liệu cụ thể
- Sử dụng JSDoc đầy đủ bằng tiếng Việt có dấu

### Controller
- Sử dụng Swagger decorators đầy đủ
- Wrap responses trong ApiResponseDto
- Sử dụng ApiResponseDto.getSchema() để hiển thị đúng trong Swagger
- Không sử dụng schemas mà sử dụng DTO với @ApiProperty để định nghĩa cấu trúc response
- Sử dụng JSDoc đầy đủ bằng tiếng Việt có dấu

### DTO
- Sử dụng validation decorators đầy đủ
- Sử dụng ApiProperty decorators đầy đủ
- Sử dụng JSDoc đầy đủ bằng tiếng Việt có dấu

### Xử lý lỗi
- Sử dụng AppException với mã lỗi cụ thể
- Ghi log lỗi trước khi throw exception
- Bao gồm thông tin context trong exception

### Logging
- Sử dụng Logger của NestJS
- Ghi log các sự kiện quan trọng với cấu trúc nhất quán
- Sử dụng tiếng Việt có dấu trong các thông điệp log
