import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến strategy trong module agent
 */
export const STRATEGY_ERROR_CODES = {
  /**
   * Lỗi khi không tìm thấy strategy
   */
  STRATEGY_NOT_FOUND: new ErrorCode(
    10350,
    'Không tìm thấy chiến lược',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy phiên bản strategy
   */
  STRATEGY_VERSION_NOT_FOUND: new ErrorCode(
    10351,
    'Không tìm thấy phiên bản chiến lược',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không có quyền truy cập strategy
   */
  STRATEGY_ACCESS_DENIED: new ErrorCode(
    10352,
    'Không có quyền truy cập chiến lược',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Lỗi khi gán strategy cho agent
   */
  STRATEGY_ASSIGN_FAILED: new ErrorCode(
    10353,
    'Không thể gán chiến lược cho agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi gỡ strategy khỏi agent
   */
  STRATEGY_REMOVE_FAILED: new ErrorCode(
    10354,
    'Không thể gỡ chiến lược khỏi agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi agent chưa được gán strategy
   */
  STRATEGY_NOT_ASSIGNED: new ErrorCode(
    10355,
    'Agent chưa được gán chiến lược',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi strategy không có phiên bản nào
   */
  STRATEGY_NO_VERSIONS: new ErrorCode(
    10356,
    'Chiến lược không có phiên bản nào',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không thể truy vấn thông tin strategy
   */
  STRATEGY_FETCH_FAILED: new ErrorCode(
    10357,
    'Không thể truy vấn thông tin chiến lược',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
