import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  CreateMultiAgentsSystemDto,
  QueryMultiAgentsSystemDto,
  UpdateMultiAgentsSystemDto
} from '../dto/multi-agents-system';
import { MultiAgentsSystemService } from '../services/multi-agents-system.service';

/**
 * Controller xử lý các API liên quan đến quan hệ đa cấp giữa các agent
 */
@ApiTags('Admin - Multi Agents System')
@Controller('admin/multi-agents-system')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class MultiAgentsSystemController {
  constructor(private readonly multiAgentsSystemService: MultiAgentsSystemService) { }

  /**
   * Tạo mới quan hệ đa cấp giữa các agent
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới quan hệ đa cấp giữa các agent' })
  @ApiResponse({
    status: 201,
    description: 'Quan hệ đa cấp đã được tạo thành công',
    type: ApiResponseDto,
  })
  async createRelation(
    @Body() createDto: CreateMultiAgentsSystemDto,
  ) {
    const result = await this.multiAgentsSystemService.createRelation(createDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật prompt cho quan hệ đa cấp
   */
  @Patch(':parentAgentId/:childAgentId')
  @ApiOperation({ summary: 'Cập nhật prompt cho quan hệ đa cấp' })
  @ApiParam({ name: 'parentAgentId', description: 'ID của agent cấp trên' })
  @ApiParam({ name: 'childAgentId', description: 'ID của agent cấp dưới' })
  @ApiResponse({
    status: 200,
    description: 'Quan hệ đa cấp đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  async updateRelation(
    @CurrentEmployee('id') employeeId: number,
    @Param('parentAgentId', ParseUUIDPipe) parentAgentId: string,
    @Param('childAgentId', ParseUUIDPipe) childAgentId: string,
    @Body() updateDto: UpdateMultiAgentsSystemDto,
  ) {
    const result = await this.multiAgentsSystemService.updateRelation(
      employeeId,
      parentAgentId,
      childAgentId,
      updateDto,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa quan hệ đa cấp
   */
  @Delete(':parentAgentId/:childAgentId')
  @ApiOperation({ summary: 'Xóa quan hệ đa cấp' })
  @ApiParam({ name: 'parentAgentId', description: 'ID của agent cấp trên' })
  @ApiParam({ name: 'childAgentId', description: 'ID của agent cấp dưới' })
  @ApiResponse({
    status: 200,
    description: 'Quan hệ đa cấp đã được xóa thành công',
    type: ApiResponseDto,
  })
  async deleteRelation(
    @CurrentEmployee('id') employeeId: number,
    @Param('parentAgentId', ParseUUIDPipe) parentAgentId: string,
    @Param('childAgentId', ParseUUIDPipe) childAgentId: string,
  ) {
    await this.multiAgentsSystemService.deleteRelation(employeeId, parentAgentId, childAgentId);
    return ApiResponseDto.success({ message: 'Quan hệ đa cấp đã được xóa thành công' });
  }

  /**
   * Lấy danh sách quan hệ đa cấp với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách quan hệ đa cấp với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách quan hệ đa cấp',
    type: ApiResponseDto,
  })
  async getRelations(@Query() queryDto: QueryMultiAgentsSystemDto) {
    const result = await this.multiAgentsSystemService.getRelations(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy thông tin quan hệ đa cấp theo ID
   */
  @Get(':parentAgentId/:childAgentId')
  @ApiOperation({ summary: 'Lấy thông tin quan hệ đa cấp theo ID' })
  @ApiParam({ name: 'parentAgentId', description: 'ID của agent cấp trên' })
  @ApiParam({ name: 'childAgentId', description: 'ID của agent cấp dưới' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin quan hệ đa cấp',
    type: ApiResponseDto,
  })
  async getRelation(
    @Param('parentAgentId', ParseUUIDPipe) parentAgentId: string,
    @Param('childAgentId', ParseUUIDPipe) childAgentId: string,
  ) {
    const result = await this.multiAgentsSystemService.getRelation(parentAgentId, childAgentId);
    return ApiResponseDto.success(result);
  }
}
