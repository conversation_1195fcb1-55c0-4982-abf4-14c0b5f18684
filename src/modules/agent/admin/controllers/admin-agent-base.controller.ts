import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import {
  Api<PERSON><PERSON><PERSON>Auth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { AdminAgentBaseService } from '../services/admin-agent-base.service';
import {
  AgentBaseListItemDto,
  AgentBaseQueryDto,
  AgentBaseResponseDto,
  AgentBaseTrashItemDto,
  CreateAgentBaseDto,
  UpdateAgentBaseDto,
} from '../dto/agent-base';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ErrorCode } from '@/common';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions/knowledge-file.exception';

/**
 * Controller xử lý các API liên quan đến agent base
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_BASE)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  AgentBaseResponseDto,
  AgentBaseListItemDto,
  AgentBaseTrashItemDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agents/base')
export class AdminAgentBaseController {
  constructor(private readonly adminAgentBaseService: AdminAgentBaseService) {}

  // /**
  //  * Lấy danh sách agent base
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent base
  //  */
  // @Get()
  // @ApiOperation({ summary: 'Lấy danh sách agent base' })
  // @ApiOkResponse({
  //   description: 'Danh sách agent base',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentBaseListItemDto),
  // })
  // @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  // async findAll(
  //   @Query() queryDto: AgentBaseQueryDto,
  // ): Promise<ApiResponseDto<PaginatedResult<AgentBaseListItemDto>>> {
  //   const result = await this.adminAgentBaseService.findAll(queryDto);
  //   return ApiResponseDto.success(result, 'Thành công');
  // }

  // /**
  //  * Lấy danh sách agent base đã xóa
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent base đã xóa
  //  */
  // @Get('trash')
  // @ApiOperation({ summary: 'Lấy danh sách agent base đã xóa' })
  // @ApiOkResponse({
  //   description: 'Danh sách agent base đã xóa',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentBaseTrashItemDto),
  // })
  // @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  // async findAllDeleted(
  //   @Query() queryDto: AgentBaseQueryDto,
  // ): Promise<ApiResponseDto<PaginatedResult<AgentBaseTrashItemDto>>> {
  //   const result = await this.adminAgentBaseService.findAllDeleted(queryDto);
  //   return ApiResponseDto.success(result, 'Thành công');
  // }

  // /**
  //  * Lấy thông tin chi tiết agent base theo ID
  //  * @param id ID của agent base
  //  * @returns Thông tin chi tiết agent base
  //  */
  // @Get(':id')
  // @ApiOperation({ summary: 'Lấy thông tin chi tiết agent base theo ID' })
  // @ApiParam({ name: 'id', description: 'ID của agent base', type: String })
  // @ApiOkResponse({
  //   description: 'Thông tin chi tiết agent base',
  //   schema: ApiResponseDto.getSchema(AgentBaseResponseDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async findOne(
  //   @Param('id') id: string,
  // ): Promise<ApiResponseDto<AgentBaseResponseDto>> {
  //   const result = await this.adminAgentBaseService.findById(id);
  //   return ApiResponseDto.success(result, 'Thành công');
  // }

  // /**
  //  * Tạo agent base mới
  //  * @param createDto Dữ liệu tạo agent base
  //  * @param employee Thông tin nhân viên
  //  * @returns Thông tin agent base đã tạo
  //  */
  // @Post()
  // @ApiOperation({ summary: 'Tạo agent base mới' })
  // @ApiCreatedResponse({
  //   description: 'Thông tin agent base đã tạo',
  //   schema: ApiResponseDto.getSchema(Object({ avatarUrlUpload: 'string' })),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async create(
  //   @Body() createDto: CreateAgentBaseDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<{ avatarUrlUpload: string | null }>> {
  //   const url = await this.adminAgentBaseService.create(createDto, employee.id);
  //   return ApiResponseDto.created(
  //     { avatarUrlUpload: url },
  //     'Tạo agent base thành công',
  //   );
  // }

  // /**
  //  * Cập nhật thông tin agent base
  //  * @param id ID của agent base
  //  * @param updateDto Dữ liệu cập nhật
  //  * @param employee Thông tin nhân viên
  //  * @returns Thông tin agent base đã cập nhật
  //  */
  // @Patch(':id')
  // @ApiOperation({ summary: 'Cập nhật thông tin agent base' })
  // @ApiParam({ name: 'id', description: 'ID của agent base', type: String })
  // @ApiOkResponse({
  //   description: 'Thông tin agent base đã cập nhật',
  //   schema: ApiResponseDto.getSchema(Object({ avatarUrlUpload: 'string' })),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async update(
  //   @Param('id') id: string,
  //   @Body() updateDto: UpdateAgentBaseDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<{ avatarUrlUpload: string | null }>> {
  //   const result = await this.adminAgentBaseService.update(
  //     id,
  //     updateDto,
  //     employee.id,
  //   );
  //   return ApiResponseDto.success({ avatarUrlUpload: result }, 'Cập nhật agent base thành công');
  // }

  // /**
  //  * Xóa agent base
  //  * @param id ID của agent base
  //  * @param employee Thông tin nhân viên
  //  * @returns Thông báo xóa thành công
  //  */
  // @Delete(':id')
  // @ApiOperation({ summary: 'Xóa agent base' })
  // @ApiParam({ name: 'id', description: 'ID của agent base', type: String })
  // @ApiOkResponse({
  //   description: 'Thông báo xóa thành công',
  //   schema: ApiResponseDto.getSchema(null),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async remove(
  //   @Param('id') id: string,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<null>> {
  //   await this.adminAgentBaseService.remove(id, employee.id);
  //   return ApiResponseDto.success(null, 'Xóa agent base thành công');
  // }

  // /**
  //  * Đặt agent base thành active
  //  * @param id ID của agent base
  //  * @param employee Thông tin nhân viên
  //  * @returns Thông báo đặt active thành công
  //  */
  // @Patch(':id/active')
  // @ApiOperation({ summary: 'Đặt agent base thành active' })
  // @ApiParam({ name: 'id', description: 'ID của agent base', type: String })
  // @ApiOkResponse({
  //   description: 'Thông báo đặt active thành công',
  //   schema: ApiResponseDto.getSchema(null),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async setActive(
  //   @Param('id') id: string,
  // ): Promise<ApiResponseDto<null>> {
  //   await this.adminAgentBaseService.setActive(id);
  //   return ApiResponseDto.success(
  //     null,
  //     'Đặt agent base thành active thành công',
  //   );
  // }



  // /**
  //  * Khôi phục agent base đã xóa
  //  * @param id ID của agent base
  //  * @param employee Thông tin nhân viên
  //  * @returns Thông báo khôi phục thành công
  //  */
  // @Patch(':id/restore')
  // @ApiOperation({ summary: 'Khôi phục agent base đã xóa' })
  // @ApiParam({ name: 'id', description: 'ID của agent base', type: String })
  // @ApiOkResponse({
  //   description: 'Thông báo khôi phục thành công',
  //   schema: ApiResponseDto.getSchema(null),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_BASE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR,
  // )
  // async restore(
  //   @Param('id') id: string,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<null>> {
  //   await this.adminAgentBaseService.restore(id, employee.id);
  //   return ApiResponseDto.success(null, 'Khôi phục agent base thành công');
  // }
}
