import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { ModelConfigDto } from '@modules/agent/admin/dto/common';

/**
 * DTO cho việc cập nhật agent system
 */
export class UpdateAgentSystemDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * Mã định danh của agent system, dùng để định danh trong code
   */
  @ApiPropertyOptional({
    description: 'Mã định danh của agent system, dùng để định danh trong code',
    example: 'system_assistant',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  @Matches(/^[a-z0-9_]+$/, {
    message: 'nameCode chỉ được chứa chữ cái thường, số và dấu gạch dưới',
  })
  nameCode?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
  })
  @IsEnum(AgentStatusEnum)
  @IsOptional()
  status?: AgentStatusEnum;

  /**
   * ID của vai trò
   */
  @ApiPropertyOptional({
    description: 'ID của vai trò',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsOptional()
  roleId?: string;

  /**
   * ID của base model được sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của base model được sử dụng',
    example: 'model-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  modelBaseId?: string;

  /**
   * ID của finetuning model được sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model được sử dụng',
    example: 'finetuning-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  modelFinetuningId?: string;
}
