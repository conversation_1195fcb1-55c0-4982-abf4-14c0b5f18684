import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho cấu hình model của agent base
 */
export class ModelConfigDto implements ModelConfig {

  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 1.0,
  })
  @IsOptional()
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model (0-1)',
    example: 1.0,
  })
  @IsOptional()
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
  })
  @IsOptional()
  top_k?: number;

  /**
   * <PERSON><PERSON> token tối đa cho kết quả
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa cho kết quả',
    example: 1000,
  })
  @IsOptional()
  max_tokens?: number;
}
