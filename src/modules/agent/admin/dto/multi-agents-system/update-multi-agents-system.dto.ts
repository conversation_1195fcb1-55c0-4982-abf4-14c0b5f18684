import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO để cập nhật prompt cho quan hệ đa cấp giữa các agent
 */
export class UpdateMultiAgentsSystemDto {
  /**
   * Prompt mới được sử dụng trong mối quan hệ giữa hai agent
   */
  @ApiProperty({
    description: 'Prompt mới cho quan hệ giữa hai agent',
    example: 'Bạn là trợ lý của agent cấp trên, hãy hỗ trợ theo yêu cầu mới',
  })
  @IsNotEmpty({ message: 'Prompt không được để trống' })
  @IsString({ message: 'Prompt phải là chuỗi' })
  prompt: string;
}
