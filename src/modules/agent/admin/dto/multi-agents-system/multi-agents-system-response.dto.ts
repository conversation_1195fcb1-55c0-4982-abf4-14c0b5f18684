import { ApiProperty } from '@nestjs/swagger';
import { MultiAgentsSystem } from '@modules/agent/entities';

/**
 * DTO để trả về thông tin quan hệ đa cấp giữa các agent
 */
export class MultiAgentsSystemResponseDto {
  /**
   * ID của agent cấp trên
   */
  @ApiProperty({
    description: 'ID của agent cấp trên',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  parentAgentId: string;

  /**
   * ID của agent cấp dưới
   */
  @ApiProperty({
    description: 'ID của agent cấp dưới',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  childAgentId: string;

  /**
   * Prompt cho quan hệ giữa hai agent
   */
  @ApiProperty({
    description: 'Prompt cho quan hệ giữa hai agent',
    example: 'Bạn là trợ lý của agent cấp trên, hãy hỗ trợ theo yêu cầu',
    nullable: true,
  })
  prompt: string | null;

  /**
   * <PERSON>y<PERSON>n đổi từ entity sang DTO
   * @param entity Entity MultiAgentsSystem
   * @returns DTO MultiAgentsSystemResponseDto
   */
  static fromEntity(entity: MultiAgentsSystem): MultiAgentsSystemResponseDto {
    const dto = new MultiAgentsSystemResponseDto();
    dto.parentAgentId = entity.parentAgentId;
    dto.childAgentId = entity.childAgentId;
    dto.prompt = entity.prompt;
    return dto;
  }
}
