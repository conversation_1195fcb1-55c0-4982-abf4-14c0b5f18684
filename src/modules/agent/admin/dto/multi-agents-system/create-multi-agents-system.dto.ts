import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

/**
 * DTO để tạo mới quan hệ đa cấp giữa các agent
 */
export class CreateMultiAgentsSystemDto {
  /**
   * ID của agent đóng vai trò là người đỡ đầu (hoặc cấp trên) trong mối quan hệ đa cấp
   */
  @ApiProperty({
    description: 'ID của agent cấp trên (parent)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID của agent cấp trên không được để trống' })
  @IsUUID('4', { message: 'ID của agent cấp trên phải là UUID hợp lệ' })
  parentAgentId: string;

  /**
   * ID của agent đóng vai trò là người được đỡ đầu (hoặc cấp dưới) trong mối quan hệ đa cấp
   */
  @ApiProperty({
    description: 'ID của agent cấp dưới (child)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID của agent cấp dưới không được để trống' })
  @IsUUID('4', { message: 'ID của agent cấp dưới phải là UUID hợp lệ' })
  childAgentId: string;

  /**
   * Prompt được sử dụng trong mối quan hệ giữa hai agent
   */
  @ApiProperty({
    description: 'Prompt cho quan hệ giữa hai agent',
    example: 'Bạn là trợ lý của agent cấp trên, hãy hỗ trợ theo yêu cầu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Prompt phải là chuỗi' })
  prompt?: string;
}
