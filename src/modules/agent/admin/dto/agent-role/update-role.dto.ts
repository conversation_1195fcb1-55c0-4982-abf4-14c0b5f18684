import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ModuleMcpConfigDto } from './create-role.dto';

/**
 * DTO cho request body khi cập nhật vai trò
 */
export class UpdateRoleDto {
  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
    maxLength: 50,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  name?: string;

  @ApiProperty({
    description: 'Mô tả của vai trò',
    example: 'Vai trò hỗ trợ quản trị viên',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  // Đã loại bỏ trường groupToolIds vì không còn sử dụng

  @ApiProperty({
    description: 'Cấu hình module MCP',
    type: ModuleMcpConfigDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ModuleMcpConfigDto)
  moduleMcpConfig?: ModuleMcpConfigDto;
}
