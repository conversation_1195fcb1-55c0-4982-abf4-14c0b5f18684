import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';

/**
 * DTO cho việc cập nhật trạng thái agent của user
 */
export class UpdateUserAgentStatusDto {
  /**
   * Trạng thái mới của agent
   */
  @ApiProperty({
    description: 'Trạng thái mới của agent',
    enum: [AgentStatusEnum.APPROVED, AgentStatusEnum.REJECTED],
    example: AgentStatusEnum.APPROVED,
  })
  @IsEnum([AgentStatusEnum.APPROVED, AgentStatusEnum.REJECTED], {
    message: 'status chỉ có thể là APPROVED hoặc REJECTED',
  })
  @IsNotEmpty({ message: 'status không được để trống' })
  status: AgentStatusEnum.APPROVED | AgentStatusEnum.REJECTED;
}
