import { TypeAgentConfig } from '../interfaces/type-agent-config.interface';

/**
 * Utility để tạo cấu hình mặc định cho TypeAgent
 */
export class TypeAgentConfigUtil {
  /**
   * Tạo cấu hình mặc định với tất cả các trường false
   */
  static createDefaultConfig(): TypeAgentConfig {
    return {
      enableAgentProfileCustomization: false,
      enableOutputToMessenger: false,
      enableOutputToWebsiteLiveChat: false,
      enableTaskConversionTracking: false,
      enableResourceUsage: false,
      enableDynamicStrategyExecution: false,
      enableMultiAgentCollaboration: false,
    };
  }

  /**
   * Tạo cấu hình cơ bản (profile, output, resources)
   */
  static createBasicConfig(): TypeAgentConfig {
    return {
      enableAgentProfileCustomization: false,
      enableOutputToMessenger: false,
      enableOutputToWebsiteLiveChat: false,
      enableTaskConversionTracking: false,
      enableResourceUsage: false,
      enableDynamicStrategyExecution: false,
      enableMultiAgentCollaboration: false,
    };
  }

  /**
   * T<PERSON>o cấu hình đầy đủ với tất cả các tính năng
   */
  static createFullConfig(): TypeAgentConfig {
    return {
      enableAgentProfileCustomization: false,
      enableOutputToMessenger: false,
      enableOutputToWebsiteLiveChat: false,
      enableTaskConversionTracking: false,
      enableResourceUsage: false,
      enableDynamicStrategyExecution: false,
      enableMultiAgentCollaboration: false,
    };
  }

  /**
   * Validate cấu hình TypeAgent
   * @param config Cấu hình cần validate
   * @returns true nếu hợp lệ, false nếu không
   */
  static validateConfig(config: any): config is TypeAgentConfig {
    if (!config || typeof config !== 'object') {
      return false;
    }

    const requiredFields: (keyof TypeAgentConfig)[] = [
      'enableAgentProfileCustomization',
      'enableOutputToMessenger',
      'enableOutputToWebsiteLiveChat',
      'enableTaskConversionTracking',
      'enableResourceUsage',
      'enableDynamicStrategyExecution',
      'enableMultiAgentCollaboration',
    ];

    return requiredFields.every(field =>
      field in config && typeof config[field] === 'boolean'
    );
  }

  /**
   * Merge cấu hình với cấu hình mặc định
   * @param config Cấu hình partial
   * @returns Cấu hình đầy đủ
   */
  static mergeWithDefault(config: Partial<TypeAgentConfig>): TypeAgentConfig {
    const defaultConfig = this.createDefaultConfig();
    return {
      ...defaultConfig,
      ...config,
    };
  }

  /**
   * Lấy danh sách tất cả các trường trong TypeAgentConfig
   * Hữu ích để tự động tạo form hoặc validation
   */
  static getConfigFields(): (keyof TypeAgentConfig)[] {
    return [
      'enableAgentProfileCustomization',
      'enableOutputToMessenger',
      'enableOutputToWebsiteLiveChat',
      'enableTaskConversionTracking',
      'enableResourceUsage',
      'enableDynamicStrategyExecution',
      'enableMultiAgentCollaboration',
    ];
  }

  /**
   * Lấy mô tả cho từng trường
   */
  static getFieldDescriptions(): Record<keyof TypeAgentConfig, string> {
    return {
      enableAgentProfileCustomization: 'Có hồ sơ không - Cho phép agent có thông tin hồ sơ cá nhân',
      enableOutputToMessenger: 'Có đầu ra không - Cho phép agent tạo ra các output/kết quả',
      enableOutputToWebsiteLiveChat: 'Có đầu ra không - Cho phép agent tạo ra các output/kết quả',
      enableTaskConversionTracking: 'Có chuyển đổi không - Cho phép agent thực hiện các conversion/chuyển đổi dữ liệu',
      enableResourceUsage: 'Có tài nguyên không - Cho phép agent sử dụng các resources/tài nguyên',
      enableDynamicStrategyExecution: 'Có chiến lược không - Cho phép agent sử dụng các strategy/chiến lược',
      enableMultiAgentCollaboration: 'Có đa agent không - Cho phép agent làm việc với nhiều agent khác',
    };
  }
}
