import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AgentMedia, AgentUrl, AgentProduct } from '@modules/agent/entities';
import { PaginatedResult } from '@common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentMediaQueryDto, AgentUrlQueryDto, AgentProductQueryDto } from '@modules/agent/user/dto';

/**
 * Repository cho AgentMedia
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến media của agent
 */
@Injectable()
export class AgentMediaRepository extends Repository<AgentMedia> {
  private readonly logger = new Logger(AgentMediaRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentMedia, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentMedia
   * @returns SelectQueryBuilder cho AgentMedia
   */
  private createBaseQuery(): SelectQueryBuilder<AgentMedia> {
    return this.createQueryBuilder('agentMedia')
      .select(['agentMedia.mediaId', 'agentMedia.agentId']);
  }

  /**
   * Lấy danh sách media của agent có phân trang
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách media có phân trang
   */
  async findPaginated(
    agentId: string,
    queryDto: AgentMediaQueryDto,
  ): Promise<PaginatedResult<AgentMedia>> {
    try {
      // Tạo query builder
      const qb = this.createBaseQuery()
        .where('agentMedia.agentId = :agentId', { agentId })
        .skip((queryDto.page - 1) * queryDto.limit)
        .take(queryDto.limit);

      // Lấy kết quả
      const [items, total] = await qb.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách media của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm media vào agent
   * @param agentId ID của agent
   * @param mediaId ID của media
   */
  @Transactional()
  async addMedia(agentId: string, mediaId: string): Promise<void> {
    try {
      // Kiểm tra media đã được thêm vào agent chưa
      const existingAgentMedia = await this.findOne({
        where: { agentId, mediaId },
      });

      if (!existingAgentMedia) {
        // Thêm media vào agent
        const agentMedia = this.create({
          agentId,
          mediaId,
        });
        await this.save(agentMedia);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi thêm media vào agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách media của agent
   * @param agentId ID của agent
   * @returns Danh sách media của agent
   */
  async findByAgentId(agentId: string): Promise<AgentMedia[]> {
    try {
      return this.createBaseQuery()
        .where('agentMedia.agentId = :agentId', { agentId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách media của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa media khỏi agent
   * @param agentId ID của agent
   * @param mediaId ID của media
   */
  @Transactional()
  async removeMedia(agentId: string, mediaId: string): Promise<void> {
    try {
      // Xóa media khỏi agent
      const qb = this.createQueryBuilder()
        .delete()
        .where('agentId = :agentId', { agentId })
        .andWhere('mediaId = :mediaId', { mediaId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa media khỏi agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }
}

/**
 * Repository cho AgentUrl
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến URL của agent
 */
@Injectable()
export class AgentUrlRepository extends Repository<AgentUrl> {
  private readonly logger = new Logger(AgentUrlRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentUrl, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentUrl
   * @returns SelectQueryBuilder cho AgentUrl
   */
  private createBaseQuery(): SelectQueryBuilder<AgentUrl> {
    return this.createQueryBuilder('agentUrl')
      .select(['agentUrl.urlId', 'agentUrl.agentId']);
  }

  /**
   * Lấy danh sách URL của agent có phân trang
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  async findPaginated(
    agentId: string,
    queryDto: AgentUrlQueryDto,
  ): Promise<PaginatedResult<AgentUrl>> {
    try {
      // Tạo query builder
      const qb = this.createBaseQuery()
        .where('agentUrl.agentId = :agentId', { agentId })
        .skip((queryDto.page - 1) * queryDto.limit)
        .take(queryDto.limit);

      // Lấy kết quả
      const [items, total] = await qb.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách URL của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm URL vào agent
   * @param agentId ID của agent
   * @param urlId ID của URL
   */
  @Transactional()
  async addUrl(agentId: string, urlId: string): Promise<void> {
    try {
      // Kiểm tra URL đã được thêm vào agent chưa
      const existingAgentUrl = await this.findOne({
        where: { agentId, urlId },
      });

      if (!existingAgentUrl) {
        // Thêm URL vào agent
        const agentUrl = this.create({
          agentId,
          urlId,
        });
        await this.save(agentUrl);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách URL của agent
   * @param agentId ID của agent
   * @returns Danh sách URL của agent
   */
  async findByAgentId(agentId: string): Promise<AgentUrl[]> {
    try {
      return this.createBaseQuery()
        .where('agentUrl.agentId = :agentId', { agentId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa URL khỏi agent
   * @param agentId ID của agent
   * @param urlId ID của URL
   */
  @Transactional()
  async removeUrl(agentId: string, urlId: string): Promise<void> {
    try {
      // Xóa URL khỏi agent
      const qb = this.createQueryBuilder()
        .delete()
        .where('agentId = :agentId', { agentId })
        .andWhere('urlId = :urlId', { urlId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa URL khỏi agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }
}

/**
 * Repository cho AgentProduct
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến sản phẩm của agent
 */
@Injectable()
export class AgentProductRepository extends Repository<AgentProduct> {
  private readonly logger = new Logger(AgentProductRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentProduct, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentProduct
   * @returns SelectQueryBuilder cho AgentProduct
   */
  private createBaseQuery(): SelectQueryBuilder<AgentProduct> {
    return this.createQueryBuilder('agentProduct')
      .select(['agentProduct.productId', 'agentProduct.agentId']);
  }

  /**
   * Lấy danh sách sản phẩm của agent có phân trang
   * @param agentId ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách sản phẩm có phân trang
   */
  async findPaginated(
    agentId: string,
    queryDto: AgentProductQueryDto,
  ): Promise<PaginatedResult<AgentProduct>> {
    try {
      // Tạo query builder
      const qb = this.createBaseQuery()
        .where('agentProduct.agentId = :agentId', { agentId })
        .skip((queryDto.page - 1) * queryDto.limit)
        .take(queryDto.limit);

      // Lấy kết quả
      const [items, total] = await qb.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi truy vấn danh sách sản phẩm của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm sản phẩm vào agent
   * @param agentId ID của agent
   * @param productId ID của sản phẩm
   */
  @Transactional()
  async addProduct(agentId: string, productId: string): Promise<void> {
    try {
      // Kiểm tra sản phẩm đã được thêm vào agent chưa
      const existingAgentProduct = await this.findOne({
        where: { agentId, productId: parseInt(productId) },
      });

      if (!existingAgentProduct) {
        // Thêm sản phẩm vào agent
        const agentProduct = this.create({
          agentId,
          productId: parseInt(productId),
        });
        await this.save(agentProduct);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi thêm sản phẩm vào agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách sản phẩm của agent
   * @param agentId ID của agent
   * @returns Danh sách sản phẩm của agent
   */
  async findByAgentId(agentId: string): Promise<AgentProduct[]> {
    try {
      return this.createBaseQuery()
        .where('agentProduct.agentId = :agentId', { agentId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách sản phẩm của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa sản phẩm khỏi agent
   * @param agentId ID của agent
   * @param productId ID của sản phẩm
   */
  @Transactional()
  async removeProduct(agentId: string, productId: string): Promise<void> {
    try {
      // Xóa sản phẩm khỏi agent
      const qb = this.createQueryBuilder()
        .delete()
        .where('agentId = :agentId', { agentId })
        .andWhere('productId = :productId', { productId: parseInt(productId) });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa sản phẩm khỏi agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }
}