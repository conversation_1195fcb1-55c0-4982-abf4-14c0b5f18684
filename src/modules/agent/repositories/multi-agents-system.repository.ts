import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { MultiAgentsSystem } from '../entities/multi-agents-system.entity';
import { AppException } from '@/common/exceptions';
import { AGENT_ERROR_CODES } from '../exceptions';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho MultiAgentsSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ đa cấp giữa các agent
 */
@Injectable()
export class MultiAgentsSystemRepository extends Repository<MultiAgentsSystem> {
  private readonly logger = new Logger(MultiAgentsSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(MultiAgentsSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho MultiAgentsSystem
   * @returns SelectQueryBuilder cho MultiAgentsSystem
   */
  private createBaseQuery(): SelectQueryBuilder<MultiAgentsSystem> {
    return this.createQueryBuilder('multiAgentsSystem');
  }

  /**
   * Tìm quan hệ đa cấp theo parentAgentId và childAgentId
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   * @returns MultiAgentsSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByParentAndChildIds(parentAgentId: string, childAgentId: string): Promise<MultiAgentsSystem | null> {
    return this.createBaseQuery()
      .where('multiAgentsSystem.parentAgentId = :parentAgentId', { parentAgentId })
      .andWhere('multiAgentsSystem.childAgentId = :childAgentId', { childAgentId })
      .getOne();
  }

  /**
   * Tìm tất cả quan hệ đa cấp theo parentAgentId
   * @param parentAgentId ID của agent cấp trên
   * @returns Danh sách các quan hệ đa cấp
   */
  async findByParentAgentId(parentAgentId: string): Promise<MultiAgentsSystem[]> {
    return this.createBaseQuery()
      .where('multiAgentsSystem.parentAgentId = :parentAgentId', { parentAgentId })
      .getMany();
  }

  /**
   * Tìm tất cả quan hệ đa cấp theo childAgentId
   * @param childAgentId ID của agent cấp dưới
   * @returns Danh sách các quan hệ đa cấp
   */
  async findByChildAgentId(childAgentId: string): Promise<MultiAgentsSystem[]> {
    return this.createBaseQuery()
      .where('multiAgentsSystem.childAgentId = :childAgentId', { childAgentId })
      .getMany();
  }

  /**
   * Tìm danh sách quan hệ đa cấp với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param parentAgentId ID của agent cấp trên (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách quan hệ đa cấp với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    parentAgentId?: string,
    sortBy: string = 'parentAgentId',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
  ): Promise<PaginatedResult<MultiAgentsSystem>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện lọc theo parentAgentId nếu có
    if (parentAgentId) {
      qb.andWhere('multiAgentsSystem.parentAgentId = :parentAgentId', { parentAgentId });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`multiAgentsSystem.${sortBy}`, sortDirection);

    // Lấy kết quả
    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tạo mới quan hệ đa cấp
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   * @param prompt Prompt cho quan hệ
   * @returns Quan hệ đa cấp đã tạo
   */
  @Transactional()
  async createRelation(
    parentAgentId: string,
    childAgentId: string,
    prompt?: string,
  ): Promise<MultiAgentsSystem> {
    try {
      // Kiểm tra xem quan hệ đã tồn tại chưa
      const existingRelation = await this.findByParentAndChildIds(parentAgentId, childAgentId);
      if (existingRelation) {
        throw new AppException(
          AGENT_ERROR_CODES.RELATION_ALREADY_EXISTS,
          `Quan hệ giữa agent ${parentAgentId} và ${childAgentId} đã tồn tại`,
        );
      }

      // Tạo quan hệ mới
      const relation = this.create({
        parentAgentId,
        childAgentId,
        prompt,
      });

      // Lưu quan hệ
      return await this.save(relation);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo quan hệ đa cấp: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật prompt cho quan hệ đa cấp
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   * @param prompt Prompt mới
   */
  @Transactional()
  async updatePrompt(
    parentAgentId: string,
    childAgentId: string,
    prompt: string,
  ): Promise<void> {
    try {
      const qb = this.createQueryBuilder()
        .update(MultiAgentsSystem)
        .set({ prompt })
        .where('parentAgentId = :parentAgentId', { parentAgentId })
        .andWhere('childAgentId = :childAgentId', { childAgentId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.RELATION_NOT_FOUND,
          `Không tìm thấy quan hệ giữa agent ${parentAgentId} và ${childAgentId}`,
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật prompt: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa quan hệ đa cấp
   * @param parentAgentId ID của agent cấp trên
   * @param childAgentId ID của agent cấp dưới
   */
  @Transactional()
  async deleteRelation(
    parentAgentId: string,
    childAgentId: string,
  ): Promise<void> {
    try {
      const qb = this.createQueryBuilder()
        .delete()
        .from(MultiAgentsSystem)
        .where('parentAgentId = :parentAgentId', { parentAgentId })
        .andWhere('childAgentId = :childAgentId', { childAgentId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(
          AGENT_ERROR_CODES.RELATION_NOT_FOUND,
          `Không tìm thấy quan hệ giữa agent ${parentAgentId} và ${childAgentId}`,
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa quan hệ đa cấp: ${error.message}`);
      throw error;
    }
  }
}
