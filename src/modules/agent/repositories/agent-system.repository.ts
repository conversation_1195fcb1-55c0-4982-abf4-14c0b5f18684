import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentSystem } from '@modules/agent/entities';

/**
 * Repository cho AgentSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent hệ thống
 */
@Injectable()
export class AgentSystemRepository extends Repository<AgentSystem> {
  private readonly logger = new Logger(AgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentSystem
   * @returns SelectQueryBuilder cho AgentSystem
   */
  private createBaseQuery(): SelectQueryBuilder<AgentSystem> {
    return this.createQueryBuilder('agentSystem');
  }

  /**
   * Tìm agent hệ thống theo ID
   * @param id ID của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.id = :id', { id })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  /**
   * Tìm agent hệ thống theo mã định danh
   * @param nameCode Mã định danh của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByNameCode(nameCode: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.nameCode = :nameCode', { nameCode })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  /**
   * Lấy danh sách agent system đã xóa với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent system đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedBy',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: AgentSystem[]; total: number; deletedEmployees?: any[] }> {
    // Query chính để lấy agent systems đã xóa
    const qb = this.createBaseQuery()
      .where('agentSystem.deletedBy IS NOT NULL')
      .withDeleted();

    // Thêm điều kiện tìm kiếm nếu có (tìm theo nameCode)
    if (search) {
      qb.andWhere('agentSystem.nameCode ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Thêm sắp xếp
    qb.orderBy(`agentSystem.${sortBy === 'deletedBy' ? 'createdAt' : sortBy}`, sortDirection);

    // Thêm phân trang
    qb.skip((page - 1) * limit).take(limit);

    // Lấy danh sách agent systems đã xóa
    const [items, total] = await qb.getManyAndCount();

    // Lấy thông tin employees đã xóa (batch query)
    const deletedEmployeeIds = [...new Set(items.map(item => item.deletedBy).filter(Boolean))];
    let deletedEmployees: any[] = [];

    if (deletedEmployeeIds.length > 0) {
      const employeeQb = this.dataSource.createQueryBuilder()
        .select([
          'employee.id',
          'employee.fullName',
          'employee.avatar'
        ])
        .from('employees', 'employee')
        .where('employee.id IN (:...ids)', { ids: deletedEmployeeIds });

      const employeeResults = await employeeQb.getRawMany();

      // Map employee info với agent systems
      deletedEmployees = items.map(agentSystem => {
        const employee = employeeResults.find(emp => emp.employee_id === agentSystem.deletedBy);
        return {
          agentSystemId: agentSystem.id,
          employeeId: agentSystem.deletedBy,
          employeeName: employee?.employee_fullName || 'Unknown',
          employeeAvatar: employee?.employee_avatar || null,
        };
      });
    }

    return {
      items,
      total,
      deletedEmployees,
    };
  }

  /**
   * Khôi phục agent system đã xóa
   * @param id ID của agent system cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns true nếu khôi phục thành công
   */
  async restoreAgentSystem(id: string, employeeId: number): Promise<boolean> {
    try {
      // Khôi phục agent system
      const result = await this.restore(id);

      if (result.affected && result.affected > 0) {
        // Cập nhật thông tin người khôi phục
        await this.update(id, {
          deletedBy: null,
          updatedBy: employeeId,
        });

        this.logger.debug(`Đã khôi phục agent system với ID ${id}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục agent system ${id}: ${error.message}`, error.stack);
      return false;
    }
  }
}
