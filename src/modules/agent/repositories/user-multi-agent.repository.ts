import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserMultiAgent } from '@modules/agent/entities/user-multi-agent.entity';
import { Agent } from '@modules/agent/entities/agent.entity';
import { AgentUser } from '@modules/agent/entities/agents-user.entity';

/**
 * Repository cho UserMultiAgent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ đa cấp giữa các agent
 */
@Injectable()
export class UserMultiAgentRepository extends Repository<UserMultiAgent> {
  private readonly logger = new Logger(UserMultiAgentRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserMultiAgent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho UserMultiAgent
   * @returns SelectQueryBuilder cho UserMultiAgent
   */
  private createBaseQuery(): SelectQueryBuilder<UserMultiAgent> {
    return this.createQueryBuilder('userMultiAgent');
  }

  /**
   * Tìm quan hệ giữa agent cha và agent con
   * @param parentAgentId ID của agent cha
   * @param childAgentId ID của agent con
   * @returns UserMultiAgent nếu tìm thấy, null nếu không tìm thấy
   */
  async findByParentIdAndChildId(
    parentAgentId: string,
    childAgentId: string,
  ): Promise<UserMultiAgent | null> {
    return this.createBaseQuery()
      .where('userMultiAgent.parentAgentId = :parentAgentId', { parentAgentId })
      .andWhere('userMultiAgent.childAgentId = :childAgentId', { childAgentId })
      .getOne();
  }

  /**
   * Tìm tất cả các agent con của một agent cha
   * @param parentAgentId ID của agent cha
   * @returns Danh sách các UserMultiAgent
   */
  async findChildrenByParentId(parentAgentId: string): Promise<UserMultiAgent[]> {
    return this.createBaseQuery()
      .where('userMultiAgent.parentAgentId = :parentAgentId', { parentAgentId })
      .getMany();
  }

  /**
   * Tìm tất cả các agent con của một agent cha kèm theo thông tin chi tiết
   * @param parentAgentId ID của agent cha
   * @param userId ID của người dùng
   * @returns Danh sách các agent con với thông tin chi tiết
   */
  async findChildrenWithDetailsByParentId(
    parentAgentId: string,
    userId: number,
  ): Promise<{ agent: Agent; agentUser: AgentUser; relation: UserMultiAgent }[]> {
    try {
      // Lấy danh sách các quan hệ
      const relations = await this.findChildrenByParentId(parentAgentId);

      if (!relations.length) {
        return [];
      }

      // Lấy danh sách ID của các agent con
      const childIds = relations.map(relation => relation.childAgentId);

      // Lấy thông tin chi tiết của các agent con
      const agentQb = this.dataSource
        .getRepository(Agent)
        .createQueryBuilder('agent')
        .where('agent.id IN (:...ids)', { ids: childIds })
        .andWhere('agent.deletedAt IS NULL');

      const agents = await agentQb.getMany();

      // Lấy thông tin AgentUser của các agent con
      const agentUserQb = this.dataSource
        .getRepository(AgentUser)
        .createQueryBuilder('agentUser')
        .where('agentUser.id IN (:...ids)', { ids: childIds })
        .andWhere('agentUser.userId = :userId', { userId });

      const agentUsers = await agentUserQb.getMany();

      // Kết hợp thông tin
      const result: { agent: Agent; agentUser: AgentUser; relation: UserMultiAgent }[] = [];
      for (const relation of relations) {
        const agent = agents.find(a => a.id === relation.childAgentId);
        const agentUser = agentUsers.find(au => au.id === relation.childAgentId);

        if (agent && agentUser) {
          result.push({ agent, agentUser, relation });
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm agent con: ${error.message}`);
      throw error;
    }
  }
}
