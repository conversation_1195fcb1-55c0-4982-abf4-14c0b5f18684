import { ApiProperty } from '@nestjs/swagger';
import { ContractStatusEnum, ContractTypeEnum } from '../../entities/rule-contract.entity';

/**
 * DTO cho phản hồi thông tin hợp đồng nguyên tắc mở rộng (user)
 */
export class RuleContractExtendedResponseDto {
  /**
   * Trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatusEnum,
    example: ContractStatusEnum.DRAFT,
  })
  status: ContractStatusEnum;

  /**
   * <PERSON><PERSON><PERSON> hợp đồng
   */
  @ApiProperty({
    description: 'Loại hợp đồng',
    enum: ContractTypeEnum,
    example: ContractTypeEnum.INDIVIDUAL,
  })
  type: ContractTypeEnum;

  /**
   * Nội dung hợp đồng dạng Base64
   */
  @ApiProperty({
    description: 'Nội dung hợp đồng dạng Base64',
    example: 'JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PC9GaWx0ZXIvRmxhdGVEZWNvZGUvTGVuZ3RoIDM...',
    type: String,
  })
  contractBase64: string;

  /**
   * URL tải hợp đồng
   */
  @ApiProperty({
    description: 'URL tải hợp đồng',
    example: 'https://cdn.example.com/contracts/contract-123.pdf',
    type: String,
  })
  contractUrl: string;
}
