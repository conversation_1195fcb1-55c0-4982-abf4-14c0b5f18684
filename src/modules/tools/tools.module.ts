import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolsAdminModule } from './admin/tools-admin.module';
import { ToolsUserModule } from './user/tools-user.module';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion,
  ApiKey,
  OAuth,
  UserToolsCustom
} from './entities';
import { TypeAgent } from '@modules/agent/entities';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  TypeAgentRepository,
  ApiKeyRepository,
  OAuthRepository,
  UserToolsCustomRepository
} from './repositories';
import { ToolValidationHelper } from './helpers/tool-validation.helper';
import { ToolFunctionValidationHelper } from './helpers/tool-function-validation.helper';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminTool,
      UserTool,
      AdminToolVersion,
      UserToolVersion,
      TypeAgent,
      ApiKey,
      OAuth,
      UserToolsCustom
    ]),
    ToolsAdminModule,
    ToolsUserModule
  ],
  providers: [
    AdminToolRepository,
    UserToolRepository,
    AdminToolVersionRepository,
    UserToolVersionRepository,
    TypeAgentRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository,
    ToolValidationHelper,
    ToolFunctionValidationHelper
  ],
  exports: [
    ToolsAdminModule,
    ToolsUserModule,
    UserToolRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository,
    ToolValidationHelper,
    ToolFunctionValidationHelper
  ],
})
export class ToolsModule {}
