import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { ApiKey } from '../entities/api-key.entity';
import { AppException } from '@common/exceptions';
import { CUSTOM_TOOLS_ERROR_CODES } from '../exceptions';

@Injectable()
export class ApiKeyRepository extends Repository<ApiKey> {
  private readonly logger = new Logger(ApiKeyRepository.name);

  // Không cần sử dụng Map để lưu trữ paramName nữa vì đã lưu trong cơ sở dữ liệu

  constructor(private dataSource: DataSource) {
    super(ApiKey, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho API Key
   * @returns SelectQueryBuilder<ApiKey> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<ApiKey> {
    return this.createQueryBuilder('apiKey');
  }

  /**
   * Tìm API Key theo ID
   * @param id ID của API Key cần tìm
   * @returns API Key nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<ApiKey | null> {
    try {
      const apiKey = await this.createBaseQuery()
        .where('apiKey.id = :id', { id })
        .getOne();

      // Không cần làm gì thêm vì paramName đã được lưu trong cơ sở dữ liệu
      // Nếu paramName là null, sử dụng giá trị mặc định
      if (apiKey && !apiKey.paramName) {
        apiKey.paramName = 'X-API-KEY';
      }

      return apiKey;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm API Key theo ID: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_NOT_FOUND, `Không tìm thấy API Key với ID ${id}`);
    }
  }

  /**
   * Tạo mới API Key
   * @param apiKey Dữ liệu API Key cần tạo
   * @returns API Key đã tạo
   */
  @Transactional()
  async createApiKey(apiKey: Partial<ApiKey>): Promise<ApiKey> {
    try {
      // Tạo bản sao của đối tượng apiKey để tránh thay đổi đối tượng gốc
      const { paramName, ...apiKeyData } = apiKey as any;

      // Lưu trữ paramName vào bộ nhớ tạm thời
      const storedParamName = paramName;

      // Không cần tạo entity, chỉ cần lấy các trường cần thiết từ apiKeyData
      const { schemeName, apiKey: apiKeyValue, apiKeyLocation } = apiKeyData;

      // Thực hiện raw query bao gồm cột param_name
      const insertResult = await this.dataSource.query(
        `INSERT INTO "api_key"("scheme_name", "api_key", "api_key_location", "param_name", "created_at", "updated_at")
         VALUES($1, $2, $3, $4, (EXTRACT(epoch FROM now()) * 1000)::bigint, (EXTRACT(epoch FROM now()) * 1000)::bigint)
         RETURNING "id", "scheme_name", "api_key", "api_key_location", "param_name", "created_at", "updated_at"`,
        [schemeName, apiKeyValue, apiKeyLocation, paramName || 'X-API-KEY']
      );

      // Lấy entity đã được lưu
      const apiKeyEntity = insertResult[0] as ApiKey;

      if (!apiKeyEntity || !apiKeyEntity.id) {
        throw new Error('Không thể lưu API Key');
      }

      // Không cần lưu paramName vào Map vì đã lưu vào cơ sở dữ liệu
      // Nếu paramName không có trong kết quả trả về, sử dụng giá trị mặc định
      if (!apiKeyEntity.paramName) {
        apiKeyEntity.paramName = storedParamName || 'X-API-KEY';
      }

      return apiKeyEntity;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo API Key: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_CREATE_FAILED, 'Tạo API Key thất bại');
    }
  }

  /**
   * Cập nhật API Key
   * @param id ID của API Key cần cập nhật
   * @param apiKey Dữ liệu cập nhật
   * @returns Void
   */
  @Transactional()
  async updateApiKey(id: string, apiKey: Partial<ApiKey>): Promise<void> {
    try {
      // Tách paramName từ dữ liệu cập nhật để xử lý riêng
      const { paramName, ...apiKeyData } = apiKey as any;

      // Lấy các trường cần thiết từ apiKeyData
      const updateFields: string[] = [];
      const params: any[] = [id]; // Tham số đầu tiên là id
      let paramIndex = 2; // Bắt đầu từ $2

      // Thêm các trường cần cập nhật vào câu lệnh SQL
      if (apiKeyData.schemeName !== undefined) {
        updateFields.push(`"scheme_name" = $${paramIndex++}`);
        params.push(apiKeyData.schemeName);
      }

      if (apiKeyData.apiKey !== undefined) {
        updateFields.push(`"api_key" = $${paramIndex++}`);
        params.push(apiKeyData.apiKey);
      }

      if (apiKeyData.apiKeyLocation !== undefined) {
        updateFields.push(`"api_key_location" = $${paramIndex++}`);
        params.push(apiKeyData.apiKeyLocation);
      }

      // Thêm paramName vào danh sách cập nhật nếu có
      if (paramName !== undefined) {
        updateFields.push(`"param_name" = $${paramIndex++}`);
        params.push(paramName);
      }

      // Luôn cập nhật updatedAt
      updateFields.push(`"updated_at" = (EXTRACT(epoch FROM now()) * 1000)::bigint`);

      // Nếu không có trường nào cần cập nhật, chỉ cập nhật updatedAt
      if (updateFields.length === 1) {
        updateFields.push(`"updated_at" = (EXTRACT(epoch FROM now()) * 1000)::bigint`);
      }

      // Thực hiện raw query
      const result = await this.dataSource.query(
        `UPDATE "api_key" SET ${updateFields.join(', ')} WHERE "id" = $1 RETURNING *`,
        params
      );

      // Kiểm tra kết quả cập nhật
      if (!result || result.length === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_NOT_FOUND, `Không tìm thấy API Key với ID ${id}`);
      }

      // Không cần cập nhật paramName trong Map nữa vì đã lưu trong cơ sở dữ liệu
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật API Key: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_UPDATE_FAILED, 'Cập nhật API Key thất bại');
    }
  }

  /**
   * Xóa API Key
   * @param id ID của API Key cần xóa
   * @returns Void
   */
  @Transactional()
  async deleteApiKey(id: string): Promise<void> {
    try {
      // Sử dụng raw query để xóa
      const result = await this.dataSource.query(
        `DELETE FROM "api_key" WHERE "id" = $1 RETURNING *`,
        [id]
      );

      // Kiểm tra kết quả xóa
      if (!result || result.length === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_NOT_FOUND, `Không tìm thấy API Key với ID ${id}`);
      }

      // Không cần xóa paramName khỏi Map nữa vì đã lưu trong cơ sở dữ liệu
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa API Key: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.API_KEY_DELETE_FAILED, 'Xóa API Key thất bại');
    }
  }
}
