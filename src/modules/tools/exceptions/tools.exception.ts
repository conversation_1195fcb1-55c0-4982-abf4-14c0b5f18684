import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const TOOLS_ERROR_CODES = {
  TOOL_NOT_FOUND: new ErrorCode(
    20301,
    'Không tìm thấy công cụ',
    HttpStatus.NOT_FOUND,
  ),

  TOOL_VERSION_NOT_FOUND: new ErrorCode(
    20302,
    'Không tìm thấy phiên bản công cụ',
    HttpStatus.NOT_FOUND,
  ),

  TOOL_ALREADY_EXISTS: new ErrorCode(
    20303,
    'Công cụ với tên này đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  TOOL_NAME_INVALID: new ErrorCode(
    20304,
    'Tên công cụ không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  TOOL_PARAMETERS_INVALID: new ErrorCode(
    20305,
    'Tham số công cụ không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  TOOL_ACCESS_DENIED: new ErrorCode(
    20306,
    'Không có quyền truy cập công cụ này',
    HttpStatus.FORBIDDEN,
  ),

  TOOL_STATUS_INVALID: new ErrorCode(
    20307,
    'Trạng thái công cụ không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  TOOL_GROUP_NOT_FOUND: new ErrorCode(
    20308,
    'Không tìm thấy nhóm công cụ',
    HttpStatus.NOT_FOUND,
  ),

  TOOL_VERSION_ALREADY_EXISTS: new ErrorCode(
    20309,
    'Phiên bản công cụ đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  TOOL_CREATION_FAILED: new ErrorCode(
    20310,
    'Tạo công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_UPDATE_FAILED: new ErrorCode(
    20311,
    'Cập nhật công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_DELETE_FAILED: new ErrorCode(
    20312,
    'Xóa công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_VERSION_CREATION_FAILED: new ErrorCode(
    20313,
    'Tạo phiên bản công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_VERSION_UPDATE_FAILED: new ErrorCode(
    20314,
    'Cập nhật phiên bản công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_VERSION_DELETE_FAILED: new ErrorCode(
    20315,
    'Xóa phiên bản công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_VERSION_INVALID: new ErrorCode(
    20316,
    'Phiên bản công cụ không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  TOOL_VERSION_EDIT_FAILED: new ErrorCode(
    20317,
    'Chỉnh sửa phiên bản công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TOOL_VERSION_ROLLBACK_FAILED: new ErrorCode(
    20318,
    'Khôi phục phiên bản công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  FETCH_FAILED: new ErrorCode(
    20319,
    'Lỗi khi lấy dữ liệu công cụ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Error codes for group tools
  GROUP_TOOL_NOT_FOUND: new ErrorCode(
    20320,
    'Không tìm thấy nhóm công cụ',
    HttpStatus.NOT_FOUND,
  ),

  GROUP_TOOL_NAME_EXISTS: new ErrorCode(
    20321,
    'Nhóm công cụ với tên này đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  GROUP_TOOL_CREATE_FAILED: new ErrorCode(
    20322,
    'Tạo nhóm công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GROUP_TOOL_UPDATE_FAILED: new ErrorCode(
    20323,
    'Cập nhật nhóm công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GROUP_TOOL_DELETE_FAILED: new ErrorCode(
    20324,
    'Xóa nhóm công cụ thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GROUP_TOOL_FETCH_FAILED: new ErrorCode(
    20325,
    'Lỗi khi lấy dữ liệu nhóm công cụ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  TYPE_AGENT_NOT_FOUND: new ErrorCode(
    20326,
    'Không tìm thấy loại agent',
    HttpStatus.NOT_FOUND,
  ),

  TOOL_NOT_AVAILABLE: new ErrorCode(
    20327,
    'Tool này đã bị xóa và không còn khả dụng',
    HttpStatus.FORBIDDEN,
  ),

  INVALID_TOOL_IDS: new ErrorCode(
    20328,
    'Danh sách ID tool không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};
