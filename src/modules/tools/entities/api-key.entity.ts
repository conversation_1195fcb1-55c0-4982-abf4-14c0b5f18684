import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiKeyLocationEnum } from '../constants';

/**
 * Entity đại diện cho bảng api_key trong cơ sở dữ liệu
 * Bảng lưu trữ cấu hình xác thực API Key cho các công cụ tùy chỉnh
 */
@Entity('api_key')
export class ApiKey {
  /**
   * Mã định danh duy nhất, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên security scheme từ Swagger (ví dụ: apiKey)
   */
  @Column({ name: 'scheme_name', length: 50 })
  schemeName: string;

  /**
   * Giá trị API Key
   */
  @Column({ name: 'api_key', type: 'text' })
  apiKey: string;

  /**
   * Vị trí gửi API Key (header, query, cookie)
   */
  @Column({
    name: 'api_key_location',
    length: 20,
    type: 'varchar',
    enum: ApiKeyLocationEnum,
  })
  apiKeyLocation: ApiKeyLocationEnum;

  /**
   * Tên tham số API Key (ví dụ: X-API-KEY, api_key)
   */
  @Column({ name: 'param_name', length: 50, nullable: false, default: 'X-API-KEY' })
  paramName: string;

  /**
   * Thời điểm tạo, tính bằng mili giây
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật, tính bằng mili giây
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;
}
