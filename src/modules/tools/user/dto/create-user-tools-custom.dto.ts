import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';
import { HttpMethodEnum, ToolStatusEnum } from '@/modules/tools/constants';

/**
 * DTO cho việc tạo mới công cụ tùy chỉnh của người dùng
 */
export class CreateUserToolsCustomDto {
  /**
   * Tên của công cụ tùy chỉnh
   */
  @ApiProperty({
    description: 'Tên của công cụ tùy chỉnh',
    example: 'Công cụ tìm kiếm sản phẩm',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên công cụ không được để trống' })
  @IsString({ message: 'Tên công cụ phải là chuỗi' })
  @MaxLength(64, { message: 'Tên công cụ không được vượt quá 64 ký tự' })
  toolName: string;

  /**
   * <PERSON><PERSON> tả chi tiết về chức năng của công cụ
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của công cụ',
    example: 'Công cụ này giúp tìm kiếm sản phẩm theo nhiều tiêu chí khác nhau',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả công cụ phải là chuỗi' })
  toolDescription?: string;

  /**
   * Đường dẫn endpoint của công cụ (ví dụ: /users)
   */
  @ApiProperty({
    description: 'Đường dẫn endpoint của công cụ',
    example: '/products/search',
    required: true,
  })
  @IsNotEmpty({ message: 'Endpoint không được để trống' })
  @IsString({ message: 'Endpoint phải là chuỗi' })
  @MaxLength(255, { message: 'Endpoint không được vượt quá 255 ký tự' })
  endpoint: string;

  /**
   * Phương thức HTTP (GET, POST, PUT, DELETE, PATCH)
   */
  @ApiProperty({
    description: 'Phương thức HTTP',
    enum: HttpMethodEnum,
    example: HttpMethodEnum.GET,
    required: true,
  })
  @IsNotEmpty({ message: 'Phương thức HTTP không được để trống' })
  @IsEnum(HttpMethodEnum, { message: 'Phương thức HTTP không hợp lệ' })
  method: HttpMethodEnum;

  /**
   * Đối tượng JSONB lưu trữ schema phản hồi theo chuẩn Swagger
   */
  @ApiProperty({
    description: 'Schema phản hồi theo chuẩn Swagger',
    example: { type: 'object', properties: { id: { type: 'string' } } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Schema phản hồi phải là đối tượng' })
  response?: Record<string, unknown>;

  /**
   * ID của cấu hình OAuth
   */
  @ApiProperty({
    description: 'ID của cấu hình OAuth',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID OAuth phải là UUID hợp lệ' })
  oauthId?: string;

  /**
   * ID của cấu hình API Key
   */
  @ApiProperty({
    description: 'ID của cấu hình API Key',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID('all', { message: 'ID API Key phải là UUID hợp lệ' })
  apiKeyId?: string;

  /**
   * URL cơ sở của API
   */
  @ApiProperty({
    description: 'URL cơ sở của API',
    example: 'https://api.example.com',
    required: true,
  })
  @IsNotEmpty({ message: 'URL cơ sở không được để trống' })
  @IsString({ message: 'URL cơ sở phải là chuỗi' })
  @MaxLength(255, { message: 'URL cơ sở không được vượt quá 255 ký tự' })
  baseUrl: string;

  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số truy vấn
   */
  @ApiProperty({
    description: 'Tham số truy vấn',
    example: { q: { type: 'string', required: true } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Tham số truy vấn phải là đối tượng' })
  queryParam?: Record<string, unknown>;

  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số đường dẫn
   */
  @ApiProperty({
    description: 'Tham số đường dẫn',
    example: { id: { type: 'string', required: true } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Tham số đường dẫn phải là đối tượng' })
  pathParam?: Record<string, unknown>;

  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số body
   */
  @ApiProperty({
    description: 'Tham số body',
    example: { name: { type: 'string', required: true } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Tham số body phải là đối tượng' })
  body?: Record<string, unknown>;
}
