import { ApiProperty } from '@nestjs/swagger';
import { HttpMethodEnum, ToolStatusEnum } from '../../constants';
import { AuthTypeEnum } from './auth-config.dto';

/**
 * DTO cho thông tin cơ bản về công cụ tùy chỉnh
 */
export class ToolResponseDto {
  /**
   * ID của công cụ
   */
  @ApiProperty({
    description: 'ID của công cụ',
    example: '550e8400-e29b-41d4-a716-446655440000'
  })
  id: string;

  /**
   * Tên công cụ
   */
  @ApiProperty({
    description: 'Tên công cụ',
    example: 'Công cụ tìm kiếm'
  })
  toolName: string;

  /**
   * Mô tả công cụ
   */
  @ApiProperty({
    description: 'Mô tả công cụ',
    example: 'Công cụ tìm kiếm thông tin từ API bên ngoài'
  })
  toolDescription: string;

  /**
   * Endpoint của công cụ
   */
  @ApiProperty({
    description: 'Endpoint của công cụ',
    example: '/api/search'
  })
  endpoint: string;

  /**
   * Phương thức HTTP
   */
  @ApiProperty({
    description: 'Phương thức HTTP',
    enum: HttpMethodEnum,
    example: HttpMethodEnum.GET
  })
  method: HttpMethodEnum;

  /**
   * Trạng thái kích hoạt của công cụ
   */
  @ApiProperty({
    description: 'Trạng thái kích hoạt của công cụ',
    example: true
  })
  active: boolean;

  /**
   * Loại xác thực
   */
  @ApiProperty({
    description: 'Loại xác thực',
    enum: AuthTypeEnum,
    example: AuthTypeEnum.API_KEY
  })
  authType: AuthTypeEnum;

  /**
   * Thời điểm tạo
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp)',
    example: 1625097600000
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp)',
    example: 1625097600000
  })
  updatedAt: number;
}

/**
 * DTO cho thông tin chi tiết về công cụ tùy chỉnh
 */
export class ToolDetailResponseDto extends ToolResponseDto {
  /**
   * Tham số của công cụ theo định dạng mới
   */
  @ApiProperty({
    description: 'Tham số của công cụ',
    example: {
      name: "Công cụ tìm kiếm",
      description: "Công cụ tìm kiếm thông tin",
      inputSchema: {
        type: "object",
        properties: {
          query_param: {
            type: "object",
            description: "Các tham số truy vấn",
            properties: {
              q: {
                type: "string",
                description: "Từ khóa tìm kiếm"
              }
            }
          },
          path_param: {
            type: "object",
            description: "Các tham số đường dẫn",
            properties: {}
          },
          body: {
            type: "object",
            description: "Các tham số body",
            properties: {}
          }
        }
      }
    }
  })
  parameters: {
    name: string;
    description: string;
    inputSchema: {
      type: string;
      properties: {
        query_param: {
          type: string;
          description: string;
          properties: Record<string, any>;
        };
        path_param: {
          type: string;
          description: string;
          properties: Record<string, any>;
        };
        body: {
          type: string;
          description: string;
          properties: Record<string, any>;
        };
      };
    };
  };

  /**
   * Base URL của API
   */
  @ApiProperty({
    description: 'Base URL của API',
    example: 'https://api.example.com'
  })
  baseUrl: string;

  /**
   * Thông tin bổ sung cho công cụ
   */
  @ApiProperty({
    description: 'Thông tin bổ sung cho công cụ',
    example: {
      url: 'https://api.example.com/search',
      method: 'GET',
      headers: {
        'X-API-KEY': '${apiKey}'
      }
    }
  })
  extra: {
    url: string;
    method: string;
    headers: Record<string, string>;
  };

  /**
   * Thông tin chi tiết về xác thực API Key (nếu có)
   */
  @ApiProperty({
    description: 'Thông tin chi tiết về xác thực API Key (nếu có)',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440001',
      schemeName: 'ApiKeyAuth',
      apiKeyLocation: 'header'
    },
    required: false
  })
  apiKeyAuth?: {
    id: string;
    schemeName: string;
    apiKeyLocation: string;
  };

  /**
   * Thông tin chi tiết về xác thực OAuth (nếu có)
   */
  @ApiProperty({
    description: 'Thông tin chi tiết về xác thực OAuth (nếu có)',
    example: {
      id: '550e8400-e29b-41d4-a716-446655440002',
      schemeName: 'OAuthAuth',
      tokenSource: 'jwt',
      tokenExpiresAt: 1625097600000
    },
    required: false
  })
  oauthAuth?: {
    id: string;
    schemeName: string;
    tokenSource: string;
    tokenExpiresAt: number;
  };
}
