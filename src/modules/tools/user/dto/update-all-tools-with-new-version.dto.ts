import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

/**
 * DTO cho việc cập nhật tất cả các tool có isUpdate=true với phiên bản mới nhất từ admin
 */
export class UpdateAllToolsWithNewVersionDto {
  /**
   * Cờ xác định có cập nhật tất cả các tool hay không
   */
  @ApiProperty({
    description: 'Cờ xác định có cập nhật tất cả các tool hay không',
    example: true,
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'Cờ cập nhật phải là boolean' })
  updateAll?: boolean;
}
