import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { ApiKeyAuthDto, AuthConfigDto, AuthTypeEnum, NoAuthDto, OAuthAuthDto } from './auth-config.dto';

/**
 * DTO cho việc cập nhật xác thực công cụ
 */
export class UpdateToolAuthDto {
  /**
   * ID của công cụ cần cập nhật
   */
  @ApiProperty({
    description: 'ID của công cụ cần cập nhật',
    example: '550e8400-e29b-41d4-a716-************'
  })
  @IsUUID()
  @IsNotEmpty()
  toolId: string;

  /**
   * ID của cấu hình xác thực cũ (nếu có)
   */
  @ApiProperty({
    description: 'ID của cấu hình xác thực cũ (nếu có)',
    example: '550e8400-e29b-41d4-a716-************',
    required: false
  })
  @IsUUID()
  @IsOptional()
  oldAuthId?: string;

  /**
   * Cấu hình xác thực mới
   */
  @ApiProperty({
    description: 'Cấu hình xác thực mới',
    oneOf: [
      { $ref: '#/components/schemas/ApiKeyAuthDto' },
      { $ref: '#/components/schemas/OAuthAuthDto' },
      { $ref: '#/components/schemas/NoAuthDto' }
    ],
    example: {
      authType: AuthTypeEnum.API_KEY,
      schemeName: 'ApiKeyAuth',
      apiKey: 'api_key_123456',
      apiKeyLocation: 'header',
      paramName: 'X-API-KEY'
    }
  })
  @ValidateNested()
  @Type(() => AuthConfigDto, {
    discriminator: {
      property: 'authType',
      subTypes: [
        { value: ApiKeyAuthDto, name: AuthTypeEnum.API_KEY },
        { value: OAuthAuthDto, name: AuthTypeEnum.OAUTH },
        { value: NoAuthDto, name: AuthTypeEnum.NONE }
      ]
    }
  })
  authConfig: ApiKeyAuthDto | OAuthAuthDto | NoAuthDto;
}
