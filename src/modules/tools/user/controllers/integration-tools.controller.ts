import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { QueryDto } from '@common/dto';
import { AppException } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CUSTOM_TOOLS_ERROR_CODES, TOOLS_ERROR_CODES } from '../../exceptions';
import { AuthTypeEnum } from '../dto/auth-config.dto';
import { DirectOpenApiInputDto, IntegrateFromOpenApiDto } from '../dto/integrate-from-openapi.dto';
import { ToolDetailResponseDto, ToolResponseDto } from '../dto/tool-response.dto';
import { UpdateBaseUrlDto } from '../dto/update-base-url.dto';
import { UpdateToolAuthDto } from '../dto/update-tool-auth.dto';
import { IntegrationToolsService } from '../services/integration-tools.service';
import { extraModels } from './integration-tools.extraModels';

/**
 * Controller quản lý tích hợp công cụ từ OpenAPI
 */
@ApiTags(SWAGGER_API_TAGS.USER_TOOL_INTEGRATION)
@Controller('user/tools/integration')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(...extraModels)
export class IntegrationToolsController {
  constructor(private readonly integrationToolsService: IntegrationToolsService) { }

  /**
   * Tích hợp công cụ từ đặc tả OpenAPI (hỗ trợ cả có và không có xác thực)
   * @param userId ID của người dùng
   * @param body
   * @returns Kết quả tích hợp
   */
  @Post('openapi')
  @ApiOperation({
    summary: 'Tích hợp công cụ từ đặc tả OpenAPI',
    description: `API này hỗ trợ nhiều định dạng đầu vào:

    1. Định dạng đầy đủ (khuyến nghị):
    \`\`\`json
    {
      "openapiSpec": {
        "openapi": "3.0.0",
        "paths": {...}
      },
      "baseUrl": "https://api.example.com",
      "authConfig": {
        "authType": "apiKey",
        "schemeName": "ApiKeyAuth",
        "apiKey": "api_key_123456",
        "apiKeyLocation": "header",
        "paramName": "X-API-KEY"
      }
    }
    \`\`\`

    2. Định dạng đóng gói không có xác thực:
    \`\`\`json
    {
      "openapiSpec": {
        "openapi": "3.0.0",
        "paths": {...}
      },
      "baseUrl": "https://api.example.com"
    }
    \`\`\`

    3. Định dạng đóng gói có xác thực (không chỉ định baseUrl):
    \`\`\`json
    {
      "openapiSpec": {
        "openapi": "3.0.0",
        "paths": {...}
      },
      "authConfig": {
        "authType": "apiKey",
        "schemeName": "ApiKeyAuth",
        "apiKey": "api_key_123456",
        "apiKeyLocation": "header",
        "paramName": "X-API-KEY"
      }
    }
    \`\`\`

    4. Định dạng OpenAPI trực tiếp:
    \`\`\`json
    {
      "openapi": "3.0.0",
      "paths": {...}
    }
    \`\`\`

    Hệ thống sẽ tự động áp dụng quy tắc ánh xạ sau:
    - Các route GET sẽ được ánh xạ thành RESOURCE
    - Các route POST, PUT, PATCH, DELETE sẽ được ánh xạ thành TOOL

    Trường "baseUrl" là tùy chọn và được sử dụng để chỉ định URL cơ sở cho API. Nếu không cung cấp, hệ thống sẽ:
    1. Sử dụng URL từ trường "servers" trong đặc tả OpenAPI (nếu có)
    2. Sử dụng giá trị mặc định "http://localhost:8080" (nếu không có thông tin servers)

    Trường "authConfig" là tùy chọn và có thể có một trong các định dạng sau:

    1. API Key:
    \`\`\`json
    "authConfig": {
      "authType": "apiKey",
      "schemeName": "ApiKeyAuth",
      "apiKey": "api_key_123456",
      "apiKeyLocation": "header",
      "paramName": "X-API-KEY"
    }
    \`\`\`

    2. OAuth:
    \`\`\`json
    "authConfig": {
      "authType": "oauth",
      "schemeName": "OAuthAuth",
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "tokenSource": "jwt"
    }
    \`\`\`

    3. Không xác thực:
    \`\`\`json
    "authConfig": {
      "authType": "none"
    }
    \`\`\`

    Kết quả trả về sẽ bao gồm:
    - toolsCreated: Số lượng công cụ đã tạo
    - resourcesCreated: Số lượng tài nguyên đã tạo
    - failedEndpoints: Danh sách các endpoint gặp lỗi trong quá trình xử lý
    - authConfig: Thông tin về cấu hình xác thực đã tạo (nếu có)

    Hệ thống sẽ cố gắng xử lý tất cả các endpoint trong đặc tả OpenAPI. Nếu một endpoint gặp lỗi,
    hệ thống sẽ bỏ qua endpoint đó và tiếp tục xử lý các endpoint khác. Thông tin về các endpoint
    gặp lỗi sẽ được trả về trong trường failedEndpoints.
    `
  })
  @ApiBody({
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/IntegrateFromOpenApiDto' },
        { $ref: '#/components/schemas/DirectOpenApiInputDto' }
      ]
    },
    examples: {
      'Định dạng đầy đủ với API Key': {
        summary: 'Định dạng đầy đủ với API Key',
        description: 'Đặc tả OpenAPI với cấu hình xác thực API Key',
        value: {
          openapiSpec: {
            openapi: '3.0.0',
            paths: {
              '/users': {
                get: { summary: 'Lấy danh sách người dùng' },
                post: { summary: 'Tạo người dùng mới' }
              }
            }
          },
          baseUrl: 'https://api.example.com',
          authConfig: {
            authType: 'apiKey',
            schemeName: 'ApiKeyAuth',
            apiKey: 'api_key_123456',
            apiKeyLocation: 'header',
            paramName: 'X-API-KEY'
          }
        }
      },
      'Định dạng đầy đủ với OAuth': {
        summary: 'Định dạng đầy đủ với OAuth',
        description: 'Đặc tả OpenAPI với cấu hình xác thực OAuth',
        value: {
          openapiSpec: {
            openapi: '3.0.0',
            paths: {
              '/users': {
                get: { summary: 'Lấy danh sách người dùng' },
                post: { summary: 'Tạo người dùng mới' }
              }
            }
          },
          baseUrl: 'https://api.example.com',
          authConfig: {
            authType: 'oauth',
            schemeName: 'OAuthAuth',
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            tokenSource: 'jwt'
          }
        }
      },
      'Định dạng đầy đủ không xác thực': {
        summary: 'Định dạng đầy đủ không xác thực',
        description: 'Đặc tả OpenAPI không có xác thực',
        value: {
          openapiSpec: {
            openapi: '3.0.0',
            paths: {
              '/users': {
                get: { summary: 'Lấy danh sách người dùng' },
                post: { summary: 'Tạo người dùng mới' }
              }
            }
          },
          baseUrl: 'https://api.example.com',
          authConfig: {
            authType: 'none'
          }
        }
      },
      'Định dạng OpenAPI trực tiếp': {
        summary: 'Định dạng OpenAPI trực tiếp',
        description: 'Đặc tả OpenAPI trực tiếp không qua wrapper',
        value: {
          openapi: '3.0.0',
          paths: {
            '/users': {
              get: {
                summary: 'Lấy danh sách người dùng',
                responses: {
                  '200': {
                    description: 'Thành công'
                  }
                }
              },
              post: {
                summary: 'Tạo người dùng mới',
                responses: {
                  '201': {
                    description: 'Tạo thành công'
                  }
                }
              }
            }
          },
          baseUrl: 'https://api.example.com'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Tích hợp thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            message: { type: 'string', example: 'Tích hợp công cụ từ OpenAPI thành công' },
            data: {
              type: 'object',
              properties: {
                toolsCreated: { type: 'number', example: 5 },
                resourcesCreated: { type: 'number', example: 3 },
                failedEndpoints: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      path: { type: 'string', example: '/users/{id}' },
                      method: { type: 'string', example: 'GET' },
                      reason: { type: 'string', example: 'Lỗi khi xử lý endpoint' }
                    }
                  }
                },
                authConfig: {
                  type: 'object',
                  properties: {
                    apiKeyCreated: { type: 'number', example: 1 },
                    oauthCreated: { type: 'number', example: 0 }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    TOOLS_ERROR_CODES.TOOL_CREATION_FAILED,
    TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS,
    CUSTOM_TOOLS_ERROR_CODES.API_KEY_CREATE_FAILED,
    CUSTOM_TOOLS_ERROR_CODES.OAUTH_CREATE_FAILED,
    CUSTOM_TOOLS_ERROR_CODES.OPENAPI_INVALID_FORMAT,
    CUSTOM_TOOLS_ERROR_CODES.OPENAPI_ENDPOINT_PROCESSING_FAILED,
    CUSTOM_TOOLS_ERROR_CODES.OPENAPI_SCHEMA_EXTRACTION_FAILED
  )
  async integrateFromOpenApi(
    @CurrentUser('id') userId: number,
    @Body() body: IntegrateFromOpenApiDto | DirectOpenApiInputDto,
  ) {
    let integrateDto: IntegrateFromOpenApiDto;
    let hasAuthConfig = false;

    try {
      // Kiểm tra xem body có phải là đặc tả OpenAPI trực tiếp không
      // Nếu body có thuộc tính 'openapi' và 'paths', coi như đó là đặc tả OpenAPI trực tiếp
      const isDirectOpenApi = 'openapi' in body && 'paths' in body;

      // Kiểm tra tính hợp lệ của đặc tả OpenAPI
      if (isDirectOpenApi) {
        // Kiểm tra đặc tả OpenAPI trực tiếp
        if (!body.paths || typeof body.paths !== 'object') {
          throw new AppException(
            CUSTOM_TOOLS_ERROR_CODES.OPENAPI_INVALID_FORMAT,
            'Đặc tả OpenAPI không hợp lệ: thuộc tính paths phải là một đối tượng'
          );
        }
      } else if ('openapiSpec' in body) {
        // Kiểm tra đặc tả OpenAPI trong trường openapiSpec
        if (!body.openapiSpec || typeof body.openapiSpec !== 'object' ||
            !body.openapiSpec.paths || typeof body.openapiSpec.paths !== 'object') {
          throw new AppException(
            CUSTOM_TOOLS_ERROR_CODES.OPENAPI_INVALID_FORMAT,
            'Đặc tả OpenAPI không hợp lệ: thuộc tính openapiSpec.paths phải là một đối tượng'
          );
        }
      } else {
        throw new AppException(
          CUSTOM_TOOLS_ERROR_CODES.OPENAPI_INVALID_FORMAT,
          'Đặc tả OpenAPI không hợp lệ: không tìm thấy thuộc tính openapi và paths'
        );
      }

      // Tạo DTO phù hợp
      integrateDto = isDirectOpenApi
        ? {
          openapiSpec: body as any,
          // Nếu có baseUrl trong body, sử dụng nó
          ...(body.baseUrl && { baseUrl: body.baseUrl })
        }
        : body as IntegrateFromOpenApiDto;

      // Kiểm tra xem có authConfig hay không
      hasAuthConfig = 'authConfig' in integrateDto && !!integrateDto.authConfig;

      // Thêm authType nếu cần
      if (hasAuthConfig && integrateDto.authConfig && !integrateDto.authConfig.authType) {
        // Xác định authType dựa trên các trường có trong authConfig
        const authConfig = integrateDto.authConfig as any;
        if (authConfig.apiKey && authConfig.apiKeyLocation) {
          integrateDto.authConfig.authType = AuthTypeEnum.API_KEY;
        } else if (authConfig.token && authConfig.tokenSource) {
          integrateDto.authConfig.authType = AuthTypeEnum.OAUTH;
        } else {
          integrateDto.authConfig.authType = AuthTypeEnum.NONE;
        }
      }

      // Gọi service phù hợp
      const result = await this.integrationToolsService.integrateFromOpenApi(userId, integrateDto);

      // Trả về kết quả phù hợp
      return ApiResponseDto.success({
        message: hasAuthConfig
          ? 'Tích hợp công cụ từ OpenAPI với xác thực thành công'
          : 'Tích hợp công cụ từ OpenAPI thành công',
        data: result,
      });
    } catch (error) {
      if (error instanceof SyntaxError) {
        // Xử lý lỗi phân tích cú pháp JSON
        throw new AppException(
          CUSTOM_TOOLS_ERROR_CODES.OPENAPI_INVALID_FORMAT,
          `Lỗi phân tích JSON: ${error.message}`
        );
      }
      throw error;
    }
  }

  /**
   * Lấy danh sách công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh
   */
  @Get('tools')
  @ApiOperation({
    summary: 'Lấy danh sách công cụ tùy chỉnh',
    description: 'API này trả về danh sách các công cụ tùy chỉnh đã tích hợp của người dùng.'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    schema: ApiResponseDto.getPaginatedSchema(ToolResponseDto),
  })
  @ApiErrorResponse(
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND
  )
  async getCustomTools(
    @CurrentUser('id') userId: number,
    @Query() query: QueryDto,
  ) {
    const result = await this.integrationToolsService.getCustomTools(userId, query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Chi tiết công cụ tùy chỉnh
   */
  @Get('tools/:id')
  @ApiOperation({
    summary: 'Lấy chi tiết công cụ tùy chỉnh',
    description: 'API này trả về thông tin chi tiết của một công cụ tùy chỉnh, bao gồm cả thông tin xác thực.'
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết thành công',
    schema: ApiResponseDto.getSchema(ToolDetailResponseDto),
  })
  @ApiErrorResponse(
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND
  )
  async getCustomToolDetail(
    @CurrentUser('id') userId: number,
    @Param('id') toolId: string,
  ) {
    const result = await this.integrationToolsService.getCustomToolDetail(userId, toolId);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật xác thực cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  @Patch('tools/auth')
  @ApiOperation({
    summary: 'Cập nhật xác thực cho công cụ tùy chỉnh',
    description: 'API này cho phép cập nhật cấu hình xác thực (API Key, OAuth, hoặc không xác thực) cho một công cụ tùy chỉnh.'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND,
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED,
    CUSTOM_TOOLS_ERROR_CODES.API_KEY_CREATE_FAILED,
    CUSTOM_TOOLS_ERROR_CODES.OAUTH_CREATE_FAILED
  )
  async updateCustomToolAuth(
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateToolAuthDto,
  ) {
    const result = await this.integrationToolsService.updateCustomToolAuth(userId, updateDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Kết quả xóa
   */
  @Delete('tools/:id')
  @ApiOperation({
    summary: 'Xóa công cụ tùy chỉnh',
    description: 'API này xóa một công cụ tùy chỉnh và các cấu hình xác thực liên quan.'
  })
  @ApiParam({ name: 'id', description: 'ID của công cụ', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Xóa thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND,
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_DELETE_FAILED
  )
  async deleteCustomTool(
    @CurrentUser('id') userId: number,
    @Param('id') toolId: string,
  ) {
    const result = await this.integrationToolsService.deleteCustomTool(userId, toolId);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật base URL cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật base URL
   * @returns Kết quả cập nhật
   */
  @Patch('tools/base-url')
  @ApiOperation({
    summary: 'Cập nhật base URL cho công cụ tùy chỉnh',
    description: 'API này cho phép cập nhật base URL cho một công cụ tùy chỉnh thay vì sử dụng giá trị mặc định http://localhost:8080.'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật thành công',
    schema: ApiResponseDto.getSchema(Object),
  })
  @ApiErrorResponse(
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND,
    CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED
  )
  async updateBaseUrl(
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateBaseUrlDto,
  ) {
    const result = await this.integrationToolsService.updateBaseUrl(userId, updateDto);
    return ApiResponseDto.success(result);
  }
}