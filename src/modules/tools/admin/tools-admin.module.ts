import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion
} from '../entities';
import { TypeAgent } from '../../agent/entities/type-agent.entity';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  TypeAgentRepository,
} from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { AdminToolService, AdminToolVersionService } from './services';
import { AdminToolController, AdminToolVersionController } from './controllers';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';

@Module({
  imports: [TypeOrmModule.forFeature([
    AdminTool,
    UserTool,
    AdminToolVersion,
    UserToolVersion,
    TypeAgent
  ])],
  controllers: [
    AdminToolVersionController,
    AdminToolController
  ],
  providers: [
    AdminToolService,
    AdminToolVersionService,
    AdminToolRepository,
    UserToolRepository,
    AdminToolVersionRepository,
    UserToolVersionRepository,
    TypeAgentRepository,
    EmployeeInfoRepository,
    S3Service
  ],
  exports: [
    AdminToolService,
    AdminToolVersionService
  ],
})
export class ToolsAdminModule {}
