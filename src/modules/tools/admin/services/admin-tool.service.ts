import {
  AccessType<PERSON>num,
  ToolStatusEnum,
} from '@/modules/tools/constants';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AdminTool, AdminToolVersion } from '../../entities';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  AdminToolRepository,
  AdminToolVersionRepository,
  UserToolRepository,
  UserToolVersionRepository,
} from '../../repositories';
import {
  CreateToolDto,
  EmployeeInfoDto,
  QueryToolDto,
  SimpleVersionDto,
  ToolDetailDto,
  ToolListItemDto,
  UpdateToolDto,
  VersionDto,
} from '../dto';

@Injectable()
export class AdminToolService {
  private readonly logger = new Logger(AdminToolService.name);

  constructor(
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
    private readonly employeeInfoRepository: EmployeeInfoRepository,
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
  ) { }

  /**
   * Tạo mới tool và phiên bản đầu tiên
   * @param employeeId ID của nhân viên tạo tool
   * @param createDto Dữ liệu tạo tool
   * @returns ID của tool đã tạo
   */
  @Transactional()
  async createTool(
    employeeId: number,
    createDto: CreateToolDto,
  ): Promise<string> {
    try {
      // Kiểm tra tồn tại tool name và function name trong một query tối ưu
      const validation = await this.adminToolRepository.validateToolAndFunctionName(
        createDto.name,
        createDto.toolName,
      );

      if (!validation.functionNameValid) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
          validation.functionNameReason,
        );
      }

      if (validation.toolNameExists) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
      }

      if (validation.functionNameExists) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
          `Tên hàm '${createDto.toolName}' đã được sử dụng`,
        );
      }

      // Tạo tool mới
      const newTool = new AdminTool();
      newTool.name = createDto.name;
      newTool.description = createDto.description || null;
      newTool.status = createDto.status || ToolStatusEnum.DRAFT;
      newTool.accessType = createDto.accessType || AccessTypeEnum.PUBLIC;
      newTool.createdBy = employeeId;
      newTool.updatedBy = employeeId;

      // Lưu tool
      const savedTool = await this.adminToolRepository.save(newTool);

      // Tạo phiên bản đầu tiên
      const newVersion = new AdminToolVersion();
      newVersion.toolId = savedTool.id;
      newVersion.versionName = createDto.versionName || 'v1.0.0'; // Sử dụng versionName từ DTO hoặc giá trị mặc định
      newVersion.toolName = createDto.toolName;
      newVersion.toolDescription = createDto.toolDescription || null;
      newVersion.parameters = createDto.parameters;
      newVersion.changeDescription = createDto.changeDescription || 'Phiên bản đầu tiên';
      newVersion.status = createDto.status || ToolStatusEnum.DRAFT;
      newVersion.isDefault = true; // Phiên bản đầu tiên luôn là mặc định
      newVersion.createdBy = employeeId;
      newVersion.updatesBy = employeeId;

      // Lưu phiên bản
      const savedVersion = await this.adminToolVersionRepository.save(newVersion);

      // Cập nhật versionDefault cho tool
      await this.adminToolRepository.updateDefaultVersion(savedTool.id, savedVersion.id);

      this.logger.log(`Created tool ${savedTool.id} with first version ${savedVersion.id} as default`);
      return savedTool.id;
    } catch (error) {
      this.logger.error(`Failed to create tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_CREATION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy danh sách tool với phân trang (Tối ưu - không có N+1 query)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getTools(
    queryDto: QueryToolDto,
  ): Promise<PaginatedResult<ToolListItemDto>> {
    try {
      const { page, limit, search, status, accessType, sortBy, sortDirection } =
        queryDto;

      // Sử dụng method tối ưu để lấy tools với latest versions trong 1 query
      const result = await this.adminToolRepository.findToolsWithLatestVersions(
        page,
        limit,
        search,
        status,
        accessType,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi sang DTO (đã có đầy đủ thông tin từ query tối ưu)
      const items = result.items.map((tool) => {
        const dto = new ToolListItemDto();
        dto.id = tool.id;
        dto.name = tool.name;
        dto.description = tool.description;
        dto.accessType = tool.accessType;
        dto.status = tool.status;
        dto.createdAt = tool.createdAt;
        dto.updatedAt = tool.updatedAt;

        // Thông tin version mặc định từ kết quả đã tối ưu
        dto.versionDefault = (tool as any).defaultVersion?.id || null;
        dto.versionName = (tool as any).defaultVersion?.versionName || null;

        return dto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to get tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết tool
   * @param id ID của tool
   * @returns Thông tin chi tiết tool
   */
  async getToolById(id: string): Promise<ToolDetailDto> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép xem chi tiết
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Lấy thông tin người tạo và người cập nhật
      const createdBy = await this.employeeInfoRepository.getEmployeeInfo(
        tool.createdBy,
      );
      const updatedBy = await this.employeeInfoRepository.getEmployeeInfo(
        tool.updatedBy,
      );

      // Lấy danh sách phiên bản
      const versions =
        await this.adminToolVersionRepository.findVersionsByToolId(id);

      // Lấy phiên bản mặc định (ưu tiên version có isDefault = true)
      let defaultVersion: AdminToolVersion | null = null;
      if (versions.length > 0) {
        // Tìm version có isDefault = true trước
        defaultVersion = versions.find(v => v.isDefault) || null;

        // Nếu không có version nào được đánh dấu default, lấy version mới nhất
        if (!defaultVersion) {
          defaultVersion = versions[0]; // Lấy phiên bản đầu tiên (mới nhất) vì đã sắp xếp theo createdAt DESC
        }
      }

      // Chuyển đổi phiên bản sang DTO đơn giản (chỉ ID và tên phiên bản)
      const simpleVersionDtos = versions.map(version => {
        const dto = new SimpleVersionDto();
        dto.id = version.id;
        dto.versionName = version.versionName;
        return dto;
      });

      // Chuyển đổi phiên bản mặc định sang DTO đầy đủ
      let defaultVersionDto: VersionDto | null = null;
      if (defaultVersion) {
        const defaultVersionCreatedBy =
          await this.employeeInfoRepository.getEmployeeInfo(
            defaultVersion.createdBy,
          );
        defaultVersionDto = this.mapVersionToDto(
          defaultVersion,
          defaultVersionCreatedBy,
        );
      }

      // Tạo DTO chi tiết
      const detailDto = new ToolDetailDto();
      detailDto.id = tool.id;
      detailDto.name = tool.name;
      detailDto.description = tool.description;
      detailDto.accessType = tool.accessType;
      detailDto.status = tool.status;
      detailDto.createdAt = tool.createdAt;
      detailDto.updatedAt = tool.updatedAt;
      detailDto.versionName = defaultVersion?.versionName || null;
      detailDto.createdBy = createdBy;
      detailDto.updatedBy = updatedBy;
      detailDto.defaultVersion = defaultVersionDto;
      detailDto.versions = simpleVersionDtos;

      return detailDto;
    } catch (error) {
      this.logger.error(
        `Failed to get tool by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Cập nhật thông tin tool
   * @param id ID của tool cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của tool đã cập nhật
   */
  @Transactional()
  async updateTool(
    id: string,
    employeeId: number,
    updateDto: UpdateToolDto,
  ): Promise<string> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép sửa
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tên hiển thị của tool đã tồn tại chưa (nếu có cập nhật tên)
      if (updateDto.name && updateDto.name !== tool.name) {
        // Sử dụng method tối ưu để kiểm tra tồn tại
        const validation = await this.adminToolRepository.validateToolAndFunctionName(
          updateDto.name,
          '', // Không cần kiểm tra function name khi update tool
          id, // Loại trừ tool hiện tại
        );

        if (validation.toolNameExists) {
          throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
        }
      }

      // Cập nhật thông tin tool
      if (updateDto.name) tool.name = updateDto.name;
      if (updateDto.description !== undefined)
        tool.description = updateDto.description;
      if (updateDto.status) tool.status = updateDto.status;
      if (updateDto.accessType) tool.accessType = updateDto.accessType;

      tool.updatedBy = employeeId;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.adminToolRepository.save(tool);

      // Cập nhật trạng thái "có update" cho tất cả các user tools đã clone từ tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(id);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Đánh dấu ${userTools.length} user tools có bản cập nhật mới từ admin tool ${id}`);

          for (const userTool of userTools) {
            userTool.hasUpdate = true;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);
          }
        }
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc cập nhật admin tool
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return tool.id;
    } catch (error) {
      this.logger.error(`Failed to update tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Xóa tool
   * @param id ID của tool cần xóa
   * @param employeeId ID của nhân viên xóa
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async deleteTool(id: string, employeeId: number): Promise<boolean> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đang ở trạng thái isForSale = true thì không cho phép xóa
      if (tool.isForSale) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_DELETE_FAILED,
          'Không thể xóa tool đang được bán trên marketplace. Vui lòng gỡ tool khỏi marketplace trước khi xóa.'
        );
      }



      // Soft delete tool
      await this.adminToolRepository.softDeleteTool(id, employeeId);

      // Vô hiệu hóa tất cả các user tools đã clone từ admin tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(id);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Vô hiệu hóa ${userTools.length} user tools đã clone từ admin tool ${id}`);

          for (const userTool of userTools) {
            // Đánh dấu user tool là DEPRECATED
            userTool.status = ToolStatusEnum.DEPRECATED;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);



            // Xóa tất cả các phiên bản của user tool
            try {
              await this.userToolVersionRepository.createQueryBuilder()
                .delete()
                .from('user_tool_versions')
                .where('original_function_id = :toolId', { toolId: userTool.id })
                .execute();
              this.logger.log(`Đã xóa tất cả các phiên bản của user tool ${userTool.id}`);
            } catch (versionError) {
              this.logger.error(`Lỗi khi xóa các phiên bản của user tool ${userTool.id}: ${versionError.message}`, versionError.stack);
            }
          }
        }
      } catch (userToolError) {
        this.logger.error(`Lỗi khi vô hiệu hóa user tools: ${userToolError.message}`, userToolError.stack);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_DELETE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy danh sách tools đã bị xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tools đã bị xóa
   */
  async getDeletedTools(
    queryDto: QueryToolDto,
  ): Promise<PaginatedResult<ToolListItemDto>> {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      const result = await this.adminToolRepository.findDeletedTools(
        page,
        limit,
        search,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi sang DTO
      const items = result.items.map((tool) => {
        const dto = new ToolListItemDto();
        dto.id = tool.id;
        dto.name = tool.name;
        dto.description = tool.description;
        dto.accessType = tool.accessType;
        dto.status = tool.status;
        dto.createdAt = tool.createdAt;
        dto.updatedAt = tool.updatedAt;
        dto.versionDefault = null;
        dto.versionName = null;

        // Thêm thông tin người xóa
        if ((tool as any).deletedByInfo) {
          dto.deletedAt = tool.deletedAt;
          dto.deletedBy = (tool as any).deletedByInfo;
        }

        return dto;
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to get deleted tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Rollback tool đã bị xóa mềm
   * @param id ID của tool cần rollback
   * @returns true nếu rollback thành công
   */
  @Transactional()
  async rollbackTool(id: string): Promise<boolean> {
    try {
      // Kiểm tra tool có tồn tại và đã bị xóa không
      const tool = await this.adminToolRepository.findOne({
        where: { id },
        withDeleted: true, // Lấy cả tools đã bị xóa
      });

      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      if (!tool.deletedAt) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này chưa bị xóa, không thể rollback.'
        );
      }

      // Rollback tool
      await this.adminToolRepository.rollbackTool(id);

      // Rollback tất cả user tools đã clone từ admin tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(id);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Rollback ${userTools.length} user tools từ admin tool ${id}`);

          for (const userTool of userTools) {
            // Khôi phục user tool về trạng thái DRAFT
            userTool.status = ToolStatusEnum.DRAFT;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);
          }
        }
      } catch (userToolError) {
        this.logger.error(`Lỗi khi rollback user tools: ${userToolError.message}`, userToolError.stack);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to rollback tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @param createdBy Thông tin người tạo
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(
    version: AdminToolVersion,
    createdBy: EmployeeInfoDto,
  ): VersionDto {
    const versionDto = new VersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName;
    versionDto.toolName = version.toolName;
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.changeDescription = version.changeDescription;
    versionDto.status = version.status;
    versionDto.createdAt = version.createdAt;
    versionDto.createdBy = createdBy;
    return versionDto;
  }
}
