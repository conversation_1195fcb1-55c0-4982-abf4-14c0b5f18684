import { Injectable, Logger } from '@nestjs/common';
import { AdminToolRepository, UserToolRepository } from '../repositories';

/**
 * Helper xử lý việc xác thực các tool
 */
@Injectable()
export class ToolFunctionValidationHelper {
  private readonly logger = new Logger(ToolFunctionValidationHelper.name);

  constructor(
    private readonly adminToolRepository: AdminToolRepository,
    private readonly userToolRepository: UserToolRepository
  ) {}

  /**
   * <PERSON><PERSON><PERSON> thực danh sách ID tool của admin
   * @param toolIds Danh sách ID tool cần xác thực
   * @returns Kết quả xác thực
   */
  async validateAdminToolIds(
    toolIds: string[]
  ): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, tr<PERSON> về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Sử dụng repository để kiểm tra tính tồn tại của các ID
      const result = await this.adminToolRepository.validateToolIds(toolIds);

      return result;
    } catch (error) {
      this.logger.error(`Error validating admin tool IDs: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }

  /**
   * Xác thực danh sách ID tool của người dùng
   * @param toolIds Danh sách ID tool cần xác thực
   * @param userId ID của người dùng
   * @returns Kết quả xác thực
   */
  async validateUserToolIds(
    toolIds: string[],
    userId?: number
  ): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, trả về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Sử dụng repository để kiểm tra tính tồn tại của các ID
      const result = await this.userToolRepository.validateToolIds(toolIds, userId);

      return result;
    } catch (error) {
      this.logger.error(`Error validating user tool IDs: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }
}
