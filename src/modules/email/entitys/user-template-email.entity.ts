import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity({ name: 'user_template_email' })
export class UserTemplateEmailEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', type: 'integer' }) // References users.id
  userId: number;

  @Column({ type: 'varchar', length: 100, unique: true })
  name: string; // User-defined name for the template

  @Column({ type: 'varchar', length: 255 })
  subject: string;

  @Column({ type: 'text' })
  body_html: string;

  @Column({ type: 'text', nullable: true })
  body_text: string;

  @Column({ name: 'is_shared', type: 'boolean', default: false }) // Can this template be shared?
  isShared: boolean;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
} 