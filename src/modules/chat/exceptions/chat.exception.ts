import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module chat
 */
export const CHAT_ERROR_CODES = {
  AGENT_NOT_FOUND: new ErrorCode(
    10401,
    'Không tìm thấy agent',
    HttpStatus.NOT_FOUND,
  ),
  RUN_CREATION_FAILED: new ErrorCode(
    10402,
    'Tạo run chat thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_AGENT_TYPE: new ErrorCode(
    10403,
    'Loại agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  AGENT_ACCESS_DENIED: new ErrorCode(
    10404,
    'Không có quyền truy cập agent',
    HttpStatus.FORBIDDEN,
  ),
};
