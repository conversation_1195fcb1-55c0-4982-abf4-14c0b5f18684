import { Column, Entity, PrimaryColumn } from 'typeorm';
import { DiscountType } from '@modules/r-point/enums';
import { CouponStatus } from '@modules/r-point/enums';

/**
 * Entity đại diện cho bảng coupons trong cơ sở dữ liệu
 * Bảng quản lý thông tin mã giảm giá (coupons) trong hệ thống
 */
@Entity('coupons')
export class Coupon {
  /**
   * ID của coupon (UUID)
   */
  @PrimaryColumn({ name: 'id', type: 'uuid', comment: 'Định danh mã giảm giá (UUID)' })
  id: string;

  /**
   * Mã giảm giá
   */
  @Column({ name: 'code', length: 50, nullable: false, unique: true, comment: 'Mã giảm giá do hệ thống hoặc admin tạo' })
  code: string;

  /**
   * Mô tả chi tiết về mã giảm giá
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: '<PERSON><PERSON> tả chi tiết về mã giảm giá' })
  description: string;

  /**
   * Loại giảm giá: phần trăm hoặc số tiền cố định
   */
  @Column({ name: 'discount_type', type: 'enum', enum: DiscountType, nullable: false, comment: 'Loại giảm giá: phần trăm hoặc số tiền cố định' })
  discountType: DiscountType;

  /**
   * Giá trị giảm giá tương ứng với loại (%, số tiền)
   */
  @Column({ name: 'discount_value', type: 'double precision', nullable: false, comment: 'Giá trị giảm giá tương ứng với loại (%, số tiền)' })
  discountValue: number;

  /**
   * Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá
   */
  @Column({ name: 'min_order_value', type: 'double precision', default: 0, comment: 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá' })
  minOrderValue: number;

  /**
   * Giảm giá tối đa cho mã giảm giá loại phần trăm
   */
  @Column({ name: 'max_discount_amount', type: 'double precision', nullable: true, comment: 'Giảm giá tối đa cho mã giảm giá loại phần trăm' })
  maxDiscountAmount: number | null;

  /**
   * Thời điểm bắt đầu áp dụng mã
   */
  @Column({ name: 'start_date', type: 'bigint', nullable: false, comment: 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)' })
  startDate: number;

  /**
   * Thời điểm kết thúc áp dụng mã
   */
  @Column({ name: 'end_date', type: 'bigint', nullable: false, comment: 'Thời điểm kết thúc áp dụng mã (Unix timestamp)' })
  endDate: number;

  /**
   * Tổng số lần sử dụng tối đa cho toàn bộ hệ thống
   */
  @Column({ name: 'usage_limit', nullable: true, comment: 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống' })
  usageLimit: number;

  /**
   * Số lần một người dùng được sử dụng mã này
   */
  @Column({ name: 'per_user_limit', default: 1, comment: 'Số lần một người dùng được sử dụng mã này' })
  perUserLimit: number;

  /**
   * Trạng thái mã giảm giá
   */
  @Column({ name: 'status', type: 'enum', enum: CouponStatus, default: CouponStatus.ACTIVE, comment: 'Trạng thái mã giảm giá: ACTIVE, INACTIVE, hoặc EXPIRED' })
  status: CouponStatus;

  /**
   * Thời gian tạo mã
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo mã (Unix timestamp)' })
  createdAt: number;

  /**
   * Thời gian cập nhật mã gần nhất
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false, comment: 'Thời gian cập nhật mã gần nhất (Unix timestamp)' })
  updatedAt: number;
}
