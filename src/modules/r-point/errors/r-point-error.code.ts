import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi liên quan đến module R-Point (12000-12099)
 */
export const RPOINT_ERROR_CODES = {
  /**
   * Lỗi khi không tìm thấy gói point
   */
  POINT_PACKAGE_NOT_FOUND: new ErrorCode(
    12000,
    'Không tìm thấy gói point',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi không tìm thấy mã giảm giá
   */
  COUPON_NOT_FOUND: new ErrorCode(
    12001,
    'Không tìm thấy mã giảm giá',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi mã giảm giá không hợp lệ
   */
  COUPON_INVALID: new ErrorCode(
    12002,
    'Mã giảm giá không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi mã giảm giá không hoạt động
   */
  COUPON_INACTIVE: new ErrorCode(
    12003,
    'Mã giảm giá không hoạt động',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi mã giảm giá chưa đến thời gian sử dụng
   */
  COUPON_NOT_STARTED: new ErrorCode(
    12004,
    'Mã giảm giá chưa đến thời gian sử dụng',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi mã giảm giá đã hết hạn
   */
  COUPON_EXPIRED: new ErrorCode(
    12005,
    'Mã giảm giá đã hết hạn',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi giá trị đơn hàng không đủ để sử dụng mã giảm giá
   */
  ORDER_VALUE_TOO_LOW: new ErrorCode(
    12006,
    'Giá trị đơn hàng không đủ để sử dụng mã giảm giá',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi không tìm thấy giao dịch
   */
  TRANSACTION_NOT_FOUND: new ErrorCode(
    12007,
    'Không tìm thấy giao dịch',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo giao dịch thất bại
   */
  TRANSACTION_CREATION_FAILED: new ErrorCode(
    12008,
    'Tạo giao dịch thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật giao dịch thất bại
   */
  TRANSACTION_UPDATE_FAILED: new ErrorCode(
    12009,
    'Cập nhật giao dịch thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi không tìm thấy hóa đơn
   */
  INVOICE_NOT_FOUND: new ErrorCode(
    12010,
    'Không tìm thấy hóa đơn',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Lỗi khi tạo hóa đơn thất bại
   */
  INVOICE_CREATION_FAILED: new ErrorCode(
    12011,
    'Tạo hóa đơn thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật hóa đơn thất bại
   */
  INVOICE_UPDATE_FAILED: new ErrorCode(
    12012,
    'Cập nhật hóa đơn thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi số lượng point không hợp lệ
   */
  INVALID_POINT_AMOUNT: new ErrorCode(
    12013,
    'Số lượng point không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi số dư point không đủ
   */
  INSUFFICIENT_POINT_BALANCE: new ErrorCode(
    12014,
    'Số dư point không đủ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi thanh toán thất bại
   */
  PAYMENT_FAILED: new ErrorCode(
    12015,
    'Thanh toán thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi tạo QR code thanh toán thất bại
   */
  QR_CODE_GENERATION_FAILED: new ErrorCode(
    12016,
    'Tạo QR code thanh toán thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xác thực webhook thất bại
   */
  WEBHOOK_VERIFICATION_FAILED: new ErrorCode(
    12017,
    'Xác thực webhook thất bại',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * Lỗi khi mã giảm giá đã tồn tại
   */
  COUPON_ALREADY_EXISTS: new ErrorCode(
    12018,
    'Mã giảm giá đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi khoảng thời gian không hợp lệ
   */
  INVALID_DATE_RANGE: new ErrorCode(
    12019,
    'Khoảng thời gian không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Lỗi khi tạo mã giảm giá thất bại
   */
  COUPON_CREATION_FAILED: new ErrorCode(
    12020,
    'Tạo mã giảm giá thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi cập nhật mã giảm giá thất bại
   */
  COUPON_UPDATE_FAILED: new ErrorCode(
    12021,
    'Cập nhật mã giảm giá thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Lỗi khi xóa mã giảm giá thất bại
   */
  COUPON_DELETION_FAILED: new ErrorCode(
    12022,
    'Xóa mã giảm giá thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
