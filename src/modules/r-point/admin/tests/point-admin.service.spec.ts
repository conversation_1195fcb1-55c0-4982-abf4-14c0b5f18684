import { Test, TestingModule } from '@nestjs/testing';
import { PointAdminService } from '../services/point-admin.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { CreatePointDto } from '../dto/create-point.dto';
import { UpdatePointDto } from '../dto/update-point.dto';

// Mock the PointRepository and Point entity
class PointRepository {
  create = jest.fn();
  save = jest.fn();
  findOne = jest.fn();
  findAndCount = jest.fn();
  delete = jest.fn();
}

class Point {
  id: number;
  name: string;
  cash: number;
  rate: number;
  point: number;
  min?: number;
  max?: number;
  isCustomize: boolean;
  description?: string;
}

describe('PointAdminService', () => {
  let service: PointAdminService;
  let pointRepository: Partial<PointRepository>;

  beforeEach(async () => {
    // Mock repository
    pointRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      findAndCount: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PointAdminService,
        {
          provide: PointRepository,
          useValue: pointRepository,
        },
      ],
    }).compile();

    service = module.get<PointAdminService>(PointAdminService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a regular point package successfully', async () => {
      // Arrange
      const createPointDto: CreatePointDto = {
        name: 'Gói 100k',
        cash: 100000,
        description: 'Gói point cơ bản',
      };

      const mockPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
        description: 'Gói point cơ bản',
      };

      (pointRepository.create as jest.Mock).mockReturnValue(mockPoint);
      (pointRepository.save as jest.Mock).mockResolvedValue(mockPoint);

      // Act
      const result = await service.create(createPointDto);

      // Assert
      expect(pointRepository.create).toHaveBeenCalledWith({
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        description: 'Gói point cơ bản',
        isCustomize: false,
      });
      expect(pointRepository.save).toHaveBeenCalledWith(mockPoint);
      expect(result).toEqual(mockPoint);
    });

    it('should use default rate if not provided', async () => {
      // Arrange
      const createPointDto: CreatePointDto = {
        name: 'Gói 100k',
        cash: 100000,
        description: 'Gói point cơ bản',
      };

      const mockPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
        description: 'Gói point cơ bản',
      };

      (pointRepository.create as jest.Mock).mockReturnValue(mockPoint);
      (pointRepository.save as jest.Mock).mockResolvedValue(mockPoint);

      // Act
      const result = await service.create(createPointDto);

      // Assert
      expect(pointRepository.create).toHaveBeenCalledWith(expect.objectContaining({
        rate: 1000,
      }));
      expect(result.point).toBe(100); // 100000 / 1000 = 100
    });

    it('should use provided rate if available', async () => {
      // Arrange
      const createPointDto: CreatePointDto = {
        name: 'Gói 100k',
        cash: 100000,
        rate: 500,
        description: 'Gói point cơ bản',
      };

      const mockPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 500,
        point: 200,
        isCustomize: false,
        description: 'Gói point cơ bản',
      };

      (pointRepository.create as jest.Mock).mockReturnValue(mockPoint);
      (pointRepository.save as jest.Mock).mockResolvedValue(mockPoint);

      // Act
      const result = await service.create(createPointDto);

      // Assert
      expect(pointRepository.create).toHaveBeenCalledWith(expect.objectContaining({
        rate: 500,
      }));
      expect(result.point).toBe(200); // 100000 / 500 = 200
    });
  });

  describe('findAll', () => {
    it('should return paginated results', async () => {
      // Arrange
      const mockPoints = [
        {
          id: 1,
          name: 'Gói 100k',
          cash: 100000,
          rate: 1000,
          point: 100,
          isCustomize: false,
        },
        {
          id: 2,
          name: 'Gói 200k',
          cash: 200000,
          rate: 1000,
          point: 200,
          isCustomize: false,
        },
      ];

      (pointRepository.findAndCount as jest.Mock).mockResolvedValue([mockPoints, 2]);

      // Act
      const result = await service.findAll({ page: 1, limit: 10 });

      // Assert
      expect(pointRepository.findAndCount).toHaveBeenCalledWith({
        where: {},
        order: { id: 'ASC' },
        skip: 0,
        take: 10,
      });
      expect(result.items).toEqual(mockPoints);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
    });

    it('should apply search filter', async () => {
      // Arrange
      const mockPoints = [
        {
          id: 1,
          name: 'Gói 100k',
          cash: 100000,
          rate: 1000,
          point: 100,
          isCustomize: false,
        },
      ];

      (pointRepository.findAndCount as jest.Mock).mockResolvedValue([mockPoints, 1]);

      // Act
      const result = await service.findAll({ search: 'Gói 100k' });

      // Assert
      expect(pointRepository.findAndCount).toHaveBeenCalledWith(expect.objectContaining({
        where: expect.anything(),
      }));
      expect(result.items).toEqual(mockPoints);
      expect(result.total).toBe(1);
    });
  });

  describe('findOne', () => {
    it('should return a point package by id', async () => {
      // Arrange
      const mockPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(mockPoint);

      // Act
      const result = await service.findOne(1);

      // Assert
      expect(pointRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toEqual(mockPoint);
    });

    it('should throw NotFoundException if point package not found', async () => {
      // Arrange
      (pointRepository.findOne as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a regular point package', async () => {
      // Arrange
      const existingPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
        description: 'Gói point cơ bản',
      };

      const updatePointDto: UpdatePointDto = {
        name: 'Gói 100k (Khuyến mãi)',
        cash: 100000,
        rate: 900,
        description: 'Gói point khuyến mãi',
      };

      const updatedPoint = {
        ...existingPoint,
        name: 'Gói 100k (Khuyến mãi)',
        rate: 900,
        point: 111, // 100000 / 900 = 111.11, floor to 111
        description: 'Gói point khuyến mãi',
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(existingPoint);
      (pointRepository.save as jest.Mock).mockResolvedValue(updatedPoint);

      // Act
      const result = await service.update(1, updatePointDto);

      // Assert
      expect(pointRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(pointRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        id: 1,
        name: 'Gói 100k (Khuyến mãi)',
        rate: 900,
        point: expect.any(Number),
      }));
      expect(result).toEqual(updatedPoint);
    });

    it('should update to customize package with valid min, max, and rate', async () => {
      // Arrange
      const existingPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
        description: 'Gói point cơ bản',
      };

      const updatePointDto: UpdatePointDto = {
        name: 'Gói tùy chỉnh',
        isCustomize: true,
        min: 50000,
        max: 500000,
        rate: 1000,
        description: 'Gói point tùy chỉnh',
      };

      const updatedPoint = {
        ...existingPoint,
        name: 'Gói tùy chỉnh',
        isCustomize: true,
        min: 50000,
        max: 500000,
        rate: 1000,
        point: 100, // Default calculation for customize package
        description: 'Gói point tùy chỉnh',
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(existingPoint);
      (pointRepository.save as jest.Mock).mockResolvedValue(updatedPoint);

      // Act
      const result = await service.update(1, updatePointDto);

      // Assert
      expect(pointRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(pointRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        isCustomize: true,
        min: 50000,
        max: 500000,
      }));
      expect(result).toEqual(updatedPoint);
    });

    it('should throw BadRequestException if min >= max for customize package', async () => {
      // Arrange
      const existingPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
      };

      const updatePointDto: UpdatePointDto = {
        isCustomize: true,
        min: 500000,
        max: 50000, // min > max, should throw error
        rate: 1000,
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(existingPoint);

      // Act & Assert
      await expect(service.update(1, updatePointDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if min < 20000 for customize package', async () => {
      // Arrange
      const existingPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
      };

      const updatePointDto: UpdatePointDto = {
        isCustomize: true,
        min: 10000, // < 20000, should throw error
        max: 500000,
        rate: 1000,
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(existingPoint);

      // Act & Assert
      await expect(service.update(1, updatePointDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if max > 500000 for customize package', async () => {
      // Arrange
      const existingPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
      };

      const updatePointDto: UpdatePointDto = {
        isCustomize: true,
        min: 50000,
        max: 600000, // > 500000, should throw error
        rate: 1000,
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(existingPoint);

      // Act & Assert
      await expect(service.update(1, updatePointDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('remove', () => {
    it('should delete a point package', async () => {
      // Arrange
      const mockPoint = {
        id: 1,
        name: 'Gói 100k',
        cash: 100000,
        rate: 1000,
        point: 100,
        isCustomize: false,
      };

      (pointRepository.findOne as jest.Mock).mockResolvedValue(mockPoint);
      (pointRepository.delete as jest.Mock).mockResolvedValue({ affected: 1 });

      // Act
      const result = await service.remove(1);

      // Assert
      expect(pointRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(pointRepository.delete).toHaveBeenCalledWith(1);
      expect(result).toEqual({ message: 'Đã xóa gói point với ID 1' });
    });

    it('should throw NotFoundException if point package not found', async () => {
      // Arrange
      (pointRepository.findOne as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(999)).rejects.toThrow(NotFoundException);
    });
  });
});
