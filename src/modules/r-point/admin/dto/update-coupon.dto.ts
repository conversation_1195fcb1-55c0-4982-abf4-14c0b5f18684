import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsPositive, IsString, MaxLength, Min } from 'class-validator';
import { DiscountType, CouponStatus } from '@modules/r-point/enums';

/**
 * DTO cho việc cập nhật coupon
 */
export class UpdateCouponDto {
  @ApiProperty({
    description: 'Mô tả chi tiết về mã giảm giá',
    example: 'Giảm giá 15% cho tất cả các gói point trong mùa hè 2023',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại giảm giá: phần trăm hoặc số tiền cố định',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
    required: false,
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> trị giảm giá tương ứng với loại (%, số tiền)',
    example: 15, // 15% hoặc 15,000 VND
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  discountValue?: number;

  @ApiProperty({
    description: 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá',
    example: 150000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minOrderValue?: number;

  @ApiProperty({
    description: 'Giảm giá tối đa cho mã giảm giá loại phần trăm',
    example: 75000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  maxDiscountAmount?: number;

  @ApiProperty({
    description: 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)',
    example: 1625097600000, // 2021-07-01
    required: false,
  })
  @IsOptional()
  @IsNumber()
  startDate?: number;

  @ApiProperty({
    description: 'Thời điểm kết thúc áp dụng mã (Unix timestamp)',
    example: 1630368000000, // 2021-08-31
    required: false,
  })
  @IsOptional()
  @IsNumber()
  endDate?: number;

  @ApiProperty({
    description: 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống',
    example: 200,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  usageLimit?: number;

  @ApiProperty({
    description: 'Số lần một người dùng được sử dụng mã này',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  perUserLimit?: number;

  @ApiProperty({
    description: 'Trạng thái mã giảm giá',
    enum: CouponStatus,
    example: CouponStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(CouponStatus)
  status?: CouponStatus;
}
