import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response trả về thông tin gói point
 */
export class PointResponseDto {
  /**
   * ID của gói point
   */
  @ApiProperty({
    description: 'ID của gói point',
    example: 1,
  })
  id: number;

  /**
   * Tên của gói point
   */
  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k',
  })
  name: string;

  /**
   * Số tiền của gói point (VND)
   */
  @ApiProperty({
    description: 'Số tiền của gói point (VND)',
    example: 100000,
  })
  cash: number;

  /**
   * Tỷ lệ quy đổi giữa tiền và point
   */
  @ApiProperty({
    description: 'Tỷ lệ quy đổi giữa tiền và point',
    example: 1000,
  })
  rate: number;

  /**
   * Số tiền tối thiểu cho gói customize
   */
  @ApiProperty({
    description: 'Số tiền tối thiểu cho gói customize (VND)',
    example: 50000,
    nullable: true,
  })
  min: number | null;

  /**
   * Số tiền tối đa cho gói customize
   */
  @ApiProperty({
    description: 'Số tiền tối đa cho gói customize (VND)',
    example: 1000000,
    nullable: true,
  })
  max: number | null;

  /**
   * Số point tương ứng với gói
   */
  @ApiProperty({
    description: 'Số point tương ứng với gói',
    example: 100,
  })
  point: number;

  /**
   * Flag xác định đây có phải là gói customize hay không
   */
  @ApiProperty({
    description: 'Có phải là gói customize hay không',
    example: false,
  })
  isCustomize: boolean;

  /**
   * Mô tả chi tiết về gói point
   */
  @ApiProperty({
    description: 'Mô tả chi tiết về gói point',
    example: 'Gói point cơ bản với tỷ lệ quy đổi 1:1000',
    nullable: true,
  })
  description: string | null;
}
