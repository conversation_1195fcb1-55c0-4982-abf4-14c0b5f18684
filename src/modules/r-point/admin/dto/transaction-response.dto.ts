import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho thông tin point trong response
 */
export class TransactionPointDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên của gói point',
    example: 'Gói 100k'
  })
  name: string;

  @ApiProperty({
    description: 'Số tiền của gói point',
    example: 100000
  })
  cash: number;

  @ApiProperty({
    description: 'Tỷ lệ quy đổi',
    example: 1000
  })
  rate: number;
}

/**
 * DTO cho thông tin user trong response
 */
export class TransactionUserDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên của người dùng',
    example: 'Nguyễn <PERSON>'
  })
  fullName: string;

  @ApiProperty({
    description: '<PERSON><PERSON> của người dùng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0987654321'
  })
  phone: string;
}

/**
 * DTO cho response trả về thông tin giao dịch
 */
export class TransactionResponseDto {
  @ApiProperty({
    description: 'ID của giao dịch',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  userId: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: TransactionUserDto,
    nullable: true
  })
  user: TransactionUserDto | null;

  @ApiProperty({
    description: 'Số tiền giao dịch',
    example: 100000
  })
  amount: number;

  @ApiProperty({
    description: 'Số lượng point mua',
    example: 100
  })
  pointsAmount: number;

  @ApiProperty({
    description: 'ID của gói point',
    example: 1
  })
  pointId: number;

  @ApiProperty({
    description: 'Thông tin gói point',
    type: TransactionPointDto,
    nullable: true
  })
  point: TransactionPointDto | null;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    example: TransactionStatus.CONFIRMED
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    example: 'VNPAY'
  })
  paymentMethod: string;

  @ApiProperty({
    description: 'Mã tham chiếu từ cổng thanh toán',
    example: 'VNP123456'
  })
  referenceId: string;

  @ApiProperty({
    description: 'Số dư trước giao dịch',
    example: 50
  })
  balanceBefore: number;

  @ApiProperty({
    description: 'Số dư sau giao dịch',
    example: 150
  })
  balanceAfter: number;

  @ApiProperty({
    description: 'ID của coupon sử dụng',
    example: 1
  })
  couponId: number;

  @ApiProperty({
    description: 'Số tiền giảm giá từ coupon',
    example: 10000
  })
  couponAmount: number;

  @ApiProperty({
    description: 'Thời gian tạo giao dịch',
    example: 1625097600000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian hoàn thành giao dịch',
    example: 1625097660000
  })
  completedAt: number;
}

/**
 * DTO cho response trả về danh sách giao dịch có phân trang
 */
export class TransactionPaginatedResponseDto {
  @ApiProperty({
    description: 'Danh sách giao dịch',
    type: [TransactionResponseDto]
  })
  items: TransactionResponseDto[];

  @ApiProperty({
    description: 'Tổng số giao dịch',
    example: 100
  })
  total: number;

  @ApiProperty({
    description: 'Số trang hiện tại',
    example: 1
  })
  page: number;

  @ApiProperty({
    description: 'Số lượng item trên mỗi trang',
    example: 10
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 10
  })
  totalPages: number;
}
