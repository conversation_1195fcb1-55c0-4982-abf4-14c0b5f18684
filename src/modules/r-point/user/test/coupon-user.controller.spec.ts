import { Test, TestingModule } from '@nestjs/testing';
import { CouponUserController } from '../controllers/coupon-user.controller';
import { CouponUserService } from '../services';
import { CouponStatus, DiscountType } from '@modules/r-point/enums';

describe('CouponUserController', () => {
  let controller: CouponUserController;
  let service: jest.Mocked<CouponUserService>;

  const mockCouponUserService = {
    getRandomCoupons: jest.fn(),
    validateCoupon: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CouponUserController],
      providers: [
        { provide: CouponUserService, useValue: mockCouponUserService },
      ],
    }).compile();

    controller = module.get<CouponUserController>(CouponUserController);
    service = module.get(CouponUserService) as jest.Mocked<CouponUserService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getRandomCoupons', () => {
    it('should return random coupons', async () => {
      // Mock data
      const mockCoupons = [
        {
          id: '1',
          code: 'COUPON1',
          description: 'Coupon 1',
          discountType: DiscountType.PERCENTAGE,
          discountValue: 10,
          minOrderValue: 50000,
          maxDiscountAmount: 10000,
          startDate: Date.now() - 86400000, // Yesterday
          endDate: Date.now() + 86400000, // Tomorrow
          status: CouponStatus.ACTIVE,
        },
        {
          id: '2',
          code: 'COUPON2',
          description: 'Coupon 2',
          discountType: DiscountType.FIXED_AMOUNT,
          discountValue: 20000,
          minOrderValue: 50000,
          startDate: Date.now() - 86400000, // Yesterday
          endDate: Date.now() + 86400000, // Tomorrow
          status: CouponStatus.ACTIVE,
        },
      ];

      // Mock service response
      service.getRandomCoupons.mockResolvedValue(mockCoupons);

      // Mock user from JWT
      const mockUser = { id: 1 };

      // Call the controller method
      const result = await controller.getRandomCoupons({
        pointId: 1,
        pointAmount: 100,
      }, mockUser as any);

      // Assertions
      expect(service.getRandomCoupons).toHaveBeenCalledWith({
        pointId: 1,
        pointAmount: 100,
      }, 1);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockCoupons);
    });
  });

  describe('validateCoupon', () => {
    it('should validate coupon and return discount info', async () => {
      // Mock data
      const mockCoupon = {
        id: '1',
        code: 'COUPON1',
        description: 'Coupon 1',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        minOrderValue: 50000,
        maxDiscountAmount: 10000,
        startDate: Date.now() - 86400000, // Yesterday
        endDate: Date.now() + 86400000, // Tomorrow
        status: CouponStatus.ACTIVE,
      };

      const mockValidationResult = {
        coupon: mockCoupon,
        originalPrice: 100000,
        discountAmount: 10000,
        finalPrice: 90000,
        isValid: true,
      };

      // Mock service response
      service.validateCoupon.mockResolvedValue(mockValidationResult);

      // Mock user from JWT
      const mockUser = { id: 1 };

      // Call the controller method
      const result = await controller.validateCoupon({
        couponCode: 'COUPON1',
        pointId: 1,
        pointAmount: 100,
      }, mockUser as any);

      // Assertions
      expect(service.validateCoupon).toHaveBeenCalledWith({
        couponCode: 'COUPON1',
        pointId: 1,
        pointAmount: 100,
      }, 1);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockValidationResult);
    });
  });
});
