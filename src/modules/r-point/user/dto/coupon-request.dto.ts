import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsPositive, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho request lấy coupon ngẫu nhiên
 */
export class RandomCouponRequestDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1,
    required: true
  })
  @IsNotEmpty({ message: 'ID gói point không được để trống' })
  @IsNumber({}, { message: 'ID gói point phải là số' })
  @Type(() => Number)
  pointId: number;

  @ApiProperty({
    description: 'Số lượng point muốn mua',
    example: 100,
    required: true
  })
  @IsNotEmpty({ message: 'Số lượng point không được để trống' })
  @IsNumber({}, { message: 'Số lượng point phải là số' })
  @IsPositive({ message: '<PERSON><PERSON> lượng point phải là số dương' })
  @Type(() => Number)
  pointAmount: number;
}

/**
 * DTO cho request kiểm tra mã coupon
 */
export class ValidateCouponRequestDto {
  @ApiProperty({
    description: 'Mã coupon cần kiểm tra',
    example: 'SUMMER2023',
    required: true
  })
  @IsNotEmpty({ message: 'Mã coupon không được để trống' })
  @IsString({ message: 'Mã coupon phải là chuỗi' })
  couponCode: string;

  @ApiProperty({
    description: 'ID của gói point',
    example: 1,
    required: true
  })
  @IsNotEmpty({ message: 'ID gói point không được để trống' })
  @IsNumber({}, { message: 'ID gói point phải là số' })
  @Type(() => Number)
  pointId: number;

  @ApiProperty({
    description: 'Số lượng point muốn mua',
    example: 100,
    required: true
  })
  @IsNotEmpty({ message: 'Số lượng point không được để trống' })
  @IsNumber({}, { message: 'Số lượng point phải là số' })
  @IsPositive({ message: 'Số lượng point phải là số dương' })
  @Type(() => Number)
  pointAmount: number;
}
