import { ApiProperty } from '@nestjs/swagger';
import { CouponResponseDto } from './coupon-response.dto';

/**
 * DTO cho response trả về kết quả áp dụng mã giảm giá
 */
export class ApplyCouponResponseDto {
  @ApiProperty({
    description: 'Số R-Point',
    example: 100
  })
  pointAmount: number;

  @ApiProperty({
    description: 'Giá niêm yết (VND)',
    example: 100000
  })
  listPrice: number;

  @ApiProperty({
    description: 'Tổng tiền trước VAT (VND)',
    example: 90000
  })
  priceBeforeVAT: number;

  @ApiProperty({
    description: 'Thuế VAT (VND)',
    example: 0
  })
  vatAmount: number;

  @ApiProperty({
    description: 'Tổng tiền thanh toán (VND)',
    example: 90000
  })
  totalPayment: number;

  @ApiProperty({
    description: 'Thông tin mã giảm giá đã áp dụng',
    type: CouponResponseDto
  })
  appliedCoupon: CouponResponseDto;

  @ApiProperty({
    description: 'Số tiền được giảm giá (VND)',
    example: 10000
  })
  discountAmount: number;
}
