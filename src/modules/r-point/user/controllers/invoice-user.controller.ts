import { Controller, Get, Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { InvoiceUserService } from '@modules/r-point/user/services';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { InvoiceDownloadResponseDto } from '../dto';

@ApiTags('R-Point - User Invoices')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/r-point/invoices')
export class InvoiceUserController {
  constructor(private readonly invoiceUserService: InvoiceUserService) {}

  /**
   * Lấy URL tải xuống hóa đơn
   * @param user Thông tin người dùng hiện tại
   * @param id ID của hóa đơn
   * @returns URL tải xuống hóa đơn
   */
  @Get(':id/download')
  @ApiOperation({ summary: 'Lấy URL tải xuống hóa đơn' })
  @ApiParam({ name: 'id', description: 'ID của hóa đơn', type: Number })
  @ApiResponse({
    status: 200,
    description: 'URL tải xuống hóa đơn',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy URL tải xuống hóa đơn thành công' },
        result: {
          type: 'object',
          properties: {
            downloadUrl: { type: 'string', example: 'https://cdn.example.com/invoices/invoice-123.pdf?expires=1625097600&signature=abc123' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Chưa đăng nhập' })
  @ApiResponse({ status: 403, description: 'Không có quyền truy cập hóa đơn này' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy hóa đơn hoặc hóa đơn không có file PDF' })
  async getInvoiceDownloadUrl(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number
  ): Promise<ApiResponseDto<InvoiceDownloadResponseDto>> {
    const result = await this.invoiceUserService.getInvoiceDownloadUrl(user.id, id);
    return ApiResponseDto.success(result, 'Lấy URL tải xuống hóa đơn thành công');
  }
}
