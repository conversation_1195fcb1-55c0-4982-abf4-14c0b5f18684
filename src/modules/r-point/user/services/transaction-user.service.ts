import { Injectable } from '@nestjs/common';
import { Between, ILike, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { UserTransactionQueryDto } from '../dto/transaction-request.dto';
import { UserTransactionResponseDto } from '../dto/transaction-response.dto';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@common/response';

/**
 * Service xử lý logic liên quan đến giao dịch mua point cho người dùng
 */
@Injectable()
export class TransactionUserService {
  constructor(
    private readonly transactionRepository: PointPurchaseTransactionRepository,
  ) {}

  /**
   * L<PERSON>y danh sách lịch sử giao dịch mua point của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  async getUserTransactions(
    userId: number,
    queryDto: UserTransactionQueryDto,
  ): Promise<PaginatedResult<UserTransactionResponseDto>> {
    if (!userId) {
      throw new AppException(ErrorCode.USER_NOT_FOUND, 'Không tìm thấy người dùng');
    }

    const {
      page = 1,
      limit = 10,
      status,
      search,
      startTime,
      endTime,
    } = queryDto;
    const skip = (page - 1) * limit;

    // Xây dựng điều kiện truy vấn
    const whereConditions: any = { userId };

    // Lọc theo trạng thái nếu có
    if (status) {
      whereConditions.status = status;
    }

    // Lọc theo từ khóa (mã tham chiếu)
    if (search) {
      whereConditions.referenceId = ILike(`%${search}%`);
    }

    // Lọc theo thời gian
    if (startTime && endTime) {
      whereConditions.createdAt = Between(startTime, endTime);
    } else if (startTime) {
      whereConditions.createdAt = MoreThanOrEqual(startTime);
    } else if (endTime) {
      whereConditions.createdAt = LessThanOrEqual(endTime);
    }

    // Đếm tổng số bản ghi
    const totalItems = await this.transactionRepository.count(whereConditions);

    // Lấy danh sách giao dịch với chỉ các trường cần thiết
    const transactions = await this.transactionRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
      select: ['id', 'amount', 'pointsAmount', 'pointName', 'status', 'completedAt', 'couponAmount', 'createdAt']
    });

    // Map dữ liệu trả về
    const items = transactions.map(transaction => {
      const responseItem: UserTransactionResponseDto = {
        id: transaction.id,
        amount: transaction.amount,
        pointsAmount: transaction.pointsAmount,
        pointName: transaction.pointName,
        status: transaction.status,
        completedAt: transaction.completedAt || 0,
        couponAmount: transaction.couponAmount || 0,
        createdAt: transaction.createdAt
      };

      return responseItem;
    });

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }
}
