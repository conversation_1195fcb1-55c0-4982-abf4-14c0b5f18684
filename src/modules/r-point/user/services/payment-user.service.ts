import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AppException, ErrorCode } from '@common/exceptions';
import { ConfigService } from '@nestjs/config';
import { SepayHubService } from '@shared/services/sepay-hub';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { TransactionStatus } from '../../enums/transaction-status.enum';
import { WebhookRequestDto } from '../dto/webhook-request.dto';
import { AffiliateService } from '@/modules/affiliate/admin/services/affiliate.service';
import {
  Coupon,
  Point,
  PointPurchaseTransaction,
  WebhookLog,
} from '../../entities';
import { SystemConfigurationService } from '@/modules/system-configuration';
import { UserService } from '@/modules/user/user/service';

@Injectable()
export class PaymentUserService {
  private readonly logger = new Logger(PaymentUserService.name);
  private readonly webhookApiKey: string;
  private readonly transactionPrefix: string = 'RPOINT';
  private readonly transactionExpireMinutes: number = 15;

  constructor(
    @InjectRepository(PointPurchaseTransaction)
    private readonly transactionRepository: Repository<PointPurchaseTransaction>,
    @InjectRepository(Point)
    private readonly pointRepository: Repository<Point>,
    @InjectRepository(Coupon)
    private readonly couponRepository: Repository<Coupon>,
    @InjectRepository(WebhookLog)
    private readonly webhookLogRepository: Repository<WebhookLog>,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly affiliateService: AffiliateService,
    private readonly sepayHubService: SepayHubService,
    private readonly systemConfigurationService: SystemConfigurationService,
  ) {
    // this.webhookApiKey = this.configService.get<string>('WEBHOOK_API_KEY');
  }

  /**
   * Tạo giao dịch mua R-Point mới
   * @param userId ID của người dùng
   * @param createPaymentDto Thông tin mua R-Point
   * @returns Thông tin giao dịch và QR code
   */
  async createPayment(userId: number, createPaymentDto: CreatePaymentDto) {
    // try {
    //   // Kiểm tra người dùng tồn tại
    //   const user = await this.userService.findOne(userId);
    //   if (!user) {
    //     throw new AppException(
    //       ErrorCode.USER_NOT_FOUND,
    //       'Không tìm thấy người dùng',
    //     );
    //   }

    //   this.logger.log(`Đã tìm thấy người dùng: ${user.id}`);

    //   // Tìm gói R-point theo ID
    //   const pointPackage = await this.pointRepository.findOne({
    //     where: { id: createPaymentDto.pointPackageId },
    //   });

    //   if (!pointPackage) {
    //     throw new AppException(
    //       ErrorCode.RESOURCE_NOT_FOUND,
    //       'Không tìm thấy gói R-Point hoặc gói không hoạt động',
    //     );
    //   }

    //   this.logger.log(
    //     `Đã tìm thấy gói R-Point: ${pointPackage.name}, loại: ${pointPackage.id}`,
    //   );

    //   // Tính toán số lượng point và số tiền cần thanh toán
    //   let pointAmount: number;
    //   let originalAmount: number;

    //   if (pointPackage.isCustomize === false) {
    //     // Gói thường: số lượng point và giá tiền cố định
    //     if (!pointPackage.point || !pointPackage.cash) {
    //       throw new AppException(
    //         ErrorCode.VALIDATION_ERROR,
    //         'Gói R-Point không hợp lệ',
    //       );
    //     }

    //     pointAmount = pointPackage.point;
    //     originalAmount = pointPackage.cash;

    //     this.logger.log(
    //       `Gói thường - Số point: ${pointAmount}, Giá gốc: ${originalAmount} VND`,
    //     );
    //   } else {
    //     // Gói customize: số lượng point từ DTO, giá tiền tính theo tỷ lệ
    //     if (!createPaymentDto.amount) {
    //       throw new AppException(
    //         ErrorCode.VALIDATION_ERROR,
    //         'Vui lòng nhập số lượng R-Point',
    //       );
    //     }

    //     if (!pointPackage.rate) {
    //       throw new AppException(
    //         ErrorCode.VALIDATION_ERROR,
    //         'Gói R-Point không hợp lệ',
    //       );
    //     }

    //     pointAmount = createPaymentDto.amount;
    //     originalAmount = pointPackage.rate * pointAmount;

    //     // Kiểm tra giới hạn min/max
    //     if (pointPackage.min && originalAmount < pointPackage.min) {
    //       throw new AppException(
    //         ErrorCode.VALIDATION_ERROR,
    //         `Số lượng R-Point phải lớn hơn hoặc bằng ${pointPackage.min}`,
    //       );
    //     }

    //     if (pointPackage.max && originalAmount > pointPackage.max) {
    //       throw new AppException(
    //         ErrorCode.VALIDATION_ERROR,
    //         `Số lượng R-Point phải nhỏ hơn hoặc bằng ${pointPackage.maxx}`,
    //       );
    //     }

    //     pointAmount = createPaymentDto.amount;
    //     originalAmount = pointAmount * pointPackage.rate;

    //     this.logger.log(
    //       `Gói tùy chỉnh - Số point: ${pointAmount}, Tỷ lệ: ${pointPackage.rate}, Giá gốc: ${originalAmount} VND`,
    //     );
    //   }

    //   // Tính toán số tiền cuối cùng sau khi áp dụng khuyến mãi (nếu có)
    //   let finalAmount = originalAmount;
    //   let appliedCoupon = null;

    //   // Kiểm tra mã khuyến mãi nếu có
    //   if (createPaymentDto.couponCode) {
    //     this.logger.log(
    //       `Kiểm tra mã khuyến mãi: ${createPaymentDto.couponCode}`,
    //     );

    //     const coupon = await this.couponRepository.findOne({
    //       where: {
    //         code: createPaymentDto.couponCode,
    //         isActive: true,
    //         startDate: { $lte: new Date() },
    //         endDate: { $gte: new Date() },
    //       },
    //     });

    //     if (!coupon) {
    //       throw new AppException(
    //         ErrorCode.COUPON_NOT_FOUND,
    //         'Mã khuyến mãi không hợp lệ hoặc đã hết hạn',
    //       );
    //     }

    //     // Kiểm tra số lần sử dụng của mã
    //     if (coupon.maxUsage > 0) {
    //       const usageCount = await this.transactionRepository.count({
    //         where: {
    //           couponId: coupon.id,
    //           status: TransactionStatus.SUCCESS,
    //         },
    //       });

    //       if (usageCount >= coupon.maxUsage) {
    //         throw new AppException(
    //           ErrorCode.COUPON_EXCEEDED,
    //           'Mã khuyến mãi đã hết lượt sử dụng',
    //         );
    //       }
    //     }

    //     // Kiểm tra số lần sử dụng của người dùng
    //     if (coupon.maxUsagePerUser > 0) {
    //       const userUsageCount = await this.transactionRepository.count({
    //         where: {
    //           couponId: coupon.id,
    //           userId: userId,
    //           status: TransactionStatus.SUCCESS,
    //         },
    //       });

    //       if (userUsageCount >= coupon.maxUsagePerUser) {
    //         throw new AppException(
    //           ErrorCode.COUPON_USER_EXCEEDED,
    //           'Bạn đã sử dụng hết lượt dùng mã khuyến mãi này',
    //         );
    //       }
    //     }

    //     // Áp dụng giảm giá
    //     const originalFinalAmount = finalAmount;
    //     if (coupon.discountType === 'PERCENTAGE') {
    //       finalAmount = originalAmount * (1 - coupon.discountValue / 100);
    //       this.logger.log(
    //         `Áp dụng giảm giá phần trăm: ${coupon.discountValue}%, Giá gốc: ${originalFinalAmount} VND, Giá sau giảm: ${finalAmount} VND`,
    //       );
    //     } else {
    //       finalAmount = originalAmount - coupon.discountValue;
    //       this.logger.log(
    //         `Áp dụng giảm giá cố định: ${coupon.discountValue} VND, Giá gốc: ${originalFinalAmount} VND, Giá sau giảm: ${finalAmount} VND`,
    //       );
    //     }

    //     // Đảm bảo số tiền không âm
    //     finalAmount = Math.max(finalAmount, 0);
    //     appliedCoupon = coupon;
    //   } else {
    //     this.logger.log(`Không có mã khuyến mãi, giá cuối: ${finalAmount} VND`);
    //   }

    //   // Tạo mã giao dịch
    //   const transactionId = uuidv4();
    //   const transactionCode = `${this.transactionPrefix}${Date.now().toString().slice(-6)}`;

    //   this.logger.log(`Tạo mã giao dịch: ${transactionCode}`);

    //   // Tạo giao dịch mới
    //   const transaction = this.transactionRepository.create({
    //     id: transactionId,
    //     userId: userId,
    //     pointPackageId: pointPackage.id,
    //     pointAmount: pointAmount,
    //     originalAmount: originalAmount,
    //     finalAmount: finalAmount,
    //     couponId: appliedCoupon?.id,
    //     status: TransactionStatus.PENDING,
    //     transactionCode: transactionCode,
    //     expiredAt: new Date(
    //       Date.now() + this.transactionExpireMinutes * 60 * 1000,
    //     ),
    //     createdAt: new Date(),
    //     updatedAt: new Date(),
    //   });

    //   await this.transactionRepository.save(transaction);
    //   this.logger.log(`Đã lưu giao dịch với ID: ${transactionId}`);

    //   // Tạo QR code thanh toán
    //   const qrCodeUrl =
    //     await this.systemConfigurationService.generateQRPaymentUrl(
    //       finalAmount,
    //       transactionCode,
    //     );

    //   this.logger.log(
    //     `Đã tạo QR code thanh toán cho giao dịch: ${transactionId}`,
    //   );

    //   return {
    //     transactionId: transaction.id,
    //     pointAmount: transaction.pointAmount,
    //     amount: transaction.finalAmount,
    //     originalAmount: transaction.originalAmount,
    //     qrCodeUrl: qrCodeUrl,
    //     expiredAt: transaction.expiredAt,
    //     status: transaction.status,
    //   };
    // } catch (error) {
    //   this.logger.error(
    //     `Error creating payment: ${error.message}`,
    //     error.stack,
    //   );
    //   if (error instanceof AppException) {
    //     throw error;
    //   }
    //   throw new AppException(
    //     ErrorCode.INTERNAL_SERVER_ERROR,
    //     'Lỗi khi tạo giao dịch',
    //   );
    // }
  }

  /**
   * Lấy thông tin giao dịch
   * @param userId ID của người dùng
   * @param transactionId ID của giao dịch
   * @returns Thông tin giao dịch
   */
  // async getTransaction(userId: number, transactionId: string) {
  //   try {
  //     const transaction = await this.transactionRepository.findOne({
  //       where: {
  //         id: transactionId,
  //         userId: userId,
  //       },
  //     });

  //     if (!transaction) {
  //       throw new AppException(
  //         ErrorCode.RESOURCE_NOT_FOUND,
  //         'Không tìm thấy giao dịch',
  //       );
  //     }

  //     return {
  //       transactionId: transaction.id,
  //       pointAmount: transaction.pointAmount,
  //       amount: transaction.finalAmount,
  //       status: transaction.status,
  //       createdAt: transaction.createdAt,
  //       updatedAt: transaction.updatedAt,
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error getting transaction: ${error.message}`,
  //       error.stack,
  //     );
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       ErrorCode.INTERNAL_SERVER_ERROR,
  //       'Lỗi khi lấy thông tin giao dịch',
  //     );
  //   }
  // }

  // /**
  //  * Xử lý webhook từ ngân hàng
  //  * @param webhookRequest Dữ liệu webhook
  //  * @param apiKey API key từ header
  //  * @returns Kết quả xử lý
  //  */
  // async processWebhook(webhookRequest: WebhookRequestDto, apiKey: string) {
  //   try {
  //     // Kiểm tra API key
  //     if (apiKey !== this.webhookApiKey) {
  //       throw new AppException(
  //         ErrorCode.UNAUTHORIZED_ACCESS,
  //         'API key không hợp lệ',
  //       );
  //     }

  //     // Lưu log webhook
  //     const webhookLog = this.webhookLogRepository.create({
  //       requestId: webhookRequest.id.toString(),
  //       requestBody: JSON.stringify(webhookRequest),
  //       createdAt: new Date(),
  //     });
  //     await this.webhookLogRepository.save(webhookLog);

  //     // Chỉ xử lý giao dịch tiền vào
  //     if (webhookRequest.transferType !== 'in') {
  //       return {
  //         success: true,
  //         message: 'Không phải giao dịch tiền vào, bỏ qua',
  //       };
  //     }

  //     // Tìm giao dịch dựa trên mã giao dịch trong nội dung chuyển khoản
  //     const transactionCode = webhookRequest.content.trim();
  //     const transaction = await this.transactionRepository.findOne({
  //       where: {
  //         transactionCode: transactionCode,
  //         status: TransactionStatus.PENDING,
  //       },
  //     });

  //     if (!transaction) {
  //       return {
  //         success: true,
  //         message:
  //           'Không tìm thấy giao dịch hoặc giao dịch không ở trạng thái chờ thanh toán',
  //       };
  //     }

  //     // Kiểm tra số tiền
  //     if (webhookRequest.transferAmount < transaction.finalAmount) {
  //       // Cập nhật trạng thái giao dịch thành thất bại
  //       transaction.status = TransactionStatus.FAILED;
  //       transaction.updatedAt = new Date();
  //       transaction.note = `Số tiền thanh toán không đủ: ${webhookRequest.transferAmount} < ${transaction.finalAmount}`;
  //       await this.transactionRepository.save(transaction);

  //       return {
  //         success: true,
  //         message: 'Số tiền thanh toán không đủ',
  //       };
  //     }

      // // Kiểm tra thời gian hết hạn
      // if (transaction.expiredAt < new Date()) {
      //   // Cập nhật trạng thái giao dịch thành hết hạn
      //   transaction.status = TransactionStatus.EXPIRED;
      //   transaction.updatedAt = new Date();
      //   transaction.note = 'Giao dịch đã hết hạn';
      //   await this.transactionRepository.save(transaction);

      //   return {
      //     success: true,
      //     message: 'Giao dịch đã hết hạn',
      //   };
      // }

      // // Cập nhật trạng thái giao dịch thành công
      // transaction.status = TransactionStatus.SUCCESS;
      // transaction.updatedAt = new Date();
      // transaction.paidAmount = webhookRequest.transferAmount;
      // transaction.paidAt = new Date(webhookRequest.transactionDate);
      // transaction.bankReference = webhookRequest.referenceCode;
      // await this.transactionRepository.save(transaction);

      // // Cộng R-Point cho người dùng
      // await this.addPointsToUser(
      //   transaction.userId,
      //   transaction.pointAmount,
      //   transaction.id,
      // );

      // Xử lý affiliate nếu người dùng có người giới thiệu
      // await this.processAffiliateReward(transaction);

      // Gửi thông báo cho người dùng (có thể triển khai sau)
      // await this.notificationService.sendPaymentSuccessNotification(transaction.userId, transaction);

  //     return {
  //       success: true,
  //       message: 'Xử lý webhook thành công',
  //     };
  //   } catch (error) {
  //     this.logger.error(
  //       `Error processing webhook: ${error.message}`,
  //       error.stack,
  //     );
  //     if (error instanceof AppException) {
  //       throw error;
  //     }
  //     throw new AppException(
  //       ErrorCode.INTERNAL_SERVER_ERROR,
  //       'Lỗi khi xử lý webhook',
  //     );
  //   }
  // }

  // /**
  //  * Cộng R-Point cho người dùng
  //  * @param userId ID của người dùng
  //  * @param pointAmount Số lượng R-Point
  //  * @param transactionId ID của giao dịch
  //  */
  // private async addPointsToUser(
  //   userId: number,
  //   pointAmount: number,
  //   transactionId: string,
  // ) {
  //   try {
  //     // Kiểm tra người dùng tồn tại
  //     const user = await this.userService.findOne(userId);
  //     if (!user) {
  //       throw new AppException(
  //         ErrorCode.USER_NOT_FOUND,
  //         'Không tìm thấy người dùng',
  //       );
  //     }

      // // Tìm bản ghi Point của người dùng hoặc tạo mới nếu chưa có
      // let userPoint = await this.pointRepository.findOne({
      //   where: { userId: userId },
      // });

      // if (!userPoint) {
      //   userPoint = this.pointRepository.create({
      //     // userId: userId,
      //     balance: 0,
      //     createdAt: new Date(),
      //     updatedAt: new Date(),
      //   });
      // }

      // Cộng điểm
      // userPoint.balance += pointAmount;
      // userPoint.updatedAt = new Date();
      // await this.pointRepository.save(userPoint);

      // Tạo lịch sử giao dịch điểm (có thể triển khai sau)
      // await this.pointHistoryRepository.save({
      //   userId: userId,
      //   amount: pointAmount,
      //   type: 'PURCHASE',
      //   referenceId: transactionId,
      //   createdAt: new Date()
      // });
  //   } catch (error) {
  //     this.logger.error(
  //       `Error adding points to user: ${error.message}`,
  //       error.stack,
  //     );
  //     throw error;
  //   }
  // }

  // /**
  //  * Xử lý phần thưởng affiliate
  //  * @param transaction Thông tin giao dịch
  //  */
  // private async processAffiliateReward(transaction: PointPurchaseTransaction) {
    // try {
    //   // Kiểm tra người dùng có người giới thiệu không
    //   const user = await this.userService.findOne(transaction.userId);
    //   if (!user || !user.referrerId) {
    //     return; // Không có người giới thiệu, bỏ qua
    //   }

    //   // Tạo lịch sử đơn hàng affiliate
    //   await this.affiliateService.createAffiliateOrder({
    //     userId: transaction.userId,
    //     referrerId: user.referrerId,
    //     orderId: transaction.id,
    //     orderAmount: transaction.finalAmount,
    //     orderType: 'POINT_PURCHASE',
    //     createdAt: new Date(),
    //   });

      // Xử lý hoa hồng cho người giới thiệu (có thể triển khai sau)
      // await this.affiliateService.processCommission(transaction.userId, user.referrerId, transaction.finalAmount);
  //   } catch (error) {
  //     this.logger.error(
  //       `Error processing affiliate reward: ${error.message}`,
  //       error.stack,
  //     );
  //     // Không throw lỗi ở đây để không ảnh hưởng đến luồng chính
  //   }
  // }
}
