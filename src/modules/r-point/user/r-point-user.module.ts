import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as services from './services';
import * as controllers from './controllers';
import { User } from '@modules/user/entities';
import { SystemConfigurationModule } from '@modules/system-configuration';
import { RedisService } from '@shared/services/redis.service';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { CdnService } from '@shared/services/cdn.service';
import { Bank } from '@modules/user/entities/bank.entity';

/**
 * Module quản lý R-Point cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities), User, Bank]),
    SystemConfigurationModule, // Import SystemConfigurationModule để sử dụng SystemConfigurationService
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
    RedisService,
    BankRepository,
    CdnService,
  ],
  exports: [
    ...Object.values(services),
  ],
})
export class RPointUserModule {}
