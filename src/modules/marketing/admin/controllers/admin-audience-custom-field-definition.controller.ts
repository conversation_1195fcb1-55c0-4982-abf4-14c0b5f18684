import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { AdminAudienceCustomFieldDefinitionService } from '../services/admin-audience-custom-field-definition.service';
import {
  CreateAudienceCustomFieldDefinitionDto,
  UpdateAudienceCustomFieldDefinitionDto,
  AudienceCustomFieldDefinitionResponseDto,
  AudienceCustomFieldDefinitionQueryDto,
} from '../dto/audience-custom-field-definition';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { MARKETING_ERROR_CODES } from '@modules/marketing/errors/marketing-error.code';

/**
 * Controller xử lý các API liên quan đến trường tùy chỉnh của admin
 */
@ApiTags(SWAGGER_API_TAGS.MARKETING_ADMIN)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/marketing/audience-custom-fields')
export class AdminAudienceCustomFieldDefinitionController {
  constructor(private readonly customFieldService: AdminAudienceCustomFieldDefinitionService) {}

  /**
   * Tạo mới trường tùy chỉnh
   * @param employee Thông tin admin
   * @param createDto Dữ liệu tạo mới
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới trường tùy chỉnh' })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_ALREADY_EXISTS,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
  )
  async create(
    @CurrentEmployee() employee: JwtPayload,
    @Body() createDto: CreateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.create(employee.id, createDto);
    return wrapResponse(result, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @param updateDto Dữ liệu cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Put(':fieldKey')
  @ApiOperation({ summary: 'Cập nhật trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
  )
  async update(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
    @Body() updateDto: UpdateAudienceCustomFieldDefinitionDto,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.update(fieldKey, updateDto);
    return wrapResponse(result, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã xóa
   */
  @Delete(':fieldKey')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(
    MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    MARKETING_ERROR_CODES.CUSTOM_FIELD_DELETION_FAILED,
  )
  async delete(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.delete(employee.id, fieldKey);
    return wrapResponse(result, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin trường tùy chỉnh
   * @param employee Thông tin admin
   * @param fieldKey Định danh của trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh
   */
  @Get(':fieldKey')
  @ApiOperation({ summary: 'Lấy thông tin trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  @ApiErrorResponse(MARKETING_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND)
  async findOne(
    @CurrentEmployee() employee: JwtPayload,
    @Param('fieldKey') fieldKey: string,
  ): Promise<ApiResponseDto<AudienceCustomFieldDefinitionResponseDto>> {
    const result = await this.customFieldService.findOne(employee.id, fieldKey);
    return wrapResponse(result);
  }

  /**
   * Lấy danh sách trường tùy chỉnh
   * @param employee Thông tin admin
   * @param queryDto Tham số truy vấn
   * @returns Danh sách trường tùy chỉnh
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh',
    schema: ApiResponseDto.getPaginatedSchema(AudienceCustomFieldDefinitionResponseDto),
  })
  async findAll(
    @Query() queryDto: AudienceCustomFieldDefinitionQueryDto,
  ): Promise<ApiResponseDto<PaginatedResponseDto<AudienceCustomFieldDefinitionResponseDto>>> {
    const result = await this.customFieldService.findAll(queryDto);
    return wrapResponse(result);
  }
}
