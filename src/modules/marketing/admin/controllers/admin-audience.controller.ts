import { Controller, Get, Post, Body, Param, Delete, Put, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { AdminAudienceService } from '../services/admin-audience.service';
import { CreateAudienceDto, UpdateAudienceDto, AudienceResponseDto, AudienceQueryDto } from '../dto/audience';
import { PaginatedResponseDto } from '../dto/common';
import { ApiResponseDto as AppApiResponse } from '@/common/response';
import { wrapResponse } from '@/modules/marketing/common/helpers/response.helper';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller cho AdminAudience
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AUDIENCE)
@Controller('admin/marketing/audiences')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth()
export class AdminAudienceController {
  constructor(private readonly adminAudienceService: AdminAudienceService) {}

  /**
   * Tạo audience mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo audience mới' })
  @ApiResponse({ status: 201, description: 'Audience đã tạo', type: AudienceResponseDto })
  async create(
    @Body() createAudienceDto: CreateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.create(createAudienceDto);
    return wrapResponse(result, 'Audience đã được tạo thành công');
  }

  /**
   * Lấy danh sách audience với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách audience với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách audience với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/AudienceResponseDto' }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @Query() query: AudienceQueryDto
  ): Promise<AppApiResponse<PaginatedResponseDto<AudienceResponseDto>>> {
    const result = await this.adminAudienceService.findAll(query);
    return wrapResponse(result, 'Danh sách audience');
  }

  /**
   * Lấy audience theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy audience theo ID' })
  @ApiResponse({ status: 200, description: 'Audience', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async findOne(
    @Param('id') id: string,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.findOne(+id);
    return wrapResponse(result, 'Thông tin audience');
  }

  /**
   * Cập nhật audience
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật audience' })
  @ApiResponse({ status: 200, description: 'Audience đã cập nhật', type: AudienceResponseDto })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async update(
    @Param('id') id: string,
    @Body() updateAudienceDto: UpdateAudienceDto,
  ): Promise<AppApiResponse<AudienceResponseDto>> {
    const result = await this.adminAudienceService.update(+id, updateAudienceDto);
    return wrapResponse(result, 'Audience đã được cập nhật thành công');
  }

  /**
   * Xóa audience
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa audience' })
  @ApiResponse({ status: 200, description: 'true nếu xóa thành công', type: Boolean })
  @ApiResponse({ status: 404, description: 'Audience không tồn tại' })
  async remove(
    @Param('id') id: string,
  ): Promise<AppApiResponse<boolean>> {
    const result = await this.adminAudienceService.remove(+id);
    return wrapResponse(result, 'Audience đã được xóa thành công');
  }
}
