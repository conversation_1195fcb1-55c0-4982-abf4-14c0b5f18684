import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { AdminTemplateSms } from '@modules/marketing/admin/entities';

/**
 * Repository cho AdminTemplateSms
 */
@Injectable()
export class AdminTemplateSmsRepository {
  constructor(
    @InjectRepository(AdminTemplateSms)
    private readonly repository: Repository<AdminTemplateSms>,
  ) {}

  /**
   * Tìm kiếm nhiều template SMS
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách template SMS
   */
  async find(options?: FindManyOptions<AdminTemplateSms>): Promise<AdminTemplateSms[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một template SMS
   * @param options Tùy chọn tìm kiếm
   * @returns Template SMS hoặc null
   */
  async findOne(options?: FindOneOptions<AdminTemplateSms>): Promise<AdminTemplateSms | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lấy template SMS theo ID
   * @param id ID của template
   * @returns Template SMS
   */
  async findById(id: number): Promise<AdminTemplateSms> {
    const template = await this.repository.findOne({ where: { id } });
    if (!template) {
      throw new NotFoundException(`Template SMS với ID ${id} không tồn tại`);
    }
    return template;
  }

  /**
   * Tìm template SMS theo category
   * @param category Danh mục của template
   * @returns Template SMS
   */
  async findByCategory(category: string): Promise<AdminTemplateSms> {
    const template = await this.repository.findOne({ where: { category } });
    if (!template) {
      throw new NotFoundException(`Template SMS với category ${category} không tồn tại`);
    }
    return template;
  }

  /**
   * Lưu template SMS
   * @param template Template SMS cần lưu
   * @returns Template SMS đã lưu
   */
  async save(template: AdminTemplateSms): Promise<AdminTemplateSms>;
  async save(template: AdminTemplateSms[]): Promise<AdminTemplateSms[]>;
  async save(template: AdminTemplateSms | AdminTemplateSms[]): Promise<AdminTemplateSms | AdminTemplateSms[]> {
    return this.repository.save(template as any);
  }

  /**
   * Tạo mới template SMS
   * @param data Dữ liệu template SMS
   * @returns Template SMS đã tạo
   */
  async create(data: Partial<AdminTemplateSms>): Promise<AdminTemplateSms> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template SMS
   * @param id ID của template
   * @param data Dữ liệu cập nhật
   * @returns Template SMS đã cập nhật
   */
  async update(id: number, data: Partial<AdminTemplateSms>): Promise<AdminTemplateSms> {
    await this.findById(id); // Kiểm tra template tồn tại
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa template SMS
   * @param id ID của template
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    await this.findById(id); // Kiểm tra template tồn tại
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa template SMS
   * @param template Template SMS cần xóa
   * @returns Template SMS đã xóa
   */
  async remove(template: AdminTemplateSms): Promise<AdminTemplateSms>;
  async remove(template: AdminTemplateSms[]): Promise<AdminTemplateSms[]>;
  async remove(template: AdminTemplateSms | AdminTemplateSms[]): Promise<AdminTemplateSms | AdminTemplateSms[]> {
    return this.repository.remove(template as any);
  }

  /**
   * Đếm số lượng template SMS
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng template SMS
   */
  async count(options?: FindManyOptions<AdminTemplateSms>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }
}
