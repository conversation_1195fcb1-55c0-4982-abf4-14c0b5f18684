import { EmailVariableDto } from '../dto/template-email/email-variable.dto';

/**
 * Utility functions cho template email
 */
export class TemplateEmailUtils {
  /**
   * Tự động extract placeholders từ htmlContent HTML
   * Pattern: {variable_name}
   * @param htmlContent Nội dung HTML
   * @returns Danh sách tên biến unique
   */
  static extractPlaceholders(htmlContent: string): string[] {
    const regex = /\{([a-zA-Z_][a-zA-Z0-9_]*)\}/g;
    const matches: string[] = [];
    let match;
    
    while ((match = regex.exec(htmlContent)) !== null) {
      matches.push(match[1]);
    }
    
    // Remove duplicates và sort
    return [...new Set(matches)].sort();
  }

  /**
   * Chuyển đổi variables array thành metadata object
   * @param variables Danh sách biến
   * @returns Metadata object
   */
  static processVariableMetadata(variables: EmailVariableDto[]): Record<string, {
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue?: string;
    required?: boolean;
    description?: string;
  }> {
    const metadata: Record<string, any> = {};
    
    variables.forEach(variable => {
      metadata[variable.name] = {
        type: variable.type,
        defaultValue: variable.defaultValue,
        required: variable.required || false,
        description: variable.description
      };
    });
    
    return metadata;
  }

  /**
   * Validate tên biến unique trong danh sách
   * @param variables Danh sách biến
   * @returns true nếu tất cả tên biến unique
   */
  static validateUniqueVariableNames(variables: EmailVariableDto[]): boolean {
    const names = variables.map(v => v.name);
    return names.length === new Set(names).size;
  }

  /**
   * Trim whitespace cho tags và loại bỏ tags rỗng
   * @param tags Danh sách tags
   * @returns Danh sách tags đã được clean
   */
  static cleanTags(tags: string[]): string[] {
    return tags
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
  }

  /**
   * Validate HTML content cơ bản
   * @param htmlContent Nội dung HTML
   * @returns true nếu HTML hợp lệ cơ bản
   */
  static validateHtmlContent(htmlContent: string): boolean {
    // Kiểm tra cơ bản: có ít nhất một tag HTML hoặc text content
    const hasHtmlTags = /<[^>]+>/g.test(htmlContent);
    const hasTextContent = htmlContent.trim().length > 0;
    
    return hasHtmlTags || hasTextContent;
  }

  /**
   * Merge placeholders từ htmlContent và variables
   * Ưu tiên placeholders từ htmlContent, bổ sung từ variables nếu có
   * @param htmlContent Nội dung HTML
   * @param variables Danh sách biến
   * @returns Danh sách placeholders merged
   */
  static mergePlaceholders(htmlContent: string, variables: EmailVariableDto[] = []): string[] {
    const contentPlaceholders = this.extractPlaceholders(htmlContent);
    const variableNames = variables.map(v => v.name);
    
    // Merge và remove duplicates
    const allPlaceholders = [...contentPlaceholders, ...variableNames];
    return [...new Set(allPlaceholders)].sort();
  }
}
