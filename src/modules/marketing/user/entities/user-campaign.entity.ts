import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_campaigns trong cơ sở dữ liệu
 * Bảng chiến dịch của người dùng
 */
@Entity('user_campaigns')
export class UserCampaign {
  /**
   * ID của campaign
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Mã người dùng' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tiêu đề chiến dịch
   */
  @Column({ name: 'title', length: 255, nullable: true, comment: 'Tiêu đề' })
  title: string;

  /**
   * <PERSON>ô tả chiến dịch
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: '<PERSON><PERSON> tả' })
  description: string;

  /**
   * <PERSON><PERSON><PERSON> tảng gửi (email, sms, ...)
   */
  @Column({ name: 'platform', length: 255, nullable: true, comment: 'Nền tảng' })
  platform: string;

  /**
   * Nội dung chiến dịch
   */
  @Column({ name: 'content', type: 'text', nullable: true, comment: 'Nội dung' })
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  @Column({ name: 'server', type: 'jsonb', nullable: true, comment: 'Thông tin máy chủ gửi' })
  server: any;

  /**
   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true, comment: 'Thời gian dự kiến gửi chiến dịch' })
  scheduledAt: number;

  /**
   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)
   */
  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Nội dung tiêu đề với chiến dịch là email' })
  subject: string;

  /**
   * Trạng thái chiến dịch
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })
  status: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Ngày cập nhật' })
  updatedAt: number;

  // Không sử dụng quan hệ với bảng UserCampaignHistory, chỉ lưu ID

  /**
   * ID của segment (nếu có)
   */
  @Column({ name: 'segment_id', type: 'bigint', nullable: true, comment: 'ID của segment' })
  segmentId: number | null;

  /**
   * Danh sách ID của audience (nếu có)
   */
  @Column({ name: 'audience_ids', type: 'jsonb', nullable: true, comment: 'Danh sách ID của audience' })
  audienceIds: number[] | null;
}
