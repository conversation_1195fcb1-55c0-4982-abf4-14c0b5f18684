import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_tags trong cơ sở dữ liệu
 */
@Entity('user_tags')
export class UserTag {
  /**
   * ID của tag
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên tag
   */
  @Column({ name: 'name', length: 255, nullable: true })
  name: string;

  /**
   * Mã màu của tag (định dạng HEX, ví dụ: #FF0000)
   */
  @Column({ name: 'color', length: 7, nullable: true })
  color: string;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
