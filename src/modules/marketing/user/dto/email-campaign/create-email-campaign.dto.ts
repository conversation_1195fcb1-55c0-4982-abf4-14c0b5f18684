import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, IsArray, IsObject, IsIn, Min } from 'class-validator';

/**
 * DTO cho tạo email campaign
 */
export class CreateEmailCampaignDto {
  /**
   * Tiêu đề campaign
   * @example "Khuyến mãi Black Friday 2024"
   */
  @ApiProperty({
    description: 'Tiêu đề campaign',
    example: 'Khuyến mãi Black Friday 2024',
  })
  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })
  @IsString({ message: 'Tiêu đề phải là chuỗi' })
  title: string;

  /**
   * M<PERSON> tả campaign
   * @example "Chiến dịch email marketing cho sự kiện Black Friday"
   */
  @ApiProperty({
    description: 'Mô tả campaign',
    example: 'Chiến dịch email marketing cho sự kiện Black Friday',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })
  description?: string;

  /**
   * Ti<PERSON><PERSON> đề email
   * @example "🔥 Black Friday - Giảm giá lên đến 70% cho {{customerName}}!"
   */
  @ApiProperty({
    description: 'Tiêu đề email (có thể chứa template variables như {{customerName}})',
    example: '🔥 Black Friday - Giảm giá lên đến 70% cho {{customerName}}!',
  })
  @IsNotEmpty({ message: 'Tiêu đề email không được để trống' })
  @IsString({ message: 'Tiêu đề email phải là chuỗi' })
  subject: string;

  /**
   * Nội dung HTML của email
   * @example "<h1>Xin chào {{customerName}}!</h1><p>Chúng tôi có ưu đãi đặc biệt dành cho bạn...</p>"
   */
  @ApiProperty({
    description: 'Nội dung HTML của email (có thể chứa template variables)',
    example: '<h1>Xin chào {{customerName}}!</h1><p>Chúng tôi có ưu đãi đặc biệt dành cho bạn...</p>',
  })
  @IsNotEmpty({ message: 'Nội dung email không được để trống' })
  @IsString({ message: 'Nội dung email phải là chuỗi' })
  content: string;

  /**
   * ID của segment (nếu gửi cho segment)
   * @example 5
   */
  @ApiProperty({
    description: 'ID của segment (nếu gửi cho segment)',
    example: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID segment phải là số' })
  @Min(1, { message: 'ID segment phải lớn hơn 0' })
  segmentId?: number;

  /**
   * Danh sách ID của audience (nếu gửi cho audience cụ thể)
   * @example [1, 2, 3, 4, 5]
   */
  @ApiProperty({
    description: 'Danh sách ID của audience (nếu gửi cho audience cụ thể)',
    example: [1, 2, 3, 4, 5],
    required: false,
    type: [Number],
  })
  @IsOptional()
  @IsArray({ message: 'Danh sách audience ID phải là mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi audience ID phải là số' })
  audienceIds?: number[];

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp). Nếu không có thì gửi ngay lập tức',
    example: 1703980800,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Thời gian gửi phải là số' })
  @Min(Date.now() / 1000, { message: 'Thời gian gửi phải trong tương lai' })
  scheduledAt?: number;

  /**
   * Cấu hình SMTP server (optional)
   */
  @ApiProperty({
    description: 'Cấu hình SMTP server (nếu không có sẽ dùng cấu hình mặc định)',
    required: false,
    example: {
      host: 'smtp.gmail.com',
      port: 587,
      secure: false,
      user: '<EMAIL>',
      password: 'your-app-password',
      from: '<EMAIL>',
    },
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình server phải là object' })
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };
}