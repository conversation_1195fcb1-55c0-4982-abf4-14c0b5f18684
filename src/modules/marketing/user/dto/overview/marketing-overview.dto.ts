import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi overview marketing
 */
export class MarketingOverviewResponseDto {
  /**
   * Tổng số templates
   * @example 25
   */
  @ApiProperty({
    description: 'Tổng số templates',
    example: 25,
  })
  totalTemplates: number;

  /**
   * Tỷ lệ mở email (%)
   * @example 24.5
   */
  @ApiProperty({
    description: 'Tỷ lệ mở email (%)',
    example: 24.5,
  })
  openRate: number;

  /**
   * Tỷ lệ click email (%)
   * @example 3.2
   */
  @ApiProperty({
    description: 'Tỷ lệ click email (%)',
    example: 3.2,
  })
  clickRate: number;

  /**
   * Tổng số email đã gửi
   * @example 1250
   */
  @ApiProperty({
    description: 'Tổng số email đã gửi',
    example: 1250,
  })
  totalEmailsSent: number;
}
