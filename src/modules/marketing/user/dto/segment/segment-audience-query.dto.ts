import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters khi lấy danh sách audience trong segment
 */
export class SegmentAudienceQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo email
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo email',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Email phải là chuỗi' })
  email?: string;
}
