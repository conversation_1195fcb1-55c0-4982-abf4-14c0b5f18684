import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@/common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp segment
 */
export enum SegmentSortField {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho query parameters khi lấy danh sách segment của user
 */
export class SegmentQueryDto extends QueryDto {
  /**
   * Trường sắp xếp
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: SegmentSortField,
    example: SegmentSortField.CREATED_AT,
    default: SegmentSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(SegmentSortField, {
    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(SegmentSortField).join(', ')}`,
  })
  sortBy?: SegmentSortField = SegmentSortField.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, {
    message: `Hướng sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,
  })
  sortDirection?: SortDirection = SortDirection.DESC;

  constructor() {
    super();
    this.sortBy = SegmentSortField.CREATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}
