import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều segment
 */
export class DeleteMultipleSegmentDto {
  @ApiProperty({
    description: 'Danh sách ID của các segment cần xóa',
    example: [1, 2, 3],
    type: [Number],
    minItems: 1,
  })
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsNotEmpty({ message: 'Danh sách IDs không được để trống' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 ID để xóa' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là một số' })
  @Type(() => Number)
  ids: number[];
}

/**
 * DTO cho kết quả xóa nhiều segment
 */
export class DeleteMultipleSegmentResultDto {
  @ApiProperty({
    description: 'Danh sách ID đã xóa thành công',
    example: [1, 2],
    type: [Number],
  })
  deletedIds: number[];

  @ApiProperty({
    description: 'Danh sách ID xóa thất bại với lý do',
    example: [{ id: 3, reason: 'Segment không tồn tại' }],
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        reason: { type: 'string' }
      }
    }
  })
  failedIds: Array<{ id: number; reason: string }>;

  @ApiProperty({
    description: 'Tổng số segment đã xóa thành công',
    example: 2,
  })
  totalDeleted: number;

  @ApiProperty({
    description: 'Tổng số segment xóa thất bại',
    example: 1,
  })
  totalFailed: number;
}
