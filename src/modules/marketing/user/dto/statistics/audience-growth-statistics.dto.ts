import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho một điểm dữ liệu trong biểu đồ tăng trưởng
 */
export class GrowthDataPointDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON><PERSON> thời gian (ngày, tuần, tháng, năm)',
    example: '2023-01'
  })
  label: string;

  @ApiProperty({
    description: '<PERSON>h<PERSON><PERSON> gian (Unix timestamp)',
    example: 1672531200
  })
  timestamp: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 25
  })
  value: number;
}

/**
 * DTO cho thống kê tăng trưởng audience
 */
export class AudienceGrowthStatisticsDto {
  @ApiProperty({
    description: 'Dữ liệu tăng trưởng audience',
    type: [GrowthDataPointDto]
  })
  audienceGrowth: GrowthDataPointDto[];

  @ApiProperty({
    description: 'Tổng số audience mới trong khoảng thời gian',
    example: 150
  })
  totalNewAudiences: number;

  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng so với khoảng thời gian trước (%)',
    example: 15.5
  })
  growthRate: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200
  })
  updatedAt: number;
}
