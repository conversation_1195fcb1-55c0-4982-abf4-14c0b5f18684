import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê phân phối của một segment
 */
export class SegmentDistributionDto {
  @ApiProperty({
    description: 'ID của segment',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên segment',
    example: 'Kh<PERSON>ch hàng tiềm năng'
  })
  name: string;

  @ApiProperty({
    description: 'Số lượng audience trong segment',
    example: 50
  })
  count: number;

  @ApiProperty({
    description: 'Tỷ lệ phần trăm so với tổng số audience (%)',
    example: 33.3
  })
  percentage: number;
}

/**
 * DTO cho thống kê phân phối segment
 */
export class SegmentDistributionStatisticsDto {
  @ApiProperty({
    description: 'Danh sách phân phối segment',
    type: [SegmentDistributionDto]
  })
  segments: SegmentDistributionDto[];

  @ApiProperty({
    description: 'Tổng số audience',
    example: 150
  })
  totalAudiences: number;

  @ApiProperty({
    description: 'Số lượng audience không thuộc segment nào',
    example: 20
  })
  unassignedAudiences: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200
  })
  updatedAt: number;
}
