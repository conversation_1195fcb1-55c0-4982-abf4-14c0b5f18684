# 🔧 Migration Fix - Template Email API

## ❌ Lỗi gặp phải

```
[2025-05-30 01:19:23] [42601] ERROR: syntax error at or near "NOT"
[2025-05-30 01:19:23] Position: 52
```

## 🔍 Nguyên nhân

PostgreSQL không hỗ trợ `IF NOT EXISTS` cho `ADD CONSTRAINT`. Syntax này chỉ hoạt động với `ADD COLUMN`.

## ✅ Giải pháp

### Cách 1: Sử dụng migration đơn giản (Khuyến nghị)

```bash
# Bước 1: Chạy migration cơ bản
psql -d your_database -f add-new-fields-to-user-template-email-simple.sql

# Bước 2: Chạy constraints (tùy chọn)
psql -d your_database -f add-constraints-to-user-template-email.sql
```

### Cách 2: Ch<PERSON>y từng lệnh riêng biệt

```sql
-- 1. <PERSON><PERSON><PERSON><PERSON> cá<PERSON> cột mới
ALTER TABLE user_template_email ADD COLUMN IF NOT EXISTS text_content TEXT;
ALTER TABLE user_template_email ADD COLUMN IF NOT EXISTS type VARCHAR(50) DEFAULT 'NEWSLETTER';
ALTER TABLE user_template_email ADD COLUMN IF NOT EXISTS preview_text VARCHAR(255);
ALTER TABLE user_template_email ADD COLUMN IF NOT EXISTS variable_metadata JSONB;

-- 2. Cập nhật dữ liệu
UPDATE user_template_email SET type = 'NEWSLETTER' WHERE type IS NULL;

-- 3. Thêm indexes
CREATE INDEX IF NOT EXISTS idx_user_template_email_type ON user_template_email(type);
CREATE INDEX IF NOT EXISTS idx_user_template_email_status ON user_template_email(status);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_template_email_user_name_unique 
ON user_template_email(user_id, name) WHERE name IS NOT NULL;

-- 4. Thêm comments
COMMENT ON COLUMN user_template_email.text_content IS 'Nội dung text thuần của email';
COMMENT ON COLUMN user_template_email.type IS 'Loại template';
COMMENT ON COLUMN user_template_email.preview_text IS 'Preview text hiển thị trong inbox';
COMMENT ON COLUMN user_template_email.variable_metadata IS 'Metadata chi tiết của biến';
```

## 📁 Files Migration

1. **`add-new-fields-to-user-template-email-simple.sql`** - Migration cơ bản (an toàn)
2. **`add-constraints-to-user-template-email.sql`** - Thêm constraints với kiểm tra
3. **`add-new-fields-to-user-template-email.sql`** - Migration đầy đủ (có thể lỗi)

## 🧪 Kiểm tra Migration

```sql
-- Kiểm tra cột đã được thêm
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'user_template_email' 
AND column_name IN ('text_content', 'type', 'preview_text', 'variable_metadata')
ORDER BY ordinal_position;

-- Kiểm tra indexes
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'user_template_email'
AND indexname LIKE '%template_email%';

-- Kiểm tra constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'user_template_email'
AND constraint_name LIKE '%template_email%';
```

## 🚀 Test API

```bash
# Cấp quyền execute cho script test
chmod +x test-api.sh

# Chạy test
./test-api.sh http://localhost:3000 YOUR_JWT_TOKEN
```

## 📋 Checklist Migration

- [ ] ✅ Backup database trước khi chạy migration
- [ ] ✅ Chạy migration trên environment test trước
- [ ] ✅ Kiểm tra các cột mới đã được tạo
- [ ] ✅ Kiểm tra indexes đã được tạo
- [ ] ✅ Kiểm tra unique constraint hoạt động
- [ ] ✅ Test API tạo template email
- [ ] ✅ Kiểm tra validation errors
- [ ] ✅ Kiểm tra auto-extract placeholders

## 🔄 Rollback (nếu cần)

```sql
-- Xóa các cột đã thêm (CẢNH BÁO: Sẽ mất dữ liệu)
ALTER TABLE user_template_email DROP COLUMN IF EXISTS text_content;
ALTER TABLE user_template_email DROP COLUMN IF EXISTS type;
ALTER TABLE user_template_email DROP COLUMN IF EXISTS preview_text;
ALTER TABLE user_template_email DROP COLUMN IF EXISTS variable_metadata;

-- Xóa indexes
DROP INDEX IF EXISTS idx_user_template_email_type;
DROP INDEX IF EXISTS idx_user_template_email_user_name_unique;

-- Xóa constraints
ALTER TABLE user_template_email DROP CONSTRAINT IF EXISTS chk_user_template_email_type;
ALTER TABLE user_template_email DROP CONSTRAINT IF EXISTS chk_user_template_email_status;
```

## 📞 Support

Nếu gặp vấn đề:

1. Kiểm tra phiên bản PostgreSQL: `SELECT version();`
2. Kiểm tra quyền user: `\du`
3. Kiểm tra schema hiện tại: `\d user_template_email`
4. Chạy migration từng bước một
5. Kiểm tra logs chi tiết
