import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, getSchemaPath } from '@nestjs/swagger';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@/modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { ZaloService } from '../services/zalo.service';
import { TagRequestDto } from '../dto/zalo';
import { QueryDto } from '@/common/dto';

/**
 * Controller xử lý API liên quan đến tag người theo dõi Zalo
 */
@ApiTags('Zalo')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('marketing/zalo/:oaId/tags')
export class ZaloTagController {
  constructor(private readonly zaloService: ZaloService) {}

  /**
   * Lấy danh sách tag
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tag' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  followerCount: { type: 'number' },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getTags(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
  ): Promise<ApiResponseDto<any[]>> {
    const tags = await this.zaloService.getZaloTags(user.id, oaId);
    return ApiResponseDto.success(tags, 'Lấy danh sách tag thành công');
  }

  /**
   * Lấy danh sách người theo dõi có tag
   */
  @Get(':tagName/followers')
  @ApiOperation({ summary: 'Lấy danh sách người theo dõi có tag' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách người theo dõi có tag thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              properties: {
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userId: { type: 'string' },
                      displayName: { type: 'string' },
                      avatar: { type: 'string' },
                      tags: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                    },
                  },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  async getFollowersWithTag(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
    @Param('tagName') tagName: string,
    @Query() queryDto: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<any>>> {
    const followers = await this.zaloService.getZaloFollowersWithTag(user.id, oaId, tagName, queryDto);
    return ApiResponseDto.success(followers, 'Lấy danh sách người theo dõi có tag thành công');
  }

  /**
   * Thêm tag cho người theo dõi
   */
  @Post('add-to-follower')
  @ApiOperation({ summary: 'Thêm tag cho người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Thêm tag cho người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' },
          },
        },
      ],
    },
  })
  async addTagToFollower(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
    @Body() tagRequest: TagRequestDto,
  ): Promise<ApiResponseDto<boolean>> {
    // Sử dụng user.id nếu cần thiết trong tương lai
    const result = await this.zaloService.addTagToFollower(oaId, tagRequest.followerId, tagRequest.tagName);
    return ApiResponseDto.success(result, 'Thêm tag cho người theo dõi thành công');
  }

  /**
   * Xóa tag của người theo dõi
   */
  @Post('remove-from-follower')
  @ApiOperation({ summary: 'Xóa tag của người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tag của người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: { type: 'boolean' },
          },
        },
      ],
    },
  })
  async removeTagFromFollower(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
    @Body() tagRequest: TagRequestDto,
  ): Promise<ApiResponseDto<boolean>> {
    // Sử dụng user.id nếu cần thiết trong tương lai
    const result = await this.zaloService.removeTagFromFollower(oaId, tagRequest.followerId, tagRequest.tagName);
    return ApiResponseDto.success(result, 'Xóa tag của người theo dõi thành công');
  }

  /**
   * Thêm tag cho nhiều người theo dõi
   */
  @Post('batch-add')
  @ApiOperation({ summary: 'Thêm tag cho nhiều người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Thêm tag cho nhiều người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                success: { type: 'number' },
                failed: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async batchAddTag(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
    @Body() request: { followerIds: string[]; tagName: string },
  ): Promise<ApiResponseDto<{ success: number; failed: number }>> {
    // Sử dụng user.id nếu cần thiết trong tương lai
    const result = await this.zaloService.batchAddTag(oaId, request.followerIds, request.tagName);
    return ApiResponseDto.success(result, 'Thêm tag cho nhiều người theo dõi thành công');
  }

  /**
   * Xóa tag của nhiều người theo dõi
   */
  @Post('batch-remove')
  @ApiOperation({ summary: 'Xóa tag của nhiều người theo dõi' })
  @ApiResponse({
    status: 200,
    description: 'Xóa tag của nhiều người theo dõi thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                success: { type: 'number' },
                failed: { type: 'number' },
              },
            },
          },
        },
      ],
    },
  })
  async batchRemoveTag(
    @CurrentUser() user: JWTPayload,
    @Param('oaId') oaId: string,
    @Body() request: { followerIds: string[]; tagName: string },
  ): Promise<ApiResponseDto<{ success: number; failed: number }>> {
    // Sử dụng user.id nếu cần thiết trong tương lai
    const result = await this.zaloService.batchRemoveTag(oaId, request.followerIds, request.tagName);
    return ApiResponseDto.success(result, 'Xóa tag của nhiều người theo dõi thành công');
  }
}
