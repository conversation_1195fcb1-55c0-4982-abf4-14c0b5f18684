#!/bin/bash

# Script test đơn giản cho Template Email API
# Sử dụng: ./test-simple.sh [BASE_URL] [JWT_TOKEN]

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN=${2:-"YOUR_JWT_TOKEN_HERE"}

API_ENDPOINT="$BASE_URL/api/v1/marketing/template-emails"

echo "🚀 Testing Template Email API - Simple Test"
echo "📍 Endpoint: $API_ENDPOINT"
echo "🔑 Token: ${JWT_TOKEN:0:20}..."
echo ""

# Test với request giống như frontend đang gửi
echo "📝 Test: Sử dụng field 'content' như frontend hiện tại"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Simple",
    "subject": "Test Subject Simple",
    "content": "<h1>Hello World!</h1><p>This is a test template</p>",
    "previewText": "Test preview",
    "tags": ["test"],
    "textContent": "Hello World! This is a test template",
    "type": "PROMOTIONAL",
    "variables": []
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n"
echo "✅ Test completed!"
echo ""
echo "📋 Expected result: 201 (Success)"
