#!/bin/bash

# Script test API Template Email
# Sử dụng: ./test-api.sh [BASE_URL] [JWT_TOKEN]

BASE_URL=${1:-"http://localhost:3000"}
JWT_TOKEN=${2:-"YOUR_JWT_TOKEN_HERE"}

API_ENDPOINT="$BASE_URL/api/v1/marketing/template-emails"

echo "🚀 Testing Template Email API"
echo "📍 Endpoint: $API_ENDPOINT"
echo "🔑 Token: ${JWT_TOKEN:0:20}..."
echo ""

# Test 1: Template cơ bản
echo "📝 Test 1: Tạo template cơ bản"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Basic",
    "subject": "Test Subject",
    "htmlContent": "<h1>Hello {user_name}!</h1><p>Welcome to {company_name}</p>"
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 2: Template với biến phức tạp
echo "📝 Test 2: Tạo template với biến phức tạp"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Advanced",
    "subject": "Welcome {user_name}!",
    "htmlContent": "<div><h2>Hello {user_name}!</h2><p>Your code: {verification_code}</p><img src=\"{banner_image}\" /></div>",
    "textContent": "Hello {user_name}! Your verification code is: {verification_code}",
    "type": "WELCOME",
    "previewText": "Welcome to our platform",
    "tags": ["welcome", "verification", "test"],
    "variables": [
      {
        "name": "user_name",
        "type": "TEXT",
        "defaultValue": "User",
        "required": true,
        "description": "Name of the user"
      },
      {
        "name": "verification_code",
        "type": "TEXT",
        "required": true,
        "description": "Verification code for account"
      },
      {
        "name": "banner_image",
        "type": "IMAGE",
        "defaultValue": "https://example.com/banner.jpg",
        "required": false,
        "description": "Welcome banner image"
      }
    ]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 3: Template với lỗi (duplicate name)
echo "📝 Test 3: Test duplicate name (should fail)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Template Basic",
    "subject": "Another Subject",
    "htmlContent": "<p>Different content</p>"
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 4: Template với validation error
echo "📝 Test 4: Test validation error (empty name)"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "subject": "Test Subject",
    "htmlContent": "<p>Test content</p>"
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n" && sleep 2

# Test 5: Template với invalid variable name
echo "📝 Test 5: Test invalid variable name"
curl -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Invalid Variable",
    "subject": "Test Subject",
    "htmlContent": "<p>Hello {123invalid}!</p>",
    "variables": [
      {
        "name": "123invalid",
        "type": "TEXT"
      }
    ]
  }' \
  -w "\n📊 Status: %{http_code}\n" \
  -s

echo -e "\n"
echo "✅ Test completed!"
echo ""
echo "📋 Expected results:"
echo "  - Test 1: 201 (Success)"
echo "  - Test 2: 201 (Success)"
echo "  - Test 3: 409 (Conflict - duplicate name)"
echo "  - Test 4: 400 (Bad Request - validation error)"
echo "  - Test 5: 400 (Bad Request - invalid variable name)"
