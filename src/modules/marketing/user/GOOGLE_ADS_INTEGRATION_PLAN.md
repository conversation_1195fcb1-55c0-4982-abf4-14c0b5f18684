# Kế hoạch tí<PERSON> hợp Google Ads vào hệ thống Marketing

## 1. Tổng quan

Tài liệu này mô tả kế hoạch tích hợp Google Ads vào hệ thống Marketing hiện tại, cho phép người dùng tạo, quản lý và theo dõi hiệu suất của các chiến dịch quảng cáo Google Ads từ trong hệ thống.

## 2. Phân tích hiện trạng

### 2.1. Các API Google Ads hiện có

Hệ thống hiện đã có các service sau:

- **GoogleAdsService**: Service cơ bản để tương tác với Google Ads API
- **GoogleAdsCampaignService**: Quản lý chiến dịch quảng cáo
- **GoogleAdsKeywordService**: Quản lý từ khóa quảng cáo
- **GoogleAdsReportService**: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hiệu suất quảng cáo

### 2.2. Module Marketing hiện tại

Module Marketing hiện tại đã có các entity:

- **UserCampaign**: Chiến dịch marketing của người dùng
- **UserCampaignHistory**: Lịch sử chiến dịch marketing
- **UserAudience**: Khách hàng mục tiêu
- **UserSegment**: Phân khúc khách hàng

## 3. Các entity mới cần thêm

### 3.1. GoogleAdsAccount

```typescript
@Entity('google_ads_accounts')
export class GoogleAdsAccount {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  @Column({ name: 'customer_id', nullable: false, comment: 'Customer ID của tài khoản Google Ads' })
  customerId: string;

  @Column({ name: 'refresh_token', nullable: false, comment: 'Refresh token để truy cập Google Ads API' })
  refreshToken: string;

  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên tài khoản' })
  name: string;

  @Column({ name: 'status', length: 20, nullable: false, default: 'active', comment: 'Trạng thái tài khoản' })
  status: string;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

### 3.2. GoogleAdsCampaign

```typescript
@Entity('google_ads_campaigns')
export class GoogleAdsCampaign {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  @Column({ name: 'account_id', nullable: false, comment: 'ID của tài khoản Google Ads' })
  accountId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID của chiến dịch trên Google Ads' })
  campaignId: string;

  @Column({ name: 'name', length: 255, nullable: false, comment: 'Tên chiến dịch' })
  name: string;

  @Column({ name: 'status', length: 20, nullable: false, comment: 'Trạng thái chiến dịch' })
  status: string;

  @Column({ name: 'type', length: 50, nullable: false, comment: 'Loại chiến dịch (SEARCH, DISPLAY, ...)' })
  type: string;

  @Column({ name: 'budget', type: 'bigint', nullable: false, comment: 'Ngân sách hàng ngày (micro amount)' })
  budget: number;

  @Column({ name: 'start_date', length: 10, nullable: true, comment: 'Ngày bắt đầu (YYYY-MM-DD)' })
  startDate: string;

  @Column({ name: 'end_date', length: 10, nullable: true, comment: 'Ngày kết thúc (YYYY-MM-DD)' })
  endDate: string;

  @Column({ name: 'user_campaign_id', type: 'bigint', nullable: true, comment: 'ID của chiến dịch marketing' })
  userCampaignId: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

### 3.3. GoogleAdsAdGroup

```typescript
@Entity('google_ads_ad_groups')
export class GoogleAdsAdGroup {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID của chiến dịch trong hệ thống' })
  campaignId: number;

  @Column({ name: 'ad_group_id', nullable: false, comment: 'ID của nhóm quảng cáo trên Google Ads' })
  adGroupId: string;

  @Column({ name: 'name', length: 255, nullable: false, comment: 'Tên nhóm quảng cáo' })
  name: string;

  @Column({ name: 'status', length: 20, nullable: false, comment: 'Trạng thái nhóm quảng cáo' })
  status: string;

  @Column({ name: 'cpc_bid_micros', type: 'bigint', nullable: true, comment: 'CPC tối đa (micro amount)' })
  cpcBidMicros: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

### 3.4. GoogleAdsKeyword

```typescript
@Entity('google_ads_keywords')
export class GoogleAdsKeyword {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  @Column({ name: 'ad_group_id', nullable: false, comment: 'ID của nhóm quảng cáo trong hệ thống' })
  adGroupId: number;

  @Column({ name: 'keyword_id', nullable: false, comment: 'ID của từ khóa trên Google Ads' })
  keywordId: string;

  @Column({ name: 'text', length: 255, nullable: false, comment: 'Văn bản từ khóa' })
  text: string;

  @Column({ name: 'match_type', length: 20, nullable: false, comment: 'Loại đối sánh (EXACT, PHRASE, BROAD)' })
  matchType: string;

  @Column({ name: 'status', length: 20, nullable: false, comment: 'Trạng thái từ khóa' })
  status: string;

  @Column({ name: 'cpc_bid_micros', type: 'bigint', nullable: true, comment: 'CPC tối đa (micro amount)' })
  cpcBidMicros: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
```

### 3.5. GoogleAdsPerformance

```typescript
@Entity('google_ads_performance')
export class GoogleAdsPerformance {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })
  userId: number;

  @Column({ name: 'campaign_id', nullable: false, comment: 'ID của chiến dịch trong hệ thống' })
  campaignId: number;

  @Column({ name: 'date', length: 10, nullable: false, comment: 'Ngày của báo cáo (YYYY-MM-DD)' })
  date: string;

  @Column({ name: 'impressions', type: 'integer', nullable: false, default: 0, comment: 'Số lần hiển thị' })
  impressions: number;

  @Column({ name: 'clicks', type: 'integer', nullable: false, default: 0, comment: 'Số lần nhấp chuột' })
  clicks: number;

  @Column({ name: 'cost', type: 'bigint', nullable: false, default: 0, comment: 'Chi phí (micro amount)' })
  cost: number;

  @Column({ name: 'ctr', type: 'float', nullable: false, default: 0, comment: 'Tỷ lệ nhấp chuột (%)' })
  ctr: number;

  @Column({ name: 'average_cpc', type: 'bigint', nullable: false, default: 0, comment: 'CPC trung bình' })
  averageCpc: number;

  @Column({ name: 'conversions', type: 'float', nullable: false, default: 0, comment: 'Số lượt chuyển đổi' })
  conversions: number;

  @Column({ name: 'conversion_value', type: 'float', nullable: false, default: 0, comment: 'Giá trị chuyển đổi' })
  conversionValue: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;
}
```

## 4. Cập nhật entity hiện có

### 4.1. Cập nhật UserCampaign

Cần bổ sung thêm trường để lưu thông tin cấu hình Google Ads:

```typescript
@Column({ name: 'google_ads_config', type: 'jsonb', nullable: true, comment: 'Cấu hình Google Ads' })
googleAdsConfig: {
  accountId?: number;
  campaignType?: string;
  budget?: number;
  keywords?: string[];
  targetLocations?: string[];
  startDate?: string;
  endDate?: string;
};
```

## 5. Các Repository mới

### 5.1. GoogleAdsAccountRepository
### 5.2. GoogleAdsCampaignRepository
### 5.3. GoogleAdsAdGroupRepository
### 5.4. GoogleAdsKeywordRepository
### 5.5. GoogleAdsPerformanceRepository

## 6. Các Service mới

### 6.1. GoogleAdsIntegrationService

Service này sẽ quản lý việc tích hợp tài khoản Google Ads vào hệ thống:

- Xác thực và lưu trữ thông tin tài khoản Google Ads
- Đồng bộ dữ liệu giữa hệ thống và Google Ads
- Quản lý refresh token

### 6.2. GoogleAdsCampaignManagementService

Service này sẽ quản lý các chiến dịch Google Ads:

- Tạo chiến dịch mới
- Cập nhật chiến dịch
- Kích hoạt/tạm dừng chiến dịch
- Xóa chiến dịch
- Đồng bộ chiến dịch giữa hệ thống và Google Ads

### 6.3. GoogleAdsKeywordManagementService

Service này sẽ quản lý từ khóa quảng cáo:

- Thêm từ khóa vào nhóm quảng cáo
- Phân tích và đề xuất từ khóa
- Quản lý giá thầu từ khóa

### 6.4. GoogleAdsReportingService

Service này sẽ quản lý báo cáo hiệu suất:

- Lấy và lưu trữ báo cáo hiệu suất hàng ngày
- Tính toán các chỉ số hiệu suất
- Tạo báo cáo tổng hợp

### 6.5. GoogleAdsMarketingCampaignService

Service này sẽ tích hợp Google Ads vào chiến dịch marketing:

- Tạo chiến dịch Google Ads từ chiến dịch marketing
- Đồng bộ trạng thái giữa chiến dịch marketing và chiến dịch Google Ads
- Cập nhật kết quả chiến dịch marketing từ báo cáo Google Ads

## 7. Các Controller mới

### 7.1. GoogleAdsAccountController

- `POST /marketing/user/google-ads/accounts`: Thêm tài khoản Google Ads mới
- `GET /marketing/user/google-ads/accounts`: Lấy danh sách tài khoản Google Ads
- `GET /marketing/user/google-ads/accounts/:id`: Lấy thông tin tài khoản Google Ads
- `PUT /marketing/user/google-ads/accounts/:id`: Cập nhật tài khoản Google Ads
- `DELETE /marketing/user/google-ads/accounts/:id`: Xóa tài khoản Google Ads
- `POST /marketing/user/google-ads/accounts/auth-url`: Lấy URL xác thực Google Ads
- `POST /marketing/user/google-ads/accounts/auth-callback`: Xử lý callback xác thực Google Ads

### 7.2. GoogleAdsCampaignController

- `GET /marketing/user/google-ads/campaigns`: Lấy danh sách chiến dịch Google Ads
- `GET /marketing/user/google-ads/campaigns/:id`: Lấy thông tin chiến dịch Google Ads
- `POST /marketing/user/google-ads/campaigns`: Tạo chiến dịch Google Ads mới
- `PUT /marketing/user/google-ads/campaigns/:id`: Cập nhật chiến dịch Google Ads
- `PUT /marketing/user/google-ads/campaigns/:id/status`: Cập nhật trạng thái chiến dịch
- `PUT /marketing/user/google-ads/campaigns/:id/budget`: Cập nhật ngân sách chiến dịch
- `DELETE /marketing/user/google-ads/campaigns/:id`: Xóa chiến dịch Google Ads

### 7.3. GoogleAdsKeywordController

- `GET /marketing/user/google-ads/ad-groups/:adGroupId/keywords`: Lấy danh sách từ khóa
- `POST /marketing/user/google-ads/ad-groups/:adGroupId/keywords`: Thêm từ khóa mới
- `POST /marketing/user/google-ads/ad-groups/:adGroupId/keywords/batch`: Thêm nhiều từ khóa
- `POST /marketing/user/google-ads/keywords/analyze`: Phân tích từ khóa từ văn bản
- `POST /marketing/user/google-ads/keywords/suggest`: Đề xuất từ khóa liên quan

### 7.4. GoogleAdsReportController

- `GET /marketing/user/google-ads/campaigns/:id/performance`: Lấy báo cáo hiệu suất chiến dịch
- `GET /marketing/user/google-ads/campaigns/:id/performance/total`: Lấy tổng số liệu hiệu suất
- `GET /marketing/user/google-ads/campaigns/:id/performance/daily`: Lấy số liệu hiệu suất theo ngày
- `GET /marketing/user/google-ads/campaigns/:id/performance/weekly`: Lấy số liệu hiệu suất theo tuần
- `GET /marketing/user/google-ads/campaigns/:id/performance/monthly`: Lấy số liệu hiệu suất theo tháng

## 8. Cập nhật UserCampaignController

Cần bổ sung các endpoint để tích hợp Google Ads vào chiến dịch marketing:

- `POST /marketing/user/campaigns/:id/google-ads`: Tạo chiến dịch Google Ads từ chiến dịch marketing
- `GET /marketing/user/campaigns/:id/google-ads`: Lấy thông tin chiến dịch Google Ads của chiến dịch marketing
- `PUT /marketing/user/campaigns/:id/google-ads`: Cập nhật chiến dịch Google Ads của chiến dịch marketing

## 9. Luồng làm việc

### 9.1. Tích hợp tài khoản Google Ads

1. Người dùng chọn "Thêm tài khoản Google Ads"
2. Hệ thống tạo URL xác thực và chuyển hướng người dùng đến trang xác thực Google
3. Người dùng đăng nhập và cấp quyền truy cập
4. Google chuyển hướng người dùng trở lại hệ thống với authorization code
5. Hệ thống sử dụng authorization code để lấy refresh token
6. Hệ thống lưu trữ thông tin tài khoản Google Ads

### 9.2. Tạo chiến dịch Google Ads

1. Người dùng tạo chiến dịch marketing mới hoặc chọn chiến dịch hiện có
2. Người dùng chọn "Tạo chiến dịch Google Ads"
3. Người dùng nhập thông tin chiến dịch Google Ads (ngân sách, từ khóa, ...)
4. Hệ thống tạo chiến dịch Google Ads thông qua API
5. Hệ thống lưu trữ thông tin chiến dịch Google Ads và liên kết với chiến dịch marketing

### 9.3. Theo dõi hiệu suất chiến dịch

1. Hệ thống tự động lấy báo cáo hiệu suất hàng ngày từ Google Ads API
2. Hệ thống lưu trữ báo cáo hiệu suất vào cơ sở dữ liệu
3. Người dùng có thể xem báo cáo hiệu suất trong dashboard
4. Hệ thống tính toán và hiển thị các chỉ số hiệu suất quan trọng (CTR, CPC, ROI, ...)

## 10. Kế hoạch triển khai

### 10.1. Giai đoạn 1: Cơ sở hạ tầng

- Tạo các entity mới
- Tạo các migration
- Tạo các repository
- Tạo các service cơ bản

### 10.2. Giai đoạn 2: Tích hợp tài khoản

- Tạo GoogleAdsAccountController
- Triển khai luồng xác thực OAuth
- Triển khai quản lý tài khoản Google Ads

### 10.3. Giai đoạn 3: Quản lý chiến dịch

- Tạo GoogleAdsCampaignController
- Triển khai tạo và quản lý chiến dịch Google Ads
- Tích hợp với chiến dịch marketing

### 10.4. Giai đoạn 4: Quản lý từ khóa

- Tạo GoogleAdsKeywordController
- Triển khai phân tích và đề xuất từ khóa
- Triển khai quản lý từ khóa

### 10.5. Giai đoạn 5: Báo cáo hiệu suất

- Tạo GoogleAdsReportController
- Triển khai lấy và lưu trữ báo cáo hiệu suất
- Triển khai tính toán và hiển thị chỉ số hiệu suất

## 11. Yêu cầu kỹ thuật

### 11.1. Môi trường

- Node.js 14+
- NestJS 8+
- PostgreSQL 12+
- TypeORM 0.2+

### 11.2. Thư viện

- google-ads-api: Thư viện chính thức để tương tác với Google Ads API
- googleapis: Thư viện để xác thực OAuth

### 11.3. Cấu hình

Cần bổ sung các biến môi trường:

- `GOOGLE_ADS_CLIENT_ID`: Client ID từ Google Cloud Console
- `GOOGLE_ADS_CLIENT_SECRET`: Client Secret từ Google Cloud Console
- `GOOGLE_ADS_DEVELOPER_TOKEN`: Developer Token từ Google Ads
- `GOOGLE_ADS_REDIRECT_URI`: URI chuyển hướng sau khi xác thực

## 12. Đề xuất giao diện người dùng

### 12.1. Tích hợp tài khoản Google Ads

#### 12.1.1. Trang quản lý tài khoản Google Ads

![Trang quản lý tài khoản Google Ads](https://example.com/mockups/google-ads-accounts.png)

**Mô tả:**
- Hiển thị danh sách tài khoản Google Ads đã kết nối
- Mỗi tài khoản hiển thị: Tên tài khoản, Customer ID, Trạng thái, Ngày kết nối
- Nút "Kết nối tài khoản Google Ads" để thêm tài khoản mới
- Các hành động: Xem chi tiết, Ngắt kết nối

**Các thành phần:**
1. **Bảng danh sách tài khoản**:
   - Cột: Tên tài khoản, Customer ID, Trạng thái, Ngày kết nối, Hành động
   - Phân trang và tìm kiếm
   - Bộ lọc theo trạng thái

2. **Nút "Kết nối tài khoản Google Ads"**:
   - Khi nhấp vào, hiển thị modal hướng dẫn kết nối
   - Nút "Tiếp tục" để chuyển hướng đến trang xác thực Google

3. **Modal xác nhận ngắt kết nối**:
   - Cảnh báo về hậu quả của việc ngắt kết nối
   - Nút "Hủy" và "Xác nhận"

#### 12.1.2. Luồng kết nối tài khoản Google Ads

![Luồng kết nối tài khoản Google Ads](https://example.com/mockups/google-ads-connect-flow.png)

**Mô tả:**
- Quy trình từng bước để kết nối tài khoản Google Ads
- Hướng dẫn người dùng qua các bước xác thực OAuth

**Các bước:**
1. **Bước 1: Giới thiệu**
   - Giải thích về quyền truy cập cần thiết
   - Nút "Tiếp tục với Google"

2. **Bước 2: Xác thực Google**
   - Chuyển hướng đến trang đăng nhập Google
   - Người dùng đăng nhập và cấp quyền truy cập

3. **Bước 3: Chọn tài khoản Google Ads**
   - Hiển thị danh sách tài khoản Google Ads có sẵn
   - Người dùng chọn tài khoản muốn kết nối

4. **Bước 4: Xác nhận kết nối**
   - Hiển thị thông tin tài khoản đã chọn
   - Nút "Hoàn tất kết nối"

5. **Bước 5: Kết nối thành công**
   - Thông báo kết nối thành công
   - Nút "Quay lại danh sách tài khoản" và "Tạo chiến dịch mới"

### 12.2. Quản lý chiến dịch Google Ads

#### 12.2.1. Trang danh sách chiến dịch Google Ads

![Trang danh sách chiến dịch Google Ads](https://example.com/mockups/google-ads-campaigns.png)

**Mô tả:**
- Hiển thị danh sách chiến dịch Google Ads
- Mỗi chiến dịch hiển thị: Tên, Loại, Trạng thái, Ngân sách, Hiệu suất tóm tắt
- Nút "Tạo chiến dịch mới" để thêm chiến dịch

**Các thành phần:**
1. **Bộ lọc và tìm kiếm**:
   - Lọc theo tài khoản Google Ads
   - Lọc theo trạng thái chiến dịch
   - Lọc theo loại chiến dịch
   - Tìm kiếm theo tên

2. **Bảng danh sách chiến dịch**:
   - Cột: Tên chiến dịch, Loại, Trạng thái, Ngân sách, Impressions, Clicks, CTR, Chi phí, Hành động
   - Phân trang
   - Sắp xếp theo các cột

3. **Thẻ tóm tắt hiệu suất**:
   - Tổng số chiến dịch
   - Tổng chi phí
   - Tổng số clicks
   - CTR trung bình

4. **Nút "Tạo chiến dịch mới"**:
   - Khi nhấp vào, chuyển hướng đến trang tạo chiến dịch

#### 12.2.2. Trang tạo/chỉnh sửa chiến dịch Google Ads

![Trang tạo chiến dịch Google Ads](https://example.com/mockups/google-ads-campaign-create.png)

**Mô tả:**
- Form tạo/chỉnh sửa chiến dịch Google Ads
- Chia thành các bước để đơn giản hóa quy trình

**Các bước:**
1. **Bước 1: Thông tin cơ bản**
   - Tên chiến dịch
   - Chọn tài khoản Google Ads
   - Loại chiến dịch (Tìm kiếm, Hiển thị, Video)
   - Mục tiêu chiến dịch (Bán hàng, Leads, Website traffic, ...)

2. **Bước 2: Ngân sách và lịch trình**
   - Ngân sách hàng ngày
   - Ngày bắt đầu
   - Ngày kết thúc (tùy chọn)
   - Lịch trình hiển thị (giờ trong ngày, ngày trong tuần)

3. **Bước 3: Đối tượng mục tiêu**
   - Vị trí địa lý
   - Ngôn ngữ
   - Nhân khẩu học (tuổi, giới tính)
   - Sở thích và hành vi (nếu có)

4. **Bước 4: Từ khóa và quảng cáo**
   - Thêm từ khóa (với gợi ý từ khóa)
   - Tạo quảng cáo văn bản (tiêu đề, mô tả, URL)
   - Xem trước quảng cáo

5. **Bước 5: Xem lại và tạo**
   - Tóm tắt cấu hình chiến dịch
   - Ước tính hiệu suất
   - Nút "Tạo chiến dịch" hoặc "Cập nhật chiến dịch"

#### 12.2.3. Trang chi tiết chiến dịch Google Ads

![Trang chi tiết chiến dịch Google Ads](https://example.com/mockups/google-ads-campaign-detail.png)

**Mô tả:**
- Hiển thị thông tin chi tiết và hiệu suất của chiến dịch
- Cho phép quản lý nhóm quảng cáo và từ khóa

**Các thành phần:**
1. **Thông tin chiến dịch**:
   - Tên, Loại, Trạng thái, Ngân sách, Ngày bắt đầu/kết thúc
   - Nút "Chỉnh sửa", "Tạm dừng/Kích hoạt", "Xóa"

2. **Biểu đồ hiệu suất**:
   - Biểu đồ đường cho Impressions, Clicks, Chi phí theo thời gian
   - Bộ lọc khoảng thời gian (7 ngày, 30 ngày, 90 ngày, Tùy chỉnh)
   - Chuyển đổi giữa các chỉ số

3. **Thẻ tóm tắt hiệu suất**:
   - Impressions, Clicks, CTR, Chi phí, CPC trung bình, Conversions
   - So sánh với kỳ trước (% tăng/giảm)

4. **Danh sách nhóm quảng cáo**:
   - Bảng hiển thị các nhóm quảng cáo trong chiến dịch
   - Cột: Tên, Trạng thái, Impressions, Clicks, CTR, Chi phí, Hành động
   - Nút "Thêm nhóm quảng cáo mới"

5. **Tab quản lý**:
   - Tab "Tổng quan": Hiển thị tóm tắt hiệu suất
   - Tab "Nhóm quảng cáo": Quản lý nhóm quảng cáo
   - Tab "Từ khóa": Quản lý từ khóa
   - Tab "Quảng cáo": Quản lý quảng cáo
   - Tab "Cài đặt": Cài đặt chiến dịch

### 12.3. Quản lý từ khóa

#### 12.3.1. Trang quản lý từ khóa

![Trang quản lý từ khóa](https://example.com/mockups/google-ads-keywords.png)

**Mô tả:**
- Hiển thị và quản lý từ khóa trong nhóm quảng cáo
- Phân tích hiệu suất từ khóa

**Các thành phần:**
1. **Bộ lọc và tìm kiếm**:
   - Lọc theo nhóm quảng cáo
   - Lọc theo trạng thái từ khóa
   - Lọc theo loại đối sánh
   - Tìm kiếm theo văn bản từ khóa

2. **Bảng danh sách từ khóa**:
   - Cột: Từ khóa, Loại đối sánh, Trạng thái, Impressions, Clicks, CTR, Chi phí, CPC trung bình, Hành động
   - Phân trang
   - Sắp xếp theo các cột

3. **Công cụ thêm từ khóa**:
   - Form thêm từ khóa đơn lẻ
   - Tải lên danh sách từ khóa từ file
   - Công cụ phân tích và đề xuất từ khóa

4. **Công cụ phân tích từ khóa**:
   - Nhập văn bản để phân tích
   - Hiển thị danh sách từ khóa đề xuất
   - Chọn và thêm từ khóa vào nhóm quảng cáo

#### 12.3.2. Công cụ đề xuất từ khóa

![Công cụ đề xuất từ khóa](https://example.com/mockups/google-ads-keyword-suggestions.png)

**Mô tả:**
- Công cụ để phân tích và đề xuất từ khóa liên quan
- Hiển thị thông tin về khối lượng tìm kiếm và mức độ cạnh tranh

**Các thành phần:**
1. **Form nhập từ khóa gốc**:
   - Nhập từ khóa hoặc cụm từ gốc
   - Nút "Tạo đề xuất"

2. **Danh sách từ khóa đề xuất**:
   - Hiển thị từ khóa đề xuất
   - Thông tin về khối lượng tìm kiếm (nếu có)
   - Mức độ cạnh tranh (nếu có)
   - Checkbox để chọn từ khóa

3. **Bộ lọc đề xuất**:
   - Lọc theo loại đối sánh
   - Lọc theo khối lượng tìm kiếm
   - Lọc theo mức độ cạnh tranh

4. **Nút hành động**:
   - "Thêm từ khóa đã chọn" để thêm vào nhóm quảng cáo
   - "Xuất danh sách" để xuất ra file CSV

### 12.4. Báo cáo hiệu suất

#### 12.4.1. Dashboard hiệu suất Google Ads

![Dashboard hiệu suất Google Ads](https://example.com/mockups/google-ads-dashboard.png)

**Mô tả:**
- Tổng quan về hiệu suất của tất cả chiến dịch Google Ads
- Biểu đồ và chỉ số KPI chính

**Các thành phần:**
1. **Bộ lọc thời gian**:
   - Chọn khoảng thời gian (7 ngày, 30 ngày, 90 ngày, Tùy chỉnh)
   - So sánh với kỳ trước

2. **Thẻ KPI chính**:
   - Tổng chi phí
   - Tổng số clicks
   - CTR trung bình
   - CPC trung bình
   - Tổng số conversions
   - Giá trị conversion
   - ROI/ROAS

3. **Biểu đồ hiệu suất theo thời gian**:
   - Biểu đồ đường cho Impressions, Clicks, Chi phí theo ngày/tuần/tháng
   - Chuyển đổi giữa các chỉ số

4. **Biểu đồ phân phối chi phí**:
   - Biểu đồ tròn/cột hiển thị phân phối chi phí theo chiến dịch
   - Chuyển đổi giữa các chỉ số (Chi phí, Clicks, Conversions)

5. **Bảng xếp hạng chiến dịch**:
   - Xếp hạng chiến dịch theo hiệu suất
   - Cột: Tên chiến dịch, Impressions, Clicks, CTR, Chi phí, Conversions, CPA, ROAS

6. **Bảng xếp hạng từ khóa**:
   - Xếp hạng từ khóa theo hiệu suất
   - Cột: Từ khóa, Impressions, Clicks, CTR, Chi phí, Conversions, CPA

#### 12.4.2. Báo cáo chi tiết chiến dịch

![Báo cáo chi tiết chiến dịch](https://example.com/mockups/google-ads-campaign-report.png)

**Mô tả:**
- Báo cáo chi tiết về hiệu suất của một chiến dịch cụ thể
- Phân tích sâu về các chỉ số hiệu suất

**Các thành phần:**
1. **Thông tin chiến dịch**:
   - Tên, Loại, Trạng thái, Ngân sách, Ngày bắt đầu/kết thúc
   - Tóm tắt hiệu suất

2. **Biểu đồ hiệu suất theo thời gian**:
   - Biểu đồ đường cho Impressions, Clicks, Chi phí, Conversions theo ngày
   - Bộ lọc khoảng thời gian
   - Chuyển đổi giữa các chỉ số

3. **Phân tích hiệu suất theo thiết bị**:
   - Biểu đồ cột hiển thị hiệu suất theo thiết bị (Desktop, Mobile, Tablet)
   - So sánh các chỉ số (CTR, CPC, Conversion Rate)

4. **Phân tích hiệu suất theo vị trí địa lý**:
   - Bản đồ nhiệt hiển thị hiệu suất theo vị trí địa lý
   - Bảng xếp hạng vị trí địa lý

5. **Phân tích hiệu suất theo thời gian trong ngày**:
   - Biểu đồ nhiệt hiển thị hiệu suất theo giờ trong ngày và ngày trong tuần
   - Xác định thời điểm hiệu quả nhất

6. **Báo cáo từ khóa**:
   - Bảng xếp hạng từ khóa theo hiệu suất
   - Phân tích từ khóa theo loại đối sánh

7. **Nút xuất báo cáo**:
   - Xuất báo cáo dưới dạng PDF, Excel, CSV

### 12.5. Tích hợp với chiến dịch marketing

#### 12.5.1. Tab Google Ads trong trang tạo chiến dịch marketing

![Tab Google Ads trong trang tạo chiến dịch marketing](https://example.com/mockups/marketing-campaign-google-ads-tab.png)

**Mô tả:**
- Tab Google Ads trong form tạo/chỉnh sửa chiến dịch marketing
- Cho phép cấu hình chiến dịch Google Ads cùng với chiến dịch marketing

**Các thành phần:**
1. **Checkbox kích hoạt Google Ads**:
   - Tùy chọn để kích hoạt kênh Google Ads cho chiến dịch marketing

2. **Form cấu hình Google Ads**:
   - Chọn tài khoản Google Ads
   - Loại chiến dịch (Tìm kiếm, Hiển thị, Video)
   - Ngân sách hàng ngày
   - Ngày bắt đầu/kết thúc (mặc định lấy từ chiến dịch marketing)

3. **Cấu hình từ khóa**:
   - Nhập từ khóa hoặc sử dụng công cụ đề xuất từ khóa
   - Chọn loại đối sánh

4. **Cấu hình quảng cáo**:
   - Tiêu đề quảng cáo (có thể sử dụng tiêu đề chiến dịch marketing)
   - Mô tả quảng cáo (có thể sử dụng mô tả chiến dịch marketing)
   - URL đích

5. **Xem trước quảng cáo**:
   - Hiển thị xem trước quảng cáo dựa trên cấu hình

#### 12.5.2. Tab Google Ads trong trang chi tiết chiến dịch marketing

![Tab Google Ads trong trang chi tiết chiến dịch marketing](https://example.com/mockups/marketing-campaign-google-ads-detail.png)

**Mô tả:**
- Tab Google Ads trong trang chi tiết chiến dịch marketing
- Hiển thị thông tin và hiệu suất của chiến dịch Google Ads liên kết

**Các thành phần:**
1. **Thông tin chiến dịch Google Ads**:
   - Tên, Loại, Trạng thái, Ngân sách, Ngày bắt đầu/kết thúc
   - Nút "Chỉnh sửa", "Tạm dừng/Kích hoạt", "Xem chi tiết"

2. **Tóm tắt hiệu suất**:
   - Impressions, Clicks, CTR, Chi phí, Conversions
   - So sánh với kỳ trước

3. **Biểu đồ hiệu suất**:
   - Biểu đồ đường cho Impressions, Clicks, Chi phí theo thời gian
   - Bộ lọc khoảng thời gian

4. **Danh sách từ khóa hàng đầu**:
   - Hiển thị các từ khóa có hiệu suất tốt nhất
   - Cột: Từ khóa, Impressions, Clicks, CTR, Chi phí

5. **Nút "Xem báo cáo đầy đủ"**:
   - Chuyển hướng đến trang báo cáo chi tiết chiến dịch Google Ads

## 13. Kết luận

Việc tích hợp Google Ads vào hệ thống Marketing sẽ mang lại nhiều lợi ích cho người dùng:

- Quản lý tập trung các chiến dịch marketing trên nhiều kênh
- Theo dõi hiệu suất chiến dịch một cách toàn diện
- Tối ưu hóa ngân sách marketing dựa trên dữ liệu thực tế

Kế hoạch này cung cấp một lộ trình rõ ràng để triển khai tích hợp Google Ads, từ việc tạo cơ sở hạ tầng đến triển khai các tính năng cụ thể và giao diện người dùng.
