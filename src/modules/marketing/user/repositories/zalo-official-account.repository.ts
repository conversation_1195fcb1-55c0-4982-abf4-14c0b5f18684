import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloOfficialAccount } from '../entities/zalo-official-account.entity';

/**
 * Repository cho ZaloOfficialAccount
 */
@Injectable()
export class ZaloOfficialAccountRepository {
  constructor(
    @InjectRepository(ZaloOfficialAccount)
    private readonly repository: Repository<ZaloOfficialAccount>,
  ) {}

  /**
   * Tìm kiếm nhiều Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách Official Account
   */
  async find(options?: FindManyOptions<ZaloOfficialAccount>): Promise<ZaloOfficialAccount[]> {
    return this.repository.find(options);
  }

  /**
   * Tì<PERSON> kiếm một Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Official Account hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloOfficialAccount>): Promise<ZaloOfficialAccount | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm Official Account theo ID
   * @param id ID của Official Account
   * @returns Official Account hoặc null
   */
  async findById(id: number): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @returns Official Account hoặc null
   */
  async findByUserIdAndOaId(userId: number, oaId: string): Promise<ZaloOfficialAccount | null> {
    return this.repository.findOne({ where: { userId, oaId } });
  }

  /**
   * Tìm tất cả Official Account của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Official Account
   */
  async findByUserId(userId: number): Promise<ZaloOfficialAccount[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tạo mới Official Account
   * @param data Dữ liệu Official Account
   * @returns Official Account đã tạo
   */
  async create(data: Partial<ZaloOfficialAccount>): Promise<ZaloOfficialAccount> {
    const officialAccount = this.repository.create(data);
    return this.repository.save(officialAccount);
  }

  /**
   * Cập nhật Official Account
   * @param id ID của Official Account
   * @param data Dữ liệu cập nhật
   * @returns Official Account đã cập nhật
   */
  async update(id: number, data: Partial<ZaloOfficialAccount>): Promise<ZaloOfficialAccount | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Cập nhật Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @param data Dữ liệu cập nhật
   * @returns Official Account đã cập nhật
   */
  async updateByUserIdAndOaId(
    userId: number,
    oaId: string,
    data: Partial<ZaloOfficialAccount>,
  ): Promise<ZaloOfficialAccount | null> {
    await this.repository.update({ userId, oaId }, data);
    return this.findByUserIdAndOaId(userId, oaId);
  }

  /**
   * Xóa Official Account
   * @param id ID của Official Account
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Xóa Official Account theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account trên Zalo
   * @returns true nếu xóa thành công
   */
  async deleteByUserIdAndOaId(userId: number, oaId: string): Promise<boolean> {
    const result = await this.repository.delete({ userId, oaId });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng Official Account
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng Official Account
   */
  async count(options?: FindManyOptions<ZaloOfficialAccount>): Promise<number> {
    return this.repository.count(options);
  }
}
