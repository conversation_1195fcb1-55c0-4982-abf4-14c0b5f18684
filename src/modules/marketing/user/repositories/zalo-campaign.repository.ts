import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ZaloCampaign } from '../entities';
import { ZaloCampaignStatus } from '../dto/zalo';

/**
 * Repository cho chiến dịch Zalo
 */
@Injectable()
export class ZaloCampaignRepository {
  constructor(
    @InjectRepository(ZaloCampaign)
    private readonly repository: Repository<ZaloCampaign>,
  ) {}

  /**
   * Tìm chiến dịch theo ID
   * @param id ID của chiến dịch
   * @returns Chiến dịch
   */
  async findById(id: number): Promise<ZaloCampaign | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm chiến dịch theo ID và ID người dùng
   * @param id ID của chiến dịch
   * @param userId ID của người dùng
   * @returns Chiến dịch
   */
  async findByIdAndUserId(id: number, userId: number): Promise<ZaloCampaign | null> {
    return this.repository.findOne({ where: { id, userId } });
  }

  /**
   * Tìm chiến dịch theo ID, ID người dùng và ID Official Account
   * @param id ID của chiến dịch
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Chiến dịch
   */
  async findByIdAndUserIdAndOaId(id: number, userId: number, oaId: string): Promise<ZaloCampaign | null> {
    return this.repository.findOne({ where: { id, userId, oaId } });
  }

  /**
   * Tìm danh sách chiến dịch theo ID người dùng và ID Official Account
   * @param userId ID của người dùng
   * @param oaId ID của Official Account
   * @returns Danh sách chiến dịch
   */
  async findByUserIdAndOaId(userId: number, oaId: string): Promise<ZaloCampaign[]> {
    return this.repository.find({ where: { userId, oaId } });
  }

  /**
   * Tìm danh sách chiến dịch đã lên lịch và đến thời gian chạy
   * @param currentTime Thời gian hiện tại
   * @returns Danh sách chiến dịch
   */
  async findScheduledCampaigns(currentTime: number): Promise<ZaloCampaign[]> {
    return this.repository.createQueryBuilder('campaign')
      .where('campaign.status = :status', { status: ZaloCampaignStatus.SCHEDULED })
      .andWhere('campaign.scheduledAt <= :currentTime', { currentTime })
      .getMany();
  }

  /**
   * Tìm danh sách chiến dịch với phân trang
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách chiến dịch và tổng số chiến dịch
   */
  async findWithPagination(options: any): Promise<[ZaloCampaign[], number]> {
    const { where, skip, take, order } = options;
    return this.repository.findAndCount({
      where,
      skip,
      take,
      order,
    });
  }

  /**
   * Tạo chiến dịch mới
   * @param data Dữ liệu chiến dịch
   * @returns Chiến dịch đã tạo
   */
  async create(data: Partial<ZaloCampaign>): Promise<ZaloCampaign> {
    const campaign = this.repository.create(data);
    return this.repository.save(campaign);
  }

  /**
   * Cập nhật chiến dịch
   * @param id ID của chiến dịch
   * @param data Dữ liệu cập nhật
   * @returns Chiến dịch đã cập nhật
   */
  async update(id: number, data: Partial<ZaloCampaign>): Promise<ZaloCampaign | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa chiến dịch
   * @param id ID của chiến dịch
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Cập nhật trạng thái chiến dịch
   * @param id ID của chiến dịch
   * @param status Trạng thái mới
   * @returns Chiến dịch đã cập nhật
   */
  async updateStatus(id: number, status: ZaloCampaignStatus): Promise<ZaloCampaign | null> {
    await this.repository.update(id, { status });
    return this.findById(id);
  }

  /**
   * Cập nhật số liệu thống kê chiến dịch
   * @param id ID của chiến dịch
   * @param stats Số liệu thống kê
   * @returns Chiến dịch đã cập nhật
   */
  async updateStats(id: number, stats: { totalRecipients?: number; successCount?: number; failureCount?: number }): Promise<ZaloCampaign | null> {
    await this.repository.update(id, stats);
    return this.findById(id);
  }
}
