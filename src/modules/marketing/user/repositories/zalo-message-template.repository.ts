import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { ZaloZnsTemplate } from '../entities/zalo-zns-template.entity';

/**
 * Repository cho ZaloMessageTemplate
 * Sử dụng ZaloZnsTemplate entity vì chức năng tương tự
 */
@Injectable()
export class ZaloMessageTemplateRepository {
  constructor(
    @InjectRepository(ZaloZnsTemplate)
    private readonly repository: Repository<ZaloZnsTemplate>,
  ) {}

  /**
   * Tìm kiếm nhiều template
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách template
   */
  async find(options?: FindManyOptions<ZaloZnsTemplate>): Promise<ZaloZnsTemplate[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một template
   * @param options Tùy chọn tìm kiếm
   * @returns Template hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloZnsTemplate>): Promise<ZaloZnsTemplate | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tạo mới template
   * @param data Dữ liệu template
   * @returns Template đã tạo
   */
  async create(data: Partial<ZaloZnsTemplate>): Promise<ZaloZnsTemplate> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template
   * @param id ID của template
   * @param data Dữ liệu cập nhật
   * @returns Template đã cập nhật
   */
  async update(id: number, data: Partial<ZaloZnsTemplate>): Promise<ZaloZnsTemplate | null> {
    await this.repository.update(id, data);
    return this.findOne({ where: { id } });
  }

  /**
   * Xóa template
   * @param id ID của template
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng template
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng template
   */
  async count(options?: FindManyOptions<ZaloZnsTemplate>): Promise<number> {
    return this.repository.count(options);
  }
}
