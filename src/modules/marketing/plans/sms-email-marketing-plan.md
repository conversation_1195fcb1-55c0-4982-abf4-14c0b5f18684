# Kế hoạch phát triển tính năng SMS và Email Marketing

## 1. Tổng quan

Tài liệu này trình bày kế hoạch phát triển các tính năng SMS và Email Marketing cho module Marketing của hệ thống. Dựa trên cấu trúc hiện tại, kế hoạch này đề xuất các tính năng mới, cấu trúc dữ liệu và lộ trình triển khai.

## 2. Hiện trạng hệ thống

### 2.1. <PERSON><PERSON>u trúc hiện có

Module Marketing hiện đã có các thành phần cơ bản:

- **Template Email**: Quản lý mẫu email với tiêu đề, nội dung, placeholders
- **Template SMS**: <PERSON>uản lý mẫu SMS với nội dung, placeholders
- **Campaign**: <PERSON>u<PERSON>n lý chiến dịch marketing trên nhiều nền tảng
- **Audience**: <PERSON><PERSON><PERSON>n lý đối tượng khách hàng với thông tin liên hệ
- **Segment và Tag**: Phân loại và gắn nhãn đối tượng

### 2.2. Hạn chế hiện tại

- Chưa có tính năng gửi email hàng loạt và theo dõi hiệu quả
- Chưa có tính năng gửi SMS hàng loạt và theo dõi phản hồi
- Thiếu công cụ phân tích và báo cáo chi tiết
- Chưa có hệ thống tự động hóa marketing
- Chưa hỗ trợ A/B testing và tối ưu hóa nội dung

## 3. Đề xuất tính năng

### 3.1. Tính năng Email Marketing

#### 3.1.1. Quản lý chiến dịch email
- Tạo chiến dịch email với nhiều đối tượng
- Lên lịch gửi email tự động
- Theo dõi trạng thái gửi và tương tác
- Chiến dịch email tự động theo sự kiện

#### 3.1.2. Email A/B Testing
- Tạo nhiều phiên bản email (tiêu đề, nội dung)
- Gửi thử nghiệm cho một phần nhỏ đối tượng
- Tự động chọn phiên bản hiệu quả nhất

#### 3.1.3. Trình soạn thảo email trực quan
- Kéo thả các thành phần (drag-and-drop)
- Thêm hình ảnh, nút bấm, sản phẩm
- Xem trước trên nhiều thiết bị

#### 3.1.4. Phân tích hiệu quả email
- Tỷ lệ mở email (open rate)
- Tỷ lệ click (click-through rate)
- Tỷ lệ hủy đăng ký (unsubscribe rate)
- Tỷ lệ chuyển đổi (conversion rate)

#### 3.1.5. Tối ưu hóa thời gian gửi
- Phân tích thời gian mở email tối ưu
- Tự động gửi email vào thời điểm phù hợp

### 3.2. Tính năng SMS Marketing

#### 3.2.1. Quản lý chiến dịch SMS
- Tạo chiến dịch SMS với nhiều đối tượng
- Lên lịch gửi SMS tự động
- Theo dõi trạng thái gửi và tỷ lệ phản hồi
- Chiến dịch SMS tự động theo sự kiện

#### 3.2.2. SMS theo vị trí địa lý
- Gửi SMS cho khách hàng trong khu vực cụ thể
- Thông báo khuyến mãi tại cửa hàng gần nhất

#### 3.2.3. SMS hai chiều
- Cho phép khách hàng phản hồi SMS
- Tự động xử lý phản hồi theo từ khóa

#### 3.2.4. Tối ưu hóa nội dung SMS
- Kiểm tra độ dài SMS (160 ký tự)
- Tự động rút gọn URL
- Đề xuất từ khóa hiệu quả

### 3.3. Tính năng chung

#### 3.3.1. Quản lý đối tượng nâng cao
- Phân đoạn đối tượng thông minh
- Quản lý danh sách đen (blacklist)
- Nhập/Xuất đối tượng từ file

#### 3.3.2. Tự động hóa marketing
- Quy trình marketing tự động (Marketing Automation)
- Trigger dựa trên sự kiện
- Điều kiện phân nhánh

#### 3.3.3. Báo cáo và phân tích
- Dashboard tổng quan
- Báo cáo chi tiết theo thời gian
- Phân tích ROI

## 4. Cấu trúc dữ liệu

### 4.1. Entity cho Email Marketing

#### 4.1.1. AdminEmailCampaign
```typescript
@Entity('admin_email_campaigns')
export class AdminEmailCampaign {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'name', length: 255, nullable: false })
  name: string;

  @Column({ name: 'subject', length: 255, nullable: false })
  subject: string;

  @Column({ name: 'template_id', nullable: true })
  templateId: number;

  @Column({ name: 'segment_id', nullable: true })
  segmentId: number;

  @Column({ name: 'sender_name', length: 100, nullable: true })
  senderName: string;

  @Column({ name: 'sender_email', length: 255, nullable: true })
  senderEmail: string;

  @Column({ name: 'reply_to', length: 255, nullable: true })
  replyTo: string;

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt: number;

  @Column({ name: 'status', length: 20, nullable: false, default: 'draft' })
  status: string;

  @Column({ name: 'is_ab_test', type: 'boolean', default: false })
  isAbTest: boolean;

  @Column({ name: 'ab_test_config', type: 'jsonb', nullable: true })
  abTestConfig: any;

  @Column({ name: 'employee_id', nullable: true })
  employeeId: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
```

#### 4.1.2. AdminEmailCampaignStats
```typescript
@Entity('admin_email_campaign_stats')
export class AdminEmailCampaignStats {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'campaign_id', nullable: false })
  campaignId: number;

  @Column({ name: 'sent_count', type: 'int', default: 0 })
  sentCount: number;

  @Column({ name: 'delivered_count', type: 'int', default: 0 })
  deliveredCount: number;

  @Column({ name: 'open_count', type: 'int', default: 0 })
  openCount: number;

  @Column({ name: 'click_count', type: 'int', default: 0 })
  clickCount: number;

  @Column({ name: 'bounce_count', type: 'int', default: 0 })
  bounceCount: number;

  @Column({ name: 'unsubscribe_count', type: 'int', default: 0 })
  unsubscribeCount: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
```

### 4.2. Entity cho SMS Marketing

#### 4.2.1. AdminSmsCampaign
```typescript
@Entity('admin_sms_campaigns')
export class AdminSmsCampaign {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'name', length: 255, nullable: false })
  name: string;

  @Column({ name: 'template_id', nullable: true })
  templateId: number;

  @Column({ name: 'segment_id', nullable: true })
  segmentId: number;

  @Column({ name: 'sender_id', length: 20, nullable: true })
  senderId: string;

  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })
  scheduledAt: number;

  @Column({ name: 'status', length: 20, nullable: false, default: 'draft' })
  status: string;

  @Column({ name: 'employee_id', nullable: true })
  employeeId: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
```

#### 4.2.2. AdminSmsReply
```typescript
@Entity('admin_sms_replies')
export class AdminSmsReply {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'campaign_id', nullable: true })
  campaignId: number;

  @Column({ name: 'audience_id', nullable: false })
  audienceId: number;

  @Column({ name: 'phone', length: 20, nullable: false })
  phone: string;

  @Column({ name: 'message', type: 'text', nullable: false })
  message: string;

  @Column({ name: 'received_at', type: 'bigint', nullable: false })
  receivedAt: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;
}
```

### 4.3. Entity cho Tự động hóa Marketing

#### 4.3.1. AdminAutomationWorkflow
```typescript
@Entity('admin_automation_workflows')
export class AdminAutomationWorkflow {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'name', length: 255, nullable: false })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'trigger_type', length: 50, nullable: false })
  triggerType: string;

  @Column({ name: 'trigger_config', type: 'jsonb', nullable: true })
  triggerConfig: any;

  @Column({ name: 'workflow_data', type: 'jsonb', nullable: false })
  workflowData: any;

  @Column({ name: 'status', length: 20, nullable: false, default: 'inactive' })
  status: string;

  @Column({ name: 'employee_id', nullable: true })
  employeeId: number;

  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
```

## 5. API cần phát triển

### 5.1. API cho Email Marketing

#### 5.1.1. API Chiến dịch Email
```
POST /admin/email-campaigns - Tạo chiến dịch email
GET /admin/email-campaigns - Lấy danh sách chiến dịch
GET /admin/email-campaigns/:id - Lấy chi tiết chiến dịch
PUT /admin/email-campaigns/:id - Cập nhật chiến dịch
DELETE /admin/email-campaigns/:id - Xóa chiến dịch
POST /admin/email-campaigns/:id/send - Gửi chiến dịch
POST /admin/email-campaigns/:id/test - Gửi email test
GET /admin/email-campaigns/:id/stats - Lấy thống kê chiến dịch
```

#### 5.1.2. API A/B Testing
```
POST /admin/email-campaigns/:id/variants - Tạo phiên bản A/B
GET /admin/email-campaigns/:id/variants - Lấy các phiên bản A/B
PUT /admin/email-campaigns/:id/variants/:variantId - Cập nhật phiên bản
DELETE /admin/email-campaigns/:id/variants/:variantId - Xóa phiên bản
POST /admin/email-campaigns/:id/select-winner - Chọn phiên bản chiến thắng
```

#### 5.1.3. API Trình soạn thảo Email
```
POST /admin/email-editor/save-design - Lưu thiết kế email
GET /admin/email-editor/:id/design - Lấy thiết kế email
POST /admin/email-editor/preview - Xem trước email
POST /admin/email-editor/export-html - Xuất HTML
```

### 5.2. API cho SMS Marketing

#### 5.2.1. API Chiến dịch SMS
```
POST /admin/sms-campaigns - Tạo chiến dịch SMS
GET /admin/sms-campaigns - Lấy danh sách chiến dịch
GET /admin/sms-campaigns/:id - Lấy chi tiết chiến dịch
PUT /admin/sms-campaigns/:id - Cập nhật chiến dịch
DELETE /admin/sms-campaigns/:id - Xóa chiến dịch
POST /admin/sms-campaigns/:id/send - Gửi chiến dịch
POST /admin/sms-campaigns/:id/test - Gửi SMS test
GET /admin/sms-campaigns/:id/stats - Lấy thống kê chiến dịch
```

#### 5.2.2. API SMS hai chiều
```
GET /admin/sms/replies - Lấy danh sách phản hồi SMS
POST /admin/sms/reply-rules - Tạo quy tắc phản hồi tự động
GET /admin/sms/reply-rules - Lấy danh sách quy tắc
PUT /admin/sms/reply-rules/:id - Cập nhật quy tắc
DELETE /admin/sms/reply-rules/:id - Xóa quy tắc
```

### 5.3. API cho Quản lý đối tượng

#### 5.3.1. API Phân đoạn
```
POST /admin/segments - Tạo phân đoạn
GET /admin/segments - Lấy danh sách phân đoạn
GET /admin/segments/:id - Lấy chi tiết phân đoạn
PUT /admin/segments/:id - Cập nhật phân đoạn
DELETE /admin/segments/:id - Xóa phân đoạn
GET /admin/segments/:id/audience - Lấy danh sách đối tượng trong phân đoạn
POST /admin/segments/:id/test - Kiểm tra số lượng đối tượng phù hợp
```

#### 5.3.2. API Import/Export
```
POST /admin/audience/import - Import đối tượng từ file
GET /admin/audience/export - Export đối tượng ra file
GET /admin/audience/import-template - Tải template import
```

### 5.4. API cho Tự động hóa Marketing

#### 5.4.1. API Quy trình tự động
```
POST /admin/automation/workflows - Tạo quy trình tự động
GET /admin/automation/workflows - Lấy danh sách quy trình
GET /admin/automation/workflows/:id - Lấy chi tiết quy trình
PUT /admin/automation/workflows/:id - Cập nhật quy trình
DELETE /admin/automation/workflows/:id - Xóa quy trình
POST /admin/automation/workflows/:id/activate - Kích hoạt quy trình
POST /admin/automation/workflows/:id/deactivate - Tạm dừng quy trình
GET /admin/automation/workflows/:id/stats - Lấy thống kê quy trình
```

## 6. Lộ trình triển khai

### 6.1. Giai đoạn 1: Nền tảng cơ bản (1-2 tháng)

#### Tuần 1-2: Thiết kế cơ sở dữ liệu và cấu trúc API
- Tạo các entity mới cho Email và SMS Campaign
- Thiết kế các DTO và controller cơ bản
- Cập nhật module và dependency injection

#### Tuần 3-4: Phát triển quản lý chiến dịch email cơ bản
- Tạo, chỉnh sửa, xóa chiến dịch email
- Lên lịch gửi email
- Tích hợp với dịch vụ gửi email (SMTP/API)

#### Tuần 5-6: Phát triển quản lý chiến dịch SMS cơ bản
- Tạo, chỉnh sửa, xóa chiến dịch SMS
- Lên lịch gửi SMS
- Tích hợp với dịch vụ gửi SMS

#### Tuần 7-8: Nâng cao quản lý đối tượng và phân đoạn
- Phát triển tính năng phân đoạn đối tượng
- Quản lý danh sách đen
- Import/Export đối tượng

### 6.2. Giai đoạn 2: Tính năng nâng cao (2-3 tháng)

#### Tuần 9-10: Trình soạn thảo email trực quan
- Phát triển hoặc tích hợp trình soạn thảo email
- Lưu trữ và quản lý thiết kế email
- Xem trước email trên nhiều thiết bị

#### Tuần 11-12: A/B Testing cho email
- Tạo và quản lý các phiên bản A/B
- Phân tích hiệu quả của các phiên bản
- Tự động chọn phiên bản chiến thắng

#### Tuần 13-14: SMS hai chiều
- Nhận và lưu trữ phản hồi SMS
- Tạo quy tắc phản hồi tự động
- Phân tích nội dung phản hồi

#### Tuần 15-18: Phân tích hiệu quả nâng cao
- Theo dõi tỷ lệ mở email, click
- Theo dõi tỷ lệ phản hồi SMS
- Tạo báo cáo và biểu đồ

### 6.3. Giai đoạn 3: Tự động hóa và tích hợp (3-4 tháng)

#### Tuần 19-22: Quy trình marketing tự động
- Thiết kế và phát triển engine quy trình
- Tạo các trigger dựa trên sự kiện
- Phát triển điều kiện phân nhánh

#### Tuần 23-26: Tích hợp với CRM và phễu bán hàng
- Tích hợp với module User
- Tích hợp với module Order
- Tích hợp với module Product

#### Tuần 27-30: Tối ưu hóa thời gian gửi
- Phân tích thời gian tương tác tối ưu
- Tự động điều chỉnh thời gian gửi
- Machine learning đơn giản để dự đoán

#### Tuần 31-34: Báo cáo ROI và phân tích kinh doanh
- Tính toán chi phí chiến dịch
- Theo dõi doanh thu từ chiến dịch
- Báo cáo ROI và KPI

## 7. Yêu cầu tích hợp

### 7.1. Tích hợp với dịch vụ bên ngoài
- Dịch vụ gửi email (SMTP, SendGrid, Amazon SES)
- Dịch vụ gửi SMS (Twilio, Nexmo, Infobip)
- Dịch vụ rút gọn URL (Bitly, TinyURL)

### 7.2. Tích hợp với các module khác
- Module User: Lấy thông tin người dùng
- Module Order: Theo dõi đơn hàng và chuyển đổi
- Module Product: Hiển thị sản phẩm trong email
- Module Auth: Xác thực và phân quyền

## 8. Rủi ro và giải pháp

### 8.1. Rủi ro kỹ thuật
- **Hiệu suất gửi hàng loạt**: Sử dụng hàng đợi và xử lý bất đồng bộ
- **Tỷ lệ gửi thành công thấp**: Thiết lập cơ chế thử lại và báo cáo lỗi
- **Khả năng mở rộng**: Thiết kế kiến trúc có thể mở rộng theo chiều ngang

### 8.2. Rủi ro kinh doanh
- **Tỷ lệ spam cao**: Tuân thủ quy định chống spam và best practices
- **Hiệu quả marketing thấp**: A/B testing và tối ưu hóa liên tục
- **Chi phí SMS cao**: Phân đoạn đối tượng chính xác để giảm chi phí

## 9. Kết luận

Kế hoạch phát triển tính năng SMS và Email Marketing này sẽ giúp nâng cao khả năng tiếp cận khách hàng, tăng tỷ lệ chuyển đổi và doanh thu. Việc triển khai theo lộ trình từng giai đoạn sẽ đảm bảo chất lượng và tính ổn định của hệ thống.

Các tính năng được đề xuất đều dựa trên cấu trúc hiện có của module marketing, tận dụng các entity và API đã có, đồng thời mở rộng thêm các entity và API mới để hỗ trợ các tính năng nâng cao. 