import { Injectable, Logger } from '@nestjs/common';
import { CurrencyResponseDto } from '../dto/currency-response.dto';
import * as currencyCodes from 'currency-codes';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Service xử lý các chức năng chung
 */
@Injectable()
export class CommonService {
  private readonly logger = new Logger(CommonService.name);

  /**
   * Lấy danh sách tiền tệ
   * @returns Danh sách tiền tệ
   */
  async getCurrencies(): Promise<CurrencyResponseDto[]> {
    try {
      // Lấy danh sách tiền tệ từ thư viện currency-codes
      const currencies = currencyCodes.data;

      // Chuyển đổi thành DTO
      return currencies.map(currency => ({
        code: currency.code,
        name: currency.currency,
        number: currency.number,
        symbol: null // Thư viện currency-codes không có trường symbol
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tiền tệ: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy danh sách tiền tệ: ${error.message}`
      );
    }
  }
}
