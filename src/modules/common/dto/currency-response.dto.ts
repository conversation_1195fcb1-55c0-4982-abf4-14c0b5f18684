import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho thông tin tiền tệ
 */
export class CurrencyResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Mã tiền tệ',
    example: 'VND',
  })
  code: string;

  @Expose()
  @ApiProperty({
    description: 'Tên tiền tệ',
    example: 'Vietnamese Dong',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Số tiền tệ',
    example: '704',
  })
  number: string;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON> hiệu tiền tệ',
    example: '₫',
    nullable: true,
  })
  symbol?: string | null;
}
