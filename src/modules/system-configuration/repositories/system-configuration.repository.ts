import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemConfiguration } from '../entities/system-configuration.entity';
import { UpdateSystemConfigurationDto } from '../dto';

@Injectable()
export class SystemConfigurationRepository {
  constructor(
    @InjectRepository(SystemConfiguration)
    private readonly repository: Repository<SystemConfiguration>,
  ) {}

  /**
   * Tìm cấu hình theo ID
   * @param id ID của cấu hình
   * @returns Thông tin cấu hình
   */
  async findById(id: number): Promise<SystemConfiguration | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm cấu hình đang active
   * @returns Thông tin cấu hình đang active
   */
  async findActive(): Promise<SystemConfiguration | null> {
    return this.repository.findOne({ where: { active: true } });
  }

  /**
   * <PERSON><PERSON>y tất cả cấu hình
   * @returns Danh sách cấu hình
   */
  async findAll(): Promise<SystemConfiguration[]> {
    return this.repository.find();
  }

  /**
   * Cập nhật cấu hình
   * @param id ID của cấu hình
   * @param updateDto Thông tin cần cập nhật
   * @returns Cấu hình đã cập nhật
   */
  async update(id: number, updateDto: UpdateSystemConfigurationDto): Promise<SystemConfiguration | null> {
    const configuration = await this.findById(id);
    
    if (!configuration) {
      return null;
    }

    // Cập nhật thông tin
    Object.assign(configuration, {
      ...updateDto,
      updatedAt: Math.floor(Date.now() / 1000), // Unix timestamp in seconds
    });

    return this.repository.save(configuration);
  }

  /**
   * Tạo cấu hình mới
   * @param configuration Thông tin cấu hình
   * @returns Cấu hình đã tạo
   */
  async create(configuration: Partial<SystemConfiguration>): Promise<SystemConfiguration> {
    const newConfiguration = this.repository.create({
      ...configuration,
      createdAt: Math.floor(Date.now() / 1000), // Unix timestamp in seconds
      updatedAt: Math.floor(Date.now() / 1000), // Unix timestamp in seconds
    });

    return this.repository.save(newConfiguration);
  }
}
