import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse as ApiResponseDoc, ApiTags } from '@nestjs/swagger';
import { BlogPurchaseAdminService } from '@modules/blog/admin/services';
import { BlogPurchaseQueryDto, BlogPurchaseStatisticsDto, BlogPurchaseStatisticsResponseDto } from '../../dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Roles } from '@/modules/auth/decorators';
import { AppException } from '@/common';
import { ApiResponseDto } from '@/common/response';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';

@ApiTags(SWAGGER_API_TAGS.ADMIN_BLOGS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/blog/purchases')
export class BlogPurchaseAdminController {
  constructor(private readonly blogPurchaseAdminService: BlogPurchaseAdminService) {}

  /**
   * Lấy thống kê giao dịch mua bài viết
   */
  @Get('statistics')
  @ApiOperation({
    summary: 'Lấy thống kê giao dịch mua bài viết',
    description: 'Lấy thống kê giao dịch mua bài viết trong khoảng thời gian',
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Timestamp bắt đầu',
    type: Number,
    example: 1632474086123,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'Timestamp kết thúc',
    type: Number,
    example: 1632574086123,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Thống kê giao dịch mua bài viết.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            total_purchases: { type: 'number', example: 120 },
            total_points: { type: 'number', example: 12500 },
            total_platform_fee: { type: 'number', example: 625 },
            total_to_sellers: { type: 'number', example: 11875 },
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  async getStatistics(
    @Query() dto: BlogPurchaseStatisticsDto,
  ): Promise<ApiResponseDto<BlogPurchaseStatisticsResponseDto>> {
    const result = await this.blogPurchaseAdminService.getStatistics(dto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách giao dịch mua bài viết
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách giao dịch mua bài viết',
    description: 'Lấy danh sách giao dịch mua bài viết với phân trang và lọc',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'blog_id',
    required: false,
    description: 'Lọc theo ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'user_id',
    required: false,
    description: 'Lọc theo ID của người dùng',
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Timestamp bắt đầu',
    type: Number,
    example: 1632474086123,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'Timestamp kết thúc',
    type: Number,
    example: 1632574086123,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách giao dịch mua bài viết.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            content: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  userId: { type: 'number', example: 10 },
                  blogId: { type: 'number', example: 1 },
                  blog: {
                    type: 'object',
                    properties: {
                      id: { type: 'number', example: 1 },
                      title: { type: 'string', example: 'Tiêu đề bài viết' },
                      authorType: { type: 'string', example: 'USER' },
                    },
                  },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'number', example: 10 },
                      name: { type: 'string', example: 'Nguyễn Văn A' },
                    },
                  },
                  point: { type: 'number', example: 100 },
                  purchasedAt: { type: 'number', example: 1632474086123 },
                  platformFeePercent: { type: 'number', example: 0.05 },
                  sellerReceivePrice: { type: 'number', example: 95 },
                },
              },
            },
            totalItems: { type: 'number', example: 120 },
            itemCount: { type: 'number', example: 10 },
            itemsPerPage: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 12 },
            currentPage: { type: 'number', example: 1 },
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  async getPurchases(
    @Query() dto: BlogPurchaseQueryDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogPurchaseAdminService.getPurchases(dto);
    return ApiResponseDto.success(result);
  }
}