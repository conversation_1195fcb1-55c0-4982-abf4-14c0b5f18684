import { Test, TestingModule } from '@nestjs/testing';
import { BlogPurchaseAdminController } from '../controllers/blog-purchase-admin.controller';
import { BlogPurchaseAdminService } from '../services/blog-purchase-admin.service';
import { BlogPurchaseQueryDto, BlogPurchaseStatisticsDto } from '../../dto';
import { ApiResponse } from '@/common/interfaces/api-response.interface';

// Mock the SWAGGER_API_TAGS import
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    ADMIN_BLOG_PURCHASES: 'admin-blog-purchases'
  }
}));

// Mock the ErrorResponseSchema import
jest.mock('@/common/swagger', () => ({
  ErrorResponseSchema: {}
}));

// Mock ConfigModule
jest.mock('@nestjs/config', () => {
  const originalModule = jest.requireActual('@nestjs/config');
  return {
    ...originalModule,
    ConfigModule: {
      forRoot: jest.fn().mockReturnValue({
        module: class ConfigModule {},
        providers: [],
      }),
    },
  };
});

// Mock JwtAuthGuard
jest.mock('@/modules/auth/guards', () => ({
  JwtAuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

// Mock CurrentUser decorator
jest.mock('@/common/decorators/current-user.decorator', () => ({
  CurrentUser: () => () => 1, // Always return user ID 1
}));

describe('BlogPurchaseAdminController', () => {
  let controller: BlogPurchaseAdminController;
  let service: BlogPurchaseAdminService;

  const mockBlogPurchaseAdminService = {
    getPurchases: jest.fn(),
    getStatistics: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BlogPurchaseAdminController],
      providers: [
        {
          provide: BlogPurchaseAdminService,
          useValue: mockBlogPurchaseAdminService,
        },
      ],
    })
    .overrideGuard(jest.requireMock('@/modules/auth/guards').JwtAuthGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<BlogPurchaseAdminController>(BlogPurchaseAdminController);
    service = module.get<BlogPurchaseAdminService>(BlogPurchaseAdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPurchases', () => {
    it('should return paginated purchase list', async () => {
      // Arrange
      const query: BlogPurchaseQueryDto = { page: 1, limit: 10 };
      const mockPurchases = {
        content: [
          {
            id: 1,
            userId: 1,
            blogId: 1,
            point: 100,
            purchasedAt: 1625097600000,
            platformFeePercent: 10,
            sellerReceivePrice: 90,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseAdminService.getPurchases.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchases(query);

      // Assert
      expect(service.getPurchases).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });

    it('should handle empty purchase list', async () => {
      // Arrange
      const query: BlogPurchaseQueryDto = { page: 1, limit: 10 };
      const mockEmptyPurchases = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogPurchaseAdminService.getPurchases.mockResolvedValue(mockEmptyPurchases);

      // Act
      const result = await controller.getPurchases(query);

      // Assert
      expect(service.getPurchases).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyPurchases,
      });
    });

    it('should handle filtering by blog_id', async () => {
      // Arrange
      const query: BlogPurchaseQueryDto = {
        page: 1,
        limit: 10,
        blog_id: 1
      };
      const mockPurchases = {
        content: [
          {
            id: 1,
            userId: 1,
            blogId: 1,
            point: 100,
            purchasedAt: 1625097600000,
            platformFeePercent: 10,
            sellerReceivePrice: 90,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseAdminService.getPurchases.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchases(query);

      // Assert
      expect(service.getPurchases).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });

    it('should handle filtering by user_id', async () => {
      // Arrange
      const query: BlogPurchaseQueryDto = {
        page: 1,
        limit: 10,
        user_id: 1
      };
      const mockPurchases = {
        content: [
          {
            id: 1,
            userId: 1,
            blogId: 1,
            point: 100,
            purchasedAt: 1625097600000,
            platformFeePercent: 10,
            sellerReceivePrice: 90,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseAdminService.getPurchases.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchases(query);

      // Assert
      expect(service.getPurchases).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });

    it('should handle filtering by date range', async () => {
      // Arrange
      const query: BlogPurchaseQueryDto = {
        page: 1,
        limit: 10,
        start_date: 1625000000000,
        end_date: 1626000000000
      };
      const mockPurchases = {
        content: [
          {
            id: 1,
            userId: 1,
            blogId: 1,
            point: 100,
            purchasedAt: 1625097600000,
            platformFeePercent: 10,
            sellerReceivePrice: 90,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseAdminService.getPurchases.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchases(query);

      // Assert
      expect(service.getPurchases).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });
  });

  describe('getStatistics', () => {
    it('should return purchase statistics', async () => {
      // Arrange
      const query: BlogPurchaseStatisticsDto = {};
      const mockStatistics = {
        totalPurchases: 10,
        totalPoints: 1000,
        totalPlatformFee: 100,
        totalSellerReceive: 900,
      };
      mockBlogPurchaseAdminService.getStatistics.mockResolvedValue(mockStatistics);

      // Act
      const result = await controller.getStatistics(query);

      // Assert
      expect(service.getStatistics).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockStatistics,
      });
    });

    it('should handle statistics with date range', async () => {
      // Arrange
      const query: BlogPurchaseStatisticsDto = {
        start_date: 1625000000000,
        end_date: 1626000000000
      };
      const mockStatistics = {
        totalPurchases: 5,
        totalPoints: 500,
        totalPlatformFee: 50,
        totalSellerReceive: 450,
      };
      mockBlogPurchaseAdminService.getStatistics.mockResolvedValue(mockStatistics);

      // Act
      const result = await controller.getStatistics(query);

      // Assert
      expect(service.getStatistics).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockStatistics,
      });
    });

    it('should handle empty statistics', async () => {
      // Arrange
      const query: BlogPurchaseStatisticsDto = {};
      const mockEmptyStatistics = {
        totalPurchases: 0,
        totalPoints: 0,
        totalPlatformFee: 0,
        totalSellerReceive: 0,
      };
      mockBlogPurchaseAdminService.getStatistics.mockResolvedValue(mockEmptyStatistics);

      // Act
      const result = await controller.getStatistics(query);

      // Assert
      expect(service.getStatistics).toHaveBeenCalledWith(query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyStatistics,
      });
    });
  });
});
