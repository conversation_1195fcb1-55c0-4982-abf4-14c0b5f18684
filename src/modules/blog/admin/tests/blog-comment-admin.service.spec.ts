import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogCommentAdminService } from '../services/blog-comment-admin.service';
import { Blog } from '../../entities/blog.entity';
import { BlogComment } from '../../entities/blog-comment.entity';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { CreateBlogCommentDto } from '../../dto/create-blog-comment.dto';
import { NotFoundException } from '@nestjs/common';
import { GetBlogCommentsDto } from '../../dto/get-blog-comments.dto';
import { SqlHelper } from '@common/helpers/sql.helper';

// Mock data
const mockBlog = {
  id: 1,
  title: 'Test Blog Title',
  content: 'https://cdn.example.com/blogs/content-123.html',
  point: 100,
  viewCount: 1000,
  thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
  tags: ['nestjs', 'typescript', 'backend'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userId: 1,
  employeeId: null,
  employeeModerator: null,
  authorType: AuthorTypeEnum.USER,
  status: BlogStatusEnum.APPROVED,
  enable: true,
  like: 500
};

const mockComment = {
  id: 1,
  blogId: 1,
  userId: null,
  employeeId: 5,
  content: 'This is an admin comment',
  createdAt: 1625097600000,
  authorType: AuthorTypeEnum.SYSTEM,
  parentCommentId: null
};

const mockReplyComment = {
  id: 2,
  blogId: 1,
  userId: null,
  employeeId: 5,
  content: 'This is an admin reply comment',
  createdAt: 1625097700000,
  authorType: AuthorTypeEnum.SYSTEM,
  parentCommentId: 1
};

describe('BlogCommentAdminService', () => {
  let service: BlogCommentAdminService;
  let blogRepository: Repository<Blog>;
  let blogCommentRepository: Repository<BlogComment>;
  let sqlHelper: SqlHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogCommentAdminService,
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(BlogComment),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[], 0])
            }),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            select: jest.fn(),
            insert: jest.fn(),
            exists: jest.fn(),
            delete: jest.fn(),
            count: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BlogCommentAdminService>(BlogCommentAdminService);
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    blogCommentRepository = module.get<Repository<BlogComment>>(getRepositoryToken(BlogComment));
    sqlHelper = module.get<SqlHelper>(SqlHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSystemComment', () => {
    it('should create a new comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin comment',
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(mockComment);

      // Act
      const result = await service.createSystemComment(blogId, employeeId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(sqlHelper.insert).toHaveBeenCalled();
      // Kiểm tra tham số đầu tiên là 'blog_comments'
      expect(sqlHelper.insert).toHaveBeenCalledWith(
        'blog_comments',
        expect.objectContaining({
          blog_id: blogId,
          user_id: null,
          content: dto.content,
          author_type: AuthorTypeEnum.SYSTEM,
        }),
        expect.anything()
      );
      expect(result).toEqual(mockComment);
    });

    it('should create a reply comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin reply comment',
        parent_comment_id: 1,
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([{ id: 1, parentCommentId: null }]);
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(mockReplyComment);

      // Act
      const result = await service.createSystemComment(blogId, employeeId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(sqlHelper.select).toHaveBeenCalledWith(
        'blog_comments',
        ['id', 'parent_comment_id as parentCommentId'],
        [
          { condition: 'id = :id', params: { id: dto.parent_comment_id } },
          { condition: 'blog_id = :blogId', params: { blogId } }
        ]
      );
      expect(sqlHelper.insert).toHaveBeenCalled();
      // Kiểm tra tham số đầu tiên là 'blog_comments'
      expect(sqlHelper.insert).toHaveBeenCalledWith(
        'blog_comments',
        expect.objectContaining({
          blog_id: blogId,
          user_id: null,
          content: dto.content,
          author_type: AuthorTypeEnum.SYSTEM,
          parent_comment_id: dto.parent_comment_id
        }),
        expect.anything()
      );
      expect(result).toEqual(mockReplyComment);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin comment',
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.createSystemComment(blogId, employeeId, dto)).rejects.toThrow(
        new NotFoundException(`Blog with ID ${blogId} not found`)
      );
    });

    it('should throw NotFoundException when parent comment not found', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin reply comment',
        parent_comment_id: 999,
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.createSystemComment(blogId, employeeId, dto)).rejects.toThrow(
        new NotFoundException(`Parent comment with ID ${dto.parent_comment_id} not found`)
      );
    });
  });

  describe('getComments', () => {
    it('should return blog comments', async () => {
      // Arrange
      const blogId = 1;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockComments = [
        {
          id: 1,
          blogId: 1,
          userId: null,
          employeeId: 5,
          content: 'This is an admin comment',
          createdAt: 1625097600000,
          authorType: AuthorTypeEnum.SYSTEM,
          parentCommentId: null
        }
      ];
      const mockReplies = [
        {
          id: 2,
          blogId: 1,
          userId: null,
          employeeId: 5,
          content: 'This is an admin reply comment',
          createdAt: 1625097700000,
          authorType: AuthorTypeEnum.SYSTEM,
          parentCommentId: 1
        }
      ];

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(blogCommentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockComments, 1])
      } as any);
      jest.spyOn(blogCommentRepository, 'find').mockResolvedValue(mockReplies as any);

      // Act
      const result = await service.getComments(blogId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(blogCommentRepository.createQueryBuilder).toHaveBeenCalled();
      expect(blogCommentRepository.find).toHaveBeenCalledWith({
        where: { parentCommentId: 1 },
        order: { createdAt: 'ASC' }
      });
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
      expect(result.currentPage).toBe(1);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getComments(blogId, dto)).rejects.toThrow(
        new NotFoundException(`Blog with ID ${blogId} not found`)
      );
    });

    it('should return empty array when no comments found', async () => {
      // Arrange
      const blogId = 1;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(blogCommentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0])
      } as any);

      // Act
      const result = await service.getComments(blogId, dto);

      // Assert
      expect(result.content).toEqual([]);
      expect(result.totalItems).toBe(0);
    });
  });
});
