import { Test, TestingModule } from '@nestjs/testing';
import { BlogAdminController } from '../controllers/blog-admin.controller';
import { BlogAdminService } from '../services/blog-admin.service';
import { GetAdminBlogsDto, CreateBlogDto, ModerateBlogDto, UpdateBlogMediaDto, MediaTypeEnum } from '../../dto';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { ApiResponse } from '@/common/interfaces/api-response.interface';
import { NotFoundException } from '@nestjs/common';

// Mock the SWAGGER_API_TAGS import
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    ADMIN_BLOGS: 'admin-blogs'
  }
}));

// Mock the ErrorResponseSchema import
jest.mock('@/common/swagger', () => ({
  ErrorResponseSchema: {}
}));

// Mock ConfigModule
jest.mock('@nestjs/config', () => {
  const originalModule = jest.requireActual('@nestjs/config');
  return {
    ...originalModule,
    ConfigModule: {
      forRoot: jest.fn().mockReturnValue({
        module: class ConfigModule {},
        providers: [],
      }),
    },
  };
});

// Mock JwtAuthGuard
jest.mock('@/modules/auth/guards', () => ({
  JwtAuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

describe('BlogAdminController', () => {
  let controller: BlogAdminController;
  let service: BlogAdminService;

  const mockBlogAdminService = {
    findAll: jest.fn(),
    findPendingBlogs: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    updateMedia: jest.fn(),
    moderate: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BlogAdminController],
      providers: [
        {
          provide: BlogAdminService,
          useValue: mockBlogAdminService,
        },
      ],
    })
    .overrideGuard(jest.requireMock('@/modules/auth/guards').JwtAuthGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<BlogAdminController>(BlogAdminController);
    service = module.get<BlogAdminService>(BlogAdminService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated blogs', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            viewCount: 1000,
            thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
            author: {
              id: 1,
              name: 'System Admin',
              type: AuthorTypeEnum.SYSTEM,
              avatar: 'https://cdn.example.com/avatars/system.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employeeModerator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogAdminService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(dto);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(dto);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });

    it('should handle empty blog list', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockEmptyBlogs = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogAdminService.findAll.mockResolvedValue(mockEmptyBlogs);

      // Act
      const result = await controller.findAll(dto);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(dto);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyBlogs,
      });
    });

    it('should handle filtering by status', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = {
        page: 1,
        limit: 10,
        status: BlogStatusEnum.APPROVED
      };
      const mockBlogs = {
        content: [
          {
            id: 1,
            title: 'Test Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            viewCount: 1000,
            thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
            author: {
              id: 1,
              name: 'System Admin',
              type: AuthorTypeEnum.SYSTEM,
              avatar: 'https://cdn.example.com/avatars/system.jpg',
            },
            status: BlogStatusEnum.APPROVED,
            enable: true,
            like: 500,
            employeeModerator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogAdminService.findAll.mockResolvedValue(mockBlogs);

      // Act
      const result = await controller.findAll(dto);

      // Assert
      expect(service.findAll).toHaveBeenCalledWith(dto);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlogs,
      });
    });
  });

  describe('findPendingBlogs', () => {
    it('should return pending blogs', async () => {
      // Arrange
      const dto: GetAdminBlogsDto = { page: 1, limit: 10 };
      const mockPendingBlogs = {
        content: [
          {
            id: 1,
            title: 'Pending Blog',
            content: 'https://cdn.example.com/blogs/content-123.html',
            point: 100,
            viewCount: 0,
            thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            tags: ['nestjs', 'typescript'],
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
            author: {
              id: 2,
              name: 'User Author',
              type: AuthorTypeEnum.USER,
              avatar: 'https://cdn.example.com/avatars/user.jpg',
            },
            status: BlogStatusEnum.PENDING,
            enable: true,
            like: 0,
            employeeModerator: null,
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogAdminService.findPendingBlogs.mockResolvedValue(mockPendingBlogs);

      // Act
      const result = await controller.findPendingBlogs(dto);

      // Assert
      expect(service.findPendingBlogs).toHaveBeenCalledWith(dto);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPendingBlogs,
      });
    });
  });

  describe('findOne', () => {
    it('should return a blog by id', async () => {
      // Arrange
      const blogId = 1;
      const mockBlog = {
        id: 1,
        title: 'Test Blog',
        content: 'https://cdn.example.com/blogs/content-123.html',
        point: 100,
        viewCount: 1000,
        thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
        tags: ['nestjs', 'typescript'],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        author: {
          id: 1,
          name: 'System Admin',
          type: AuthorTypeEnum.SYSTEM,
          avatar: 'https://cdn.example.com/avatars/system.jpg',
        },
        status: BlogStatusEnum.APPROVED,
        enable: true,
        like: 500,
        employeeModerator: null,
      };
      mockBlogAdminService.findOne.mockResolvedValue(mockBlog);

      // Act
      const result = await controller.findOne(blogId);

      // Assert
      expect(service.findOne).toHaveBeenCalledWith(blogId);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockBlog,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      mockBlogAdminService.findOne.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.findOne(blogId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('create', () => {
    it('should create a new blog and return it', async () => {
      // Arrange
      const createBlogDto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.SYSTEM,
      };
      const mockResult = {
        id: 1,
        title: 'New Blog',
        description: 'Blog description',
        content: null,
        point: 100,
        viewCount: 0,
        thumbnailUrl: null,
        tags: ['nestjs', 'typescript'],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        author: {
          id: 1,
          name: 'System Admin',
          type: AuthorTypeEnum.SYSTEM,
          avatar: 'https://cdn.example.com/avatars/system.jpg',
        },
        status: BlogStatusEnum.APPROVED,
        enable: true,
        like: 0,
        employeeModerator: null,
      };
      mockBlogAdminService.create.mockResolvedValue(mockResult);

      // Act
      const result = await controller.create(createBlogDto);

      // Assert
      expect(service.create).toHaveBeenCalledWith(createBlogDto);
      expect(result).toEqual({
        code: 201,
        message: 'Blog created successfully',
        result: mockResult,
      });
    });
  });

  describe('updateMedia', () => {
    it('should update blog media and return upload URLs', async () => {
      // Arrange
      const blogId = 1;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.mediaType = MediaTypeEnum.CONTENT;
      updateMediaDto.mediaContentType = 'text/html';
      const mockResult = {
        upload_url: 'https://cdn-storage.example.com/temp/uploads/123456789/content.html?signature=abc123...',
        final_url: 'https://cdn.example.com/blogs/content-1.html',
      };
      mockBlogAdminService.updateMedia.mockResolvedValue(mockResult);

      // Act
      const result = await controller.updateMedia(blogId, updateMediaDto);

      // Assert
      expect(service.updateMedia).toHaveBeenCalledWith(blogId, updateMediaDto);
      expect(result).toEqual({
        code: 200,
        message: 'Media URLs generated successfully',
        result: mockResult,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const updateMediaDto = new UpdateBlogMediaDto();
      updateMediaDto.mediaType = MediaTypeEnum.CONTENT;
      updateMediaDto.mediaContentType = 'text/html';
      mockBlogAdminService.updateMedia.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.updateMedia(blogId, updateMediaDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('moderate', () => {
    it('should moderate a blog and return success response', async () => {
      // Arrange
      const blogId = 1;
      const moderateBlogDto: ModerateBlogDto = {
        status: BlogStatusEnum.APPROVED,
      };
      const mockResult = {
        id: 1,
        status: BlogStatusEnum.APPROVED,
        employeeModerator: null,
      };
      mockBlogAdminService.moderate.mockResolvedValue(mockResult);

      // Act
      const result = await controller.moderate(blogId, moderateBlogDto);

      // Assert
      expect(service.moderate).toHaveBeenCalledWith(blogId, moderateBlogDto);
      expect(result).toEqual({
        code: 200,
        message: 'Blog moderated successfully',
        result: mockResult,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const moderateBlogDto: ModerateBlogDto = {
        status: BlogStatusEnum.APPROVED,
      };
      mockBlogAdminService.moderate.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.moderate(blogId, moderateBlogDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('delete', () => {
    it('should delete a blog and return success response', async () => {
      // Arrange
      const blogId = 1;
      mockBlogAdminService.delete.mockResolvedValue(undefined);

      // Act
      const result = await controller.delete(blogId);

      // Assert
      expect(service.delete).toHaveBeenCalledWith(blogId);
      expect(result).toEqual({
        code: 200,
        message: 'Blog deleted successfully',
        result: null,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      mockBlogAdminService.delete.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.delete(blogId)).rejects.toThrow(NotFoundException);
    });
  });
});
