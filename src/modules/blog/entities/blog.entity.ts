import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { AuthorTypeEnum, BlogStatusEnum } from '../enums';

/**
 * Entity đ<PERSON><PERSON> diện cho bảng blogs trong cơ sở dữ liệu
 */
@Entity('blogs')
export class Blog {
  //
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  //
  @Column({ name: 'title', length: 500, nullable: true })
  title: string;

  @Column({ name: 'description', length: 1000, nullable: true })
  description: string;

  @Column({ name: 'content', length: 255, nullable: true })
  content: string;

  //
  @Column({ name: 'point', type: 'bigint', nullable: true })
  point: number;

  //
  @Column({ name: 'view_count', type: 'bigint', nullable: true })
  viewCount: number;

  //
  @Column({ name: 'thumbnail_url', length: 255, nullable: true })
  thumbnailUrl: string;

  //
  @Column({ name: 'tags', type: 'jsonb', nullable: true })
  tags: any;

  //
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;

  // id, name, avatar
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  // id, name, avatar
  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId: number | null;

  @Column({ name: 'employee_moderator', type: 'integer', nullable: true })
  employeeModerator: number | null;

  //
  @Column({
    name: 'author_type',
    type: 'enum',
    enum: AuthorTypeEnum,
    nullable: true,
  })
  authorType: AuthorTypeEnum;

  //
  @Column({ name: 'status', type: 'enum', enum: BlogStatusEnum })
  status: BlogStatusEnum;

  @Column({ name: 'enable', default: true })
  enable: boolean;

  //
  @Column({ name: 'like', type: 'bigint', nullable: true })
  like: number;
}
