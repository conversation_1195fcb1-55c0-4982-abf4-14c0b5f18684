import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { AuthorTypeEnum } from '../enums';

/**
 * Entity đại diện cho bảng blog_comments trong cơ sở dữ liệu
 */
@Entity('blog_comments')
export class BlogComment {
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  @Column({ name: 'blog_id', type: 'integer', nullable: true })
  blogId: number;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  @Column({ name: 'content', length: 1000, nullable: true })
  content: string;

  @Column({
    name: 'author_type',
    type: 'enum',
    enum: AuthorTypeEnum,
    nullable: true,
  })
  authorType: AuthorTypeEnum;

  @Column({ name: 'employee_id', type: 'integer', nullable: true })
  employeeId: number | null;

  @Column({ name: 'parent_comment_id', type: 'bigint', nullable: true })
  parentCommentId: number | null;
}
