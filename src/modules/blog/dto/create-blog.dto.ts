import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength, Min } from 'class-validator';
import { AuthorTypeEnum, BlogStatusEnum } from '../enums';

export class CreateBlogDto {
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Tiêu đề bài viết',
    maxLength: 500,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(500)
  title: string;

  @ApiProperty({
    description: 'Mô tả ngắn về bài viết',
    example: 'Mô tả ngắn về bài viết',
    maxLength: 1000,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(1000)
  description: string;

  @ApiProperty({
    description: 'Loại media của nội dung',
    example: 'text/html',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  contentMediaType: string;

  @ApiProperty({
    description: 'Loại media của thumbnail',
    example: 'image/jpeg',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  thumbnailMediaType: string;

  @ApiProperty({
    description: 'Số point để mua bài viết',
    example: 100,
    required: true,
    minimum: 0,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  point: number;

  @ApiProperty({
    description: 'Tags của bài viết',
    example: ['tag1', 'tag2'],
    type: [String],
    required: true,
  })
  @IsArray()
  @IsString({ each: true })
  @MaxLength(50, { each: true })
  tags: string[];

  @ApiProperty({
    description: 'Trạng thái bài viết',
    example: BlogStatusEnum.DRAFT,
    enum: BlogStatusEnum,
    default: BlogStatusEnum.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(BlogStatusEnum)
  status?: BlogStatusEnum;

  @ApiProperty({
    description: 'Loại tác giả',
    example: AuthorTypeEnum.SYSTEM,
    enum: AuthorTypeEnum,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(AuthorTypeEnum)
  authorType: AuthorTypeEnum;
}
