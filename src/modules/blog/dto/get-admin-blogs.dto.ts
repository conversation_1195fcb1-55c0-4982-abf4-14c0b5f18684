import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { GetBlogsDto } from './get-blogs.dto';

export class GetAdminBlogsDto extends GetBlogsDto {
  @ApiProperty({
    description: 'Lọc theo ID của người dùng',
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Transform(({ value, obj }) => {
    // Hỗ trợ cả camelCase và snake_case
    return value || obj.user_id;
  })
  userId?: number;

  @ApiProperty({
    description: 'Lọc theo ID của nhân viên',
    required: false,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Transform(({ value, obj }) => {
    // Hỗ trợ cả camelCase và snake_case
    return value || obj.employee_id;
  })
  employeeId?: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kiếm theo tiêu đề',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  titleSearch?: string;
}
