import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

export enum MediaTypeEnum {
  CONTENT = 'content',
  THUMBNAIL = 'thumbnail',
}

export class UpdateBlogMediaDto {
  @ApiProperty({
    description: 'Loại media cần cập nhật',
    example: 'content',
    enum: MediaTypeEnum,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(MediaTypeEnum)
  media_type: MediaTypeEnum;

  // Alias for camelCase access
  get mediaType(): MediaTypeEnum {
    return this.media_type;
  }

  set mediaType(value: MediaTypeEnum) {
    this.media_type = value;
  }

  @ApiProperty({
    description: 'Loại nội dung của media. Nếu media_type là "content" thì phải là "text/html". Nếu media_type là "thumbnail" thì phải là một trong các định dạng hình ảnh: "image/jpeg", "image/png", "image/webp", "image/gif".',
    example: 'text/html',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  media_content_type: string;

  // Alias for camelCase access
  get mediaContentType(): string {
    return this.media_content_type;
  }

  set mediaContentType(value: string) {
    this.media_content_type = value;
  }
}
