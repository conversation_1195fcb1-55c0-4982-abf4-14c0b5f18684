# Luồng xử lý Blog trong hệ thống

## Tổng quan

Hệ thống blog được thiết kế với hai phần chính:
1. **User**: Người dùng thông thường có thể tạo, xem, bì<PERSON> luận và mua bài viết
2. **Admin**: Quản trị viên có thể tạo, qu<PERSON><PERSON> lý, phê duyệt bài viết và xem thống kê

## Luồng xử lý bài viết

### 1. Tạo bài viết

#### Ng<PERSON>ời dùng (User)
1. Người dùng tạo bài viết mới
2. <PERSON>à<PERSON> viết được lưu với trạng thái `PENDING` (chờ phê duyệt)
3. Admin cần phê duyệt bài viết trước khi nó được hiển thị công khai

#### Quả<PERSON> trị viên (Admin)
1. <PERSON><PERSON> tạo bài viết mới
2. <PERSON><PERSON><PERSON> viết được lưu với trạng thái `APPROVED` (đã phê duyệt)
3. Bài viết ngay lập tức được hiển thị công khai

### 2. Phê duyệt bài viết

1. Admin xem danh sách các bài viết đang chờ phê duyệt (`PENDING`)
2. Admin xem chi tiết bài viết và quyết định:
   - Phê duyệt: Chuyển trạng thái sang `APPROVED`
   - Từ chối: Chuyển trạng thái sang `DRAFT`

### 3. Xem bài viết

#### Người dùng (User)
1. Người dùng chỉ có thể xem các bài viết đã được phê duyệt (`APPROVED`)
2. Người dùng có thể xem danh sách tất cả bài viết của mình với mọi trạng thái

#### Quản trị viên (Admin)
1. Admin có thể xem tất cả bài viết với mọi trạng thái
2. Admin có thể lọc bài viết theo trạng thái, người tạo, v.v.

### 4. Mua bài viết

1. Người dùng xem chi tiết bài viết
2. Nếu bài viết có giá (point > 0), người dùng cần mua để xem nội dung đầy đủ
3. Khi mua, hệ thống:
   - Trừ point của người mua
   - Cộng point cho người bán (sau khi trừ phí sàn)
   - Lưu thông tin giao dịch

### 5. Bình luận

1. Người dùng và admin có thể bình luận trên các bài viết đã được phê duyệt
2. Bình luận có thể là bình luận gốc hoặc phản hồi cho bình luận khác
3. Admin có thể xóa bình luận không phù hợp

## Trạng thái bài viết

1. **DRAFT**: Bài viết nháp hoặc bị từ chối
2. **PENDING**: Bài viết đang chờ phê duyệt
3. **APPROVED**: Bài viết đã được phê duyệt và hiển thị công khai

## Thống kê

### Người dùng (User)
Người dùng có thể xem thống kê về bài viết của mình:
- Tổng số bài viết
- Số bài viết đã được phê duyệt
- Số bài viết đang chờ phê duyệt
- Số bài viết bị từ chối
- Tổng lượt xem
- Tổng lượt thích

### Quản trị viên (Admin)
Admin có thể xem thống kê tổng quan về hệ thống:
- Tổng số bài viết theo trạng thái
- Tổng lượt xem, lượt thích
- Thống kê giao dịch mua bài viết
- Doanh thu từ phí sàn

## Phân quyền

### Người dùng (User)
- Tạo bài viết (cần phê duyệt)
- Xem bài viết đã được phê duyệt
- Bình luận trên bài viết
- Mua bài viết
- Xem thống kê bài viết của mình

### Quản trị viên (Admin)
- Tạo bài viết (không cần phê duyệt)
- Xem tất cả bài viết
- Phê duyệt/từ chối bài viết
- Chỉnh sửa/xóa bài viết
- Quản lý bình luận
- Xem thống kê toàn hệ thống
