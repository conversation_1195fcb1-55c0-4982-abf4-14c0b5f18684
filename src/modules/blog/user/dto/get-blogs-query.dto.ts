import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsArray, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';

export class GetBlogsQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái',
    enum: BlogStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(BlogStatusEnum)
  status?: BlogStatusEnum;

  @ApiProperty({
    description: 'Lọc theo loại tác giả',
    enum: AuthorTypeEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(AuthorTypeEnum)
  author_type?: AuthorTypeEnum;

  @ApiProperty({
    description: 'Lọc theo tags',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  tags?: string[];
}
