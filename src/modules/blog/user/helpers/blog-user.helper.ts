import { User } from '@/modules/user/entities/user.entity';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { Blog } from '../../entities';
import { GetBlogsDto, BlogResponseDto, PaginatedBlogResponseDto } from '../../dto';

export class BlogUserHelper {
  /**
   * Helper method để tạo query builder cơ bản cho Blog
   */
  static createBlogQueryBuilder(
    blogRepository: Repository<Blog>,
  ): SelectQueryBuilder<Blog> {
    return blogRepository.createQueryBuilder('blog');
  }

  /**
   * Helper method để áp dụng các điều kiện lọc
   */
  static applyFilters(
    queryBuilder: SelectQueryBuilder<Blog>,
    filters: {
      authorType?: string;
      tags?: string[];
      search?: string;
    },
  ): void {
    const { authorType, tags, search } = filters;

    // Lọc theo loại tác giả
    if (authorType) {
      queryBuilder.andWhere('blog.authorType = :authorType', {
        authorType: authorType,
      });
    }

    // Lọc theo tags
    if (tags && tags.length > 0) {
      // Lọc theo tags (giả sử tags lưu dạng jsonb trong PostgreSQL)
      queryBuilder.andWhere('blog.tags @> :tags', {
        tags: JSON.stringify(tags),
      });
    }

    // Tìm kiếm theo tiêu đề và nội dung
    if (search) {
      queryBuilder.andWhere(
        '(blog.title ILIKE :search OR blog.content ILIKE :search)',
        { search: `%${search}%` },
      );
    }
  }

  /**
   * Helper method để lấy thông tin user cho mỗi bài viết
   */
  static async populateUserInfo(
    blogs: Blog[],
    userRepository: Repository<User>,
  ): Promise<BlogResponseDto[]> {
    // Lấy danh sách user IDs
    const userIds = blogs
      .filter((blog) => blog.userId)
      .map((blog) => blog.userId);

    // Nếu không có user IDs thì trả về luôn
    if (userIds.length === 0) {
      return blogs.map((blog) => BlogResponseDto.fromEntity(blog));
    }

    // Lấy thông tin users
    const users = await userRepository.find({
      where: { id: In(userIds) },
      select: ['id', 'fullName', 'avatar'],
    });

    // Map user info cho mỗi blog
    return blogs.map((blog) => {
      const user = users.find((u) => u.id === blog.userId) || null;
      return BlogResponseDto.fromEntity(blog, user);
    });
  }

  /**
   * Helper method để lấy danh sách bài viết với phân trang
   */
  static async findBlogsWithPagination(
    queryBuilder: SelectQueryBuilder<Blog>,
    dto: GetBlogsDto,
    userRepository: Repository<User>,
  ): Promise<PaginatedBlogResponseDto> {
    const { page, limit, authorType, search, sortBy, sortDirection } = dto;

    // Áp dụng các điều kiện lọc
    this.applyFilters(queryBuilder, { authorType, search });

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`blog.${sortBy || 'createdAt'}`, sortDirection || 'DESC');

    // Thực hiện phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Lấy dữ liệu
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    // Lấy thông tin user cho mỗi bài viết
    const blogs = await this.populateUserInfo(items, userRepository);

    // Tính toán thông tin phân trang
    return {
      content: blogs,
      totalItems,
      itemCount: blogs.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page,
    };
  }
}
