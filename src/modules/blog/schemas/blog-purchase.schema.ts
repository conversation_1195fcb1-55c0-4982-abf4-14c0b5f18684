import { ApiProperty } from '@nestjs/swagger';
import { BlogPurchase } from '@modules/blog/entities';

export class BlogPurchaseSchema {
  @ApiProperty({
    description: 'ID của giao dịch mua bài viết',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người mua',
    example: 1,
    nullable: true,
  })
  userId: number;

  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
    nullable: true,
  })
  blogId: number;

  @ApiProperty({
    description: 'Số point thời điểm mua',
    example: 100,
    nullable: true,
  })
  point: number;

  @ApiProperty({
    description: 'Thời gian mua hàng (Unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  purchasedAt: number;

  @ApiProperty({
    description: 'Phần trăm phí sàn',
    example: 10.5,
    nullable: true,
  })
  platformFeePercent: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> người bán nhận được sau khi trừ phí sàn',
    example: 90,
    nullable: true,
  })
  sellerReceivePrice: number;

  constructor(partial: Partial<BlogPurchase>) {
    Object.assign(this, partial);
  }
}

export class BlogPurchaseListResponseSchema {
  @ApiProperty({
    description: 'Danh sách giao dịch mua bài viết',
    type: [BlogPurchaseSchema],
  })
  items: BlogPurchaseSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số giao dịch',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số giao dịch trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số giao dịch trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
