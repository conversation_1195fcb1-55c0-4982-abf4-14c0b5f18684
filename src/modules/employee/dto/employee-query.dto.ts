import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của nhân viên
 */
export enum EmployeeSortField {
  ID = 'id',
  FULL_NAME = 'fullName',
  EMAIL = 'email',
  PHONE_NUMBER = 'phoneNumber',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho query parameters khi lấy danh sách nhân viên
 */
export class EmployeeQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động của nhân viên',
    required: false,
    example: true,
  })
  @IsOptional()
  enable?: boolean;

  @ApiProperty({
    description: '<PERSON>ọ<PERSON> theo email nhân viên',
    required: false,
    example: '<EMAIL>',
  })
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Lọc theo số điện thoại nhân viên',
    required: false,
    example: '0987654321',
  })
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: EmployeeSortField,
    default: EmployeeSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(EmployeeSortField)
  sortBy?: EmployeeSortField = EmployeeSortField.CREATED_AT;
}
