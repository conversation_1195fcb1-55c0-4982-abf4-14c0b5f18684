import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin vai trò và số lượng
 */
export class RoleCountDto {
  @ApiProperty({
    description: 'ID của vai trò',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên vai trò',
    example: 'Admin'
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả vai trò',
    example: 'Quản trị viên hệ thống'
  })
  description: string;

  @ApiProperty({
    description: 'Số lượng nhân viên có vai trò này',
    example: 3
  })
  employeeCount: number;
}

/**
 * DTO trả về tổng quan về nhân viên
 */
export class EmployeeOverviewDto {
  @ApiProperty({
    description: 'Tổng số nhân viên',
    example: 10
  })
  totalEmployees: number;

  @ApiProperty({
    description: 'Tổng số vai trò',
    example: 5
  })
  totalRoles: number;

  @ApiProperty({
    description: 'Tổng số quyền',
    example: 25
  })
  totalPermissions: number;

  @ApiProperty({
    description: 'Danh sách vai trò với số lượng nhân viên',
    type: [RoleCountDto]
  })
  roles: RoleCountDto[];
} 