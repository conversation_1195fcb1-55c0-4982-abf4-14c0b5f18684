import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EmployeeResponseDto } from './employee-response.dto';

/**
 * DTO cho phản hồi khi tạo nhân viên mới có thêm thông tin URL tạm thời để upload avatar
 */
export class CreateEmployeeResponseDto extends EmployeeResponseDto {
  /**
   * URL tạm thời để tải lên avatar (nếu có yêu cầu)
   */
  @ApiPropertyOptional({
    description: 'URL tạm thời để tải lên avatar',
    example: 'https://storage.example.com/upload/signed-url?token=abc123',
  })
  avatarUploadUrl?: string;

  /**
   * Khóa S3 cho avatar (nếu có yêu cầu)
   */
  @ApiPropertyOptional({
    description: 'Khóa S3 cho avatar',
    example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
  })
  avatarKey?: string;

  /**
   * Thời điểm hết hạn của URL tạm thời (timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời điểm hết hạn của URL tạm thời (timestamp)',
    example: 1746968772000,
  })
  avatarUrlExpiresAt?: number;
}
