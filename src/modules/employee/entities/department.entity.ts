import {
    Entity,
    PrimaryColumn, // Changed from PrimaryGeneratedColumn
    Column,
    // Removed unused imports: ManyToOne, OneToMany, JoinColumn, Index, User
  } from 'typeorm';
  
  /**
   * Phòng ban
   */
  @Entity('departments')
  export class Department {
    /**
     * Primary key for the department.
     * Note: This is NOT auto-generated based on the schema (integer, not serial).
     * You will need to provide the ID when creating a new department.
     */
    @PrimaryColumn({ type: 'int', nullable: false })
    id: number;
  
    /**
     * Tên phòng ban
     */
    @Column({ type: 'varchar', length: 255, nullable: true }) // Assumed nullable as NOT NULL is not specified
    name: string | null;
  
    /**
     * Th<PERSON>i gian tạo (Unix timestamp)
     * Stored as BIGINT in the database.
     */
    @Column({ type: 'bigint', nullable: true })
    created_at: string | null; // Use string for large integers, TypeORM handles conversion
  
    /**
     * Th<PERSON>i gian cập nhật (Unix timestamp)
     * Stored as BIGINT in the database.
     */
    @Column({ type: 'bigint', nullable: true })
    update_at: string | null; // Use string for large integers, TypeORM handles conversion
  
    // --- Relationships removed as they are not in the new schema ---
  }  