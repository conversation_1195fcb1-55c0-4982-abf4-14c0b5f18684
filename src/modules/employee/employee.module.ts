import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as entities from './entities';
import * as repositories from './repositories';
import * as services from './services';
import * as controllers from './controller';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature(Object.values(entities)),
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
    ...Object.values(services),
  ],
  exports: [
    ...Object.values(repositories),
    ...Object.values(services),
  ],
})
export class EmployeeModule {}
