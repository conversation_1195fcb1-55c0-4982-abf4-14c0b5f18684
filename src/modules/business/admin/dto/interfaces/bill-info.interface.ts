import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';

/**
 * Class cho thông tin hóa đơn
 */
export class BillInfo {
  /**
   * Tổng tiền hàng
   */
  @ApiProperty({
    description: 'Tổng tiền hàng',
    example: 200000
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  subtotal: number;

  /**
   * Thuế
   */
  @ApiProperty({
    description: 'Thuế',
    example: 20000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  tax?: number;

  /**
   * Phí vận chuyển
   */
  @ApiProperty({
    description: 'Phí vận chuyển',
    example: 30000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  shipping?: number;

  /**
   * Tổng tiền
   */
  @ApiProperty({
    description: 'Tổng tiền',
    example: 250000
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  total: number;

  /**
   * <PERSON><PERSON>ơng thức thanh toán
   */
  @ApiProperty({
    description: '<PERSON><PERSON>ơng thức thanh toán',
    example: 'COD',
    required: false
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  /**
   * Các thông tin khác của hóa đơn
   */
  [key: string]: any;
}
