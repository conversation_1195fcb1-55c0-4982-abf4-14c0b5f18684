import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';

/**
 * DTO cho response của file
 */
export class FileResponseDto {
  @ApiProperty({
    description: 'ID của tệp tin',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên tệp tin',
    example: 'document.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'ID thư mục chứa tệp tin',
    example: 1,
  })
  @Expose()
  folderId: number;

  @ApiProperty({
    description: '<PERSON>ích thước tệp tin (byte)',
    example: 1024000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  size: number;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1620000000000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1620000000000,
  })
  @Expose()
  @Transform(({ value }) => value ? Number(value) : 0)
  updatedAt: number;
}
