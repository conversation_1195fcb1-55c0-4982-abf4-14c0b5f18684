import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import {
  WarehouseRepository,
  CustomFieldRepository
} from '@modules/business/repositories';
import {
  QueryWarehouseCustomFieldDto,
  WarehouseCustomFieldResponseDto,
  WarehouseCustomFieldDetailResponseDto,
  FieldDetailsDto
} from '../dto/warehouse';
import { PaginatedResult } from '@common/response';
import { plainToInstance } from 'class-transformer';
import { WarehouseValidationHelper } from '@modules/business/admin/helpers';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh của kho
 */
@Injectable()
export class AdminWarehouseCustomFieldService {
  private readonly logger = new Logger(AdminWarehouseCustomFieldService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly warehouseValidationHelper: WarehouseValidationHelper
  ) {}

  /**
   * Lấy danh sách trường tùy chỉnh của kho với phân trang
   * @param queryDto DTO truy vấn
   * @returns Danh sách trường tùy chỉnh của kho với phân trang
   */
  async findAll(queryDto: QueryWarehouseCustomFieldDto): Promise<PaginatedResult<WarehouseCustomFieldResponseDto>> {
    this.logger.log(`Lấy danh sách trường tùy chỉnh của kho với phân trang: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách trường tùy chỉnh từ repository
      // const [customFields, total] = await this.warehouseCustomFieldRepository.findAllWithPagination(queryDto); // WarehouseCustomField đã bị xóa
      const customFields = [];
      const total = 0;

      // Chuyển đổi dữ liệu thành DTO sử dụng constructor
      const items = customFields.map(cf => new WarehouseCustomFieldResponseDto(cf));

      // Trả về kết quả với phân trang
      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh của kho: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách trường tùy chỉnh của kho'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết trường tùy chỉnh của kho theo ID kho và ID trường
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Thông tin chi tiết trường tùy chỉnh của kho
   */
  async findOne(warehouseId: number, fieldId: number): Promise<WarehouseCustomFieldDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết trường tùy chỉnh của kho với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);

    // Kiểm tra kho có tồn tại không
    const warehouse = await this.warehouseRepository.findByWarehouseId_admin(warehouseId);
    this.warehouseValidationHelper.validateWarehouseExists(warehouse);

    // Lấy thông tin chi tiết trường tùy chỉnh của kho
    // const customFieldDetail = await this.warehouseCustomFieldRepository.findDetailByWarehouseIdAndFieldId(warehouseId, fieldId); // WarehouseCustomField đã bị xóa
    const customFieldDetail = null;

    // WarehouseCustomField đã bị xóa hoàn toàn - trả về lỗi không tìm thấy
    throw new AppException(
      ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
      `Không tìm thấy trường tùy chỉnh với ID ${fieldId} cho kho với ID ${warehouseId} - WarehouseCustomField đã bị xóa`
    );
  }
}
