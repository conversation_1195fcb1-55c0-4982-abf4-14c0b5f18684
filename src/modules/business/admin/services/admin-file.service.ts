import { Injectable, Logger } from '@nestjs/common';
import { FileRepository, FolderRepository } from '@modules/business/repositories';
import { FileQueryDto, FileResponseDto, FileDetailResponseDto } from '../dto/file';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { FILE_ERROR_CODES } from '../exceptions/file.exception';
import { plainToInstance } from 'class-transformer';
import { FileValidationHelper } from '../helpers/file-validation.helper';
import { FileHelper } from '../helpers/file-helper';

/**
 * Service xử lý các thao tác liên quan đến file cho admin
 */
@Injectable()
export class AdminFileService {
  private readonly logger = new Logger(AdminFileService.name);

  constructor(
    private readonly fileRepository: FileRepository,
    private readonly folderRepository: FolderRepository,
    private readonly fileValidationHelper: FileValidationHelper,
    private readonly fileHelper: FileHelper,
  ) {}

  /**
   * Lấy danh sách file với phân trang và tìm kiếm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách file với phân trang
   */
  async findAll(queryDto: FileQueryDto): Promise<PaginatedResult<FileResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách file với tham số: ${JSON.stringify(queryDto)}`);

      const { page, limit, search, folderId, sortBy, sortDirection } = queryDto;

      // Lấy danh sách file từ repository
      const result = await this.fileRepository.findAllAdmin({
        page,
        limit,
        search,
        folderId,
        sortBy,
        sortDirection,
      });

      // Chuyển đổi sang DTO
      const items = result.items.map(file => plainToInstance(FileResponseDto, file, { excludeExtraneousValues: true }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`method: ${this.findAll.name}`);
      throw new AppException(
        FILE_ERROR_CODES.FILE_FIND_FAILED,
        'Lỗi khi lấy danh sách file',
      );
    }
  }

  /**
   * Lấy chi tiết file theo ID
   * @param id ID của file
   * @returns Chi tiết file
   */
  async findById(id: number): Promise<FileDetailResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết file với ID: ${id}`);

      // Lấy chi tiết file từ repository
      const file = await this.fileRepository.findByIdAdmin(id);

      // Log dữ liệu file để kiểm tra
      this.logger.log(`Dữ liệu file: ${JSON.stringify(file)}`);

      // Kiểm tra file tồn tại
      this.fileValidationHelper.validateFileExists(file);

      // Lấy thông tin thư mục chứa file
      const folder = await this.folderRepository.findById_admin(file.folderId);
      if (!folder) {
        this.logger.warn(`Không tìm thấy thư mục với ID: ${file.folderId} cho file ID: ${id}`);
      } else {
        this.logger.log(`Dữ liệu folder: ${JSON.stringify(folder)}`);
      }

      // Tạo đối tượng chi tiết file
      const fileDetail = {
        id: file.id,
        name: file.name,
        folder: folder ? {
          id: folder.id,
          name: folder.name,
          path: folder.path,
          root: folder.root
        } : null,
        size: file.size,
        storageKey: file.storageKey,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
      };

      // Chuyển đổi sang DTO
      return plainToInstance(FileDetailResponseDto, fileDetail, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết file: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`method: ${this.findById.name}`);
      throw new AppException(
        FILE_ERROR_CODES.FILE_DETAIL_FAILED,
        'Lỗi khi lấy chi tiết file',
      );
    }
  }
}
