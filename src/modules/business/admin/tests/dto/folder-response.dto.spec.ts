import { plainToInstance } from 'class-transformer';
import { FolderResponseDto } from '../../dto/folder/folder-response.dto';

describe('FolderResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của FolderResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Documents',
      parentId: 3,
      userId: 42,
      path: '/Documents',
      root: 1,
      createdAt: 1620000000000,
      updatedAt: 1620000000000,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Documents');
    expect(dto.parentId).toBe(3);
    expect(dto.userId).toBe(42);
    expect(dto.path).toBe('/Documents');
    expect(dto.root).toBe(1);
    expect(dto.createdAt).toBe(1620000000000);
    expect(dto.updatedAt).toBe(1620000000000);
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FolderResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Documents',
      userId: 42
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Documents');
    expect(dto.userId).toBe(42);
    expect(dto.parentId).toBeUndefined();
    expect(dto.path).toBeUndefined();
    expect(dto.root).toBeUndefined();
    expect(dto.createdAt).toBeUndefined();
    expect(dto.updatedAt).toBeUndefined();
  });

  it('nên chuyển đổi plain object với parentId null thành instance của FolderResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      name: 'Root Folder',
      parentId: null,
      userId: 42,
      path: '/',
      root: null,
      createdAt: 1620000000000,
      updatedAt: 1620000000000
    };

    // Act
    const dto = plainToInstance(FolderResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FolderResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.name).toBe('Root Folder');
    expect(dto.parentId).toBeNull();
    expect(dto.userId).toBe(42);
    expect(dto.path).toBe('/');
    expect(dto.root).toBeNull();
    expect(dto.createdAt).toBe(1620000000000);
    expect(dto.updatedAt).toBe(1620000000000);
  });

  it('nên chuyển đổi mảng plain object thành mảng instance của FolderResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        name: 'Documents',
        parentId: null,
        userId: 42,
        path: '/Documents',
        root: 1,
        createdAt: 1620000000000,
        updatedAt: 1620000000000
      },
      {
        id: 2,
        name: 'Images',
        parentId: 1,
        userId: 42,
        path: '/Documents/Images',
        root: 1,
        createdAt: 1620000000000,
        updatedAt: 1620000000000
      }
    ];

    // Act
    const dtos = plainToInstance(FolderResponseDto, plainArray);

    // Assert
    expect(Array.isArray(dtos)).toBe(true);
    expect(dtos.length).toBe(2);
    expect(dtos[0]).toBeInstanceOf(FolderResponseDto);
    expect(dtos[1]).toBeInstanceOf(FolderResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[1].id).toBe(2);
    expect(dtos[0].parentId).toBeNull();
    expect(dtos[1].parentId).toBe(1);
  });
});
