# Tài liệu API GHN (Giao hàng Nhanh)

Các API GHN yêu cầu gửi header **Token** (chuỗi định danh) để xác thực và **ShopID** (ID cửa hàng) trong hầu hết các yêu cầu. Dữ liệu Token và ShopID được cấp khi đăng ký tài khoản và tạo cửa hàng trên hệ thống GHN.

## Lấy <PERSON>ken (Xác thực)

### Đăng ký tài khoản và lấy Token, Client_ID, ShopID  
- Đăng ký tài khoản tại trang GHN (môi trường sandbox hoặc production).  
- <PERSON><PERSON><PERSON> nhập (quản trị viên) và tạo **cửa hàng** (Shop). Tại trang quản lý cửa hàng, GHN cung cấp **Token**, **Client_ID** (ID đại lý) và **ShopID**.  
- <PERSON>ưu <PERSON> và ShopID để gửi kèm trong header của các API khác. Ví dụ: `Token: <mã-token>`, `ShopId: <mã-shop>`.

## Đơn hàng (Order)

Các API trong nhóm Đơn hàng quản lý tạo, cập nhật, hủy, in và tra cứu đơn hàng. Tất cả yêu cầu đều gửi header `Token` và `ShopId`.

### Cập nhật đơn hàng (Update Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/update`  
  - *Môi trường thử nghiệm:* `https://dev-online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/update`  
  - *Môi trường sản xuất:* `https://online-gateway.ghn.vn/shiip/public-api/v2/shipping-order/update`  
- **Mô tả:** Cập nhật thông tin của đơn hàng đã tạo. Chỉ sử dụng khi đơn hàng ở trạng thái chưa giao hàng.  
- **Tham số (gửi trong body JSON):**  
  - `order_code` (String, **bắt buộc**) – Mã đơn hàng GHN (mã theo dõi).  
  - `from_name`, `from_phone`, `from_address`, `from_ward_code`, `from_district_id` – Thông tin người gửi (nếu thay đổi).  
  - `to_name`, `to_phone`, `to_address`, `to_ward_code`, `to_district_id` – Thông tin người nhận (nếu thay đổi).  
  - `return_phone`, `return_address`, `return_ward_code`, `return_district_id` – Thông tin liên hệ trả hàng (nếu có).  
  - `client_order_code` (String) – Mã đơn hàng từ hệ thống khách hàng (nếu thay đổi).  
  - `cod_amount` (Int) – Số tiền thu hộ (nếu thay đổi).  
  - `content` (String) – Mô tả đơn hàng.  
  - `weight`, `length`, `width`, `height` – Kích thước, khối lượng mới (gram, cm).  
  - `pick_station_id` (Int) – Nếu người giao nhận không đến địa chỉ shop, ID bưu cục lấy hàng.  
  - `insurance_value` (Int) – Giá trị bảo hiểm (nếu cập nhật).  
  - `coupon` (String) – Mã giảm giá (nếu có).  
  - `payment_type_id` (Int) – Ai trả phí vận chuyển (`1`=Người gửi, `2`=Người nhận).  
  - `note` (String) – Ghi chú cho người giao hàng.  
  - `required_note` (String) – Ghi chú đơn hàng: `CHOTHUHANG`, `CHOXEMHANGKHONGTHU`, `KHONGCHOXEMHANG`.  
  - `pick_shift` (Int) – Ca lấy hàng (xem API **Pick Shift**).  
  - `items` (Array) – Danh sách hàng hóa (đối với dịch vụ truyền thống). Mỗi phần tử gồm `name` (tên sp), `code` (mã sp), `quantity`, `price`, và (nếu truyền thống) `length`, `width`, `height`, `weight`, `category.level1/2/3`.  
- **Ví dụ request:**  

  ```json
  {
    "order_code": "5F5NH3LN",
    "from_name": "Nguyễn Văn A",
    "from_phone": "0912345678",
    "to_name": "Trần Thị B",
    "to_phone": "0987654321",
    "to_address": "123 Đường XYZ, Quận 1, TP.HCM",
    "to_ward_code": "20107",
    "to_district_id": 1442,
    "cod_amount": 500000,
    "content": "Hàng điện tử",
    "weight": 1000,
    "length": 20,
    "width": 15,
    "height": 10,
    "note": "Gọi trước khi giao"
  }
  ```  
- **Ví dụ response thành công (200):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": null
  }
  ```  

### Hủy đơn hàng (Cancel Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/switch-status/cancel`  
  - *Test:* `https://dev-online-gateway.ghn.vn/shiip/public-api/v2/switch-status/cancel`  
  - *Prod:* `https://online-gateway.ghn.vn/shiip/public-api/v2/switch-status/cancel`  
- **Mô tả:** Yêu cầu hủy đơn hàng GHN (trả hàng về kho). Sau khi hủy, đơn chuyển trạng thái “cancel”.  
- **Tham số:**  
  - `order_codes` (Array of Strings, **bắt buộc**) – Danh sách mã đơn GHN cần hủy.  
- **Ví dụ request:**  
  ```json
  { "order_codes": ["5F5NH3LN"] }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "order_code": "5F5NH3LN",
        "result": true,
        "message": "OK"
      }
    ]
  }
  ```  

### Đề nghị trả hàng (Return Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/switch-status/return`  
  - *Test:* `.../switch-status/return`  
  - *Prod:* `.../switch-status/return`  
- **Mô tả:** Đề nghị trả lại đơn hàng (ví dụ khách không nhận hàng). Đơn chuyển trạng thái “storage”.  
- **Tham số:**  
  - `order_codes` (Array of Strings) – Danh sách mã đơn cần trả.  
- **Ví dụ response thành công:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"order_code": "5F5NH3LN","result": true,"message":"OK"}
    ]
  }
  ```  

### Tạo token in đơn hàng (Print Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/a5/gen-token`  
  - *Test:* `https://dev-online-gateway.ghn.vn/shiip/public-api/v2/a5/gen-token`  
  - *Prod:* `https://online-gateway.ghn.vn/shiip/public-api/v2/a5/gen-token`  
- **Mô tả:** Tạo token để in vận đơn (khổ A5) hoặc kiện.  
- **Tham số:**  
  - `order_codes` (Array of Strings, **bắt buộc**) – Danh sách mã đơn GHN cần in vận đơn.  
- **Ví dụ request:**  
  ```json
  { "order_codes": ["5F5NH3LN"] }
  ```  
- **Ví dụ response:**  
  ```json
  { "code":200, "message":"Success", "data":"<token_in_van_don>" }
  ```  
  Sử dụng token trả về để gọi URL in GHN (khổ A5, ZF, Thermal) theo hướng dẫn GHN (không nằm trong phạm vi API này).

### Thông tin đơn hàng (Order Info)  
- **Method:** `POST` (có thể dùng `GET`)  
- **Endpoint:** `shiip/public-api/v2/shipping-order/detail`  
  - *Test:* `.../detail`  
  - *Prod:* `.../detail`  
- **Mô tả:** Lấy chi tiết thông tin đơn hàng. Trả về đầy đủ thông tin người gửi, người nhận, cước phí, trạng thái…  
- **Tham số:**  
  - `order_code` (String, **bắt buộc**) – Mã đơn hàng GHN.  
- **Ví dụ request:**  
  ```json
  { "order_code": "5F5NH3LN" }
  ```  
- **Ví dụ response (trích):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "order_code": "5F5NH3LN",
        "status": "return",
        "from_name": "Nguyen Van A",
        "to_name": "Tran Thi B",
        "weight": 1000,
        "cod_amount": 500000,
        "fee": 30000,
        // ... nhiều trường khác ...
      }
    ]
  }
  ```  
  *Ghi chú:* Mảng `data` chứa một phần tử là thông tin đơn hàng. Các trường quan trọng gồm trạng thái (`status`), cước phí (`fee`), ngày tạo, ca lấy, v.v.  

### Giao lại (Delivery Again)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/switch-status/storing`  
  - *Test:* `.../switch-status/storing`  
  - *Prod:* `.../switch-status/storing`  
- **Mô tả:** Yêu cầu giao lại những đơn bị chuyển sang trạng thái “storage” (khách xin giao lại trong thời hạn hợp đồng). Đơn chuyển về “storage” và chờ vận chuyển.  
- **Tham số:**  
  - `order_codes` (Array of Strings) – Danh sách mã đơn cần giao lại.  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"order_code":"5F5NH3LN","result":true,"message":"OK"}
    ]
  }
  ```  

### Cập nhật tiền thu hộ (Update COD)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/updateCOD`  
  - *Test:* `.../shipping-order/updateCOD`  
  - *Prod:* `.../shipping-order/updateCOD`  
- **Mô tả:** Cập nhật số tiền thu hộ của đơn hàng GHN.  
- **Tham số:**  
  - `order_code` (String, **bắt buộc**) – Mã đơn GHN.  
  - `cod_amount` (Int, **bắt buộc**) – Giá trị thu hộ mới (tối đa 5.000.000).  
- **Ví dụ request:**  
  ```json
  { "order_code": "5F5NH3LN", "cod_amount": 100000 }
  ```  
- **Ví dụ response:**  
  ```json
  { "code": 200, "message": "Success", "data": null }
  ```  

### Lấy thông tin bưu cục (Get Station)  
- **Method:** `GET` (cũng chấp nhận `POST`)  
- **Endpoint:** `shiip/public-api/v2/station/get`  
  - *Test:* `https://dev-online-gateway.ghn.vn/shiip/public-api/v2/station/get`  
  - *Prod:* `https://online-gateway.ghn.vn/shiip/public-api/v2/station/get`  
- **Mô tả:** Lấy danh sách bưu cục GHN gần địa chỉ cho trước. Thường dùng để xác định `station_id` khi tạo đơn.  
- **Tham số:** (gửi body JSON)  
  - `district_id` (Int, **bắt buộc**) – ID quận/huyện nhận hàng. Xem API **Get District**.  
  - `ward_code` (String) – Mã phường/xã nhận hàng. Xem API **Get Ward**.  
  - `offset` (Int) – Mặc định 0.  
  - `limit` (Int) – Số lượng bản ghi tối đa (không quá 1000).  
- **Ví dụ request:**  
  ```json
  {
    "district_id": 1442,
    "ward_code": "20101",
    "offset": 0,
    "limit": 10
  }
  ```  
- **Ví dụ response (trích):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "locationId": 1888,
        "locationName": "Bưu cục GHN Quận 1",
        "address": "123 Đường ABC, Quận 1, TP.HCM",
        "latitude": 10.123,
        "longitude": 106.123,
        // ...
      },
      // ...
    ]
  }
  ```  
  Trường trả về `data` là mảng các bưu cục, gồm `locationId` (ID bưu cục), `locationName`, `address`, tọa độ, v.v.

### Tính thời gian giao dự kiến (Calculate expected delivery time)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/leadtime`  
  - *Test:* `.../shipping-order/leadtime`  
  - *Prod:* `.../shipping-order/leadtime`  
- **Mô tả:** Tính toán thời gian giao hàng dự kiến dựa trên thông tin địa chỉ và dịch vụ.  
- **Tham số:**  
  - `from_district_id` (Int, **bắt buộc**) – ID quận/huyện lấy hàng (xem API Get District).  
  - `from_ward_code` (String) – Mã phường/xã lấy hàng (xem API Get Ward).  
  - `to_district_id` (Int, **bắt buộc**) – ID quận/huyện giao hàng.  
  - `to_ward_code` (String, **bắt buộc**) – Mã phường/xã giao hàng.  
  - `service_id` (Int, **bắt buộc**) – ID dịch vụ GHN (xem API Get Service). Nếu biết `service_type_id` thì không cần.  
- **Ví dụ request:**  
  ```json
  {
    "from_district_id": 1750,
    "from_ward_code": "1A0706",
    "to_district_id": 1750,
    "to_ward_code": "511110",
    "service_id": 53320
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "leadtime": 1593187200,
      "order_date": 1592981718
    }
  }
  ```  
  `leadtime` là timestamp thời gian giao dự kiến. `order_date` là timestamp thời gian bắt đầu (thường là thời điểm gọi API).

### Tạo đơn hàng (Create Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/create`  
  - *Test:* `.../shipping-order/create`  
  - *Prod:* `.../shipping-order/create`  
- **Mô tả:** Tạo một đơn hàng mới trên hệ thống GHN với đầy đủ thông tin gửi/nhận, kích thước, khối lượng, cước phí, v.v.  
- **Tham số (gửi JSON):**  
  - `shop_id` (Int, **bắt buộc**) – ID cửa hàng (ShopID).  
  - `to_name`, `to_phone`, `to_address` (String, **bắt buộc**) – Thông tin người nhận.  
  - `to_ward_code` (String, **bắt buộc**) – Mã phường/xã người nhận (xem API Get Ward).  
  - `to_district_id` (Int, **bắt buộc**) – ID quận/huyện người nhận (xem API Get District).  
  - `from_name`, `from_phone`, `from_address` (String, **bắt buộc**) – Thông tin người gửi (Shop). Nếu không truyền, GHN mặc định lấy theo thông tin của `shop_id`.  
  - `from_ward_name`, `from_district_name`, `from_provice_name` (String) – Có thể thay thế `from_ward_code`, `from_district_id` bằng tên phường/quận/tỉnh.  
  - `return_phone`, `return_address`, `return_district_id`, `return_ward_code` – Thông tin liên hệ trả hàng (nếu có).  
  - `client_order_code` (String) – Mã đơn hàng từ hệ thống khách (để đối soát). (Tự động trả về đơn có mã này nếu trùng)  
  - `cod_amount` (Int) – Số tiền thu hộ (tối đa 50.000.000).  
  - `content` (String) – Nội dung/tên hàng hóa (tối đa 2000 ký tự).  
  - `weight` (Int, **bắt buộc**) – Khối lượng (gram, tối đa 50.000g).  
  - `length`, `width`, `height` (Int, **bắt buộc**) – Kích thước (cm, tối đa 200cm).  
  - `pick_station_id` (Int) – Nếu người giao nhận đến lấy hàng tại bưu cục, ID bưu cục lấy hàng.  
  - `insurance_value` (Int) – Giá trị khai báo bồi thường (tối đa 5.000.000).  
  - `coupon` (String) – Mã giảm giá (nếu có).  
  - `service_type_id` (Int, **bắt buộc**) – Loại dịch vụ (e-commerce = 2, truyền thống = 5). Mặc định 2.  
  - `service_id` (Int) – ID dịch vụ cụ thể (xem API Get Service) nếu muốn cố định (nếu bỏ trống dùng `service_type_id`).  
  - `payment_type_id` (Int, **bắt buộc**) – Ai trả cước (`1`=Shop/Người gửi, `2`=Người nhận).  
  - `note` (String) – Ghi chú cho người giao hàng (ví dụ: “Gọi trước khi đến”).  
  - `required_note` (String, **bắt buộc**) – Lưu ý giao hàng: `CHOTHUHANG`, `CHOXEMHANGKHONGTHU`, `KHONGCHOXEMHANG`.  
  - `pick_shift` (Int) – Ca lấy hàng (xem API **Pick Shift**).  
  - `items` (Array, *nếu* dịch vụ truyền thống hoặc muốn liệt kê chi tiết hàng): danh sách sản phẩm gồm `name` (tên, **bắt buộc**), `code` (mã), `quantity` (số lượng, **bắt buộc**), `price` (giá tiền), và nếu dịch vụ truyền thống bắt buộc thêm `length`, `width`, `height`, `weight` của từng món.  
- **Ví dụ request (rút gọn):**  
  ```json
  {
    "shop_id": 885,
    "to_name": "Khách Hàng A",
    "to_phone": "0912345678",
    "to_address": "72 Thành Thái, Phường 14, Quận 10, TP.HCM",
    "to_ward_code": "20107",
    "to_district_id": 1442,
    "from_name": "Cửa Hàng B",
    "from_phone": "0987654321",
    "from_address": "17 Đồng Đen, Phường 1, Quận Tân Bình, TP.HCM",
    "from_ward_name": "Phường 1",
    "from_district_name": "Tân Bình",
    "from_provice_name": "Hồ Chí Minh",
    "client_order_code": "DH123456",
    "cod_amount": 200000,
    "content": "Quần áo thời trang",
    "weight": 1500,
    "length": 30,
    "width": 20,
    "height": 10,
    "insurance_value": 500000,
    "service_type_id": 2,
    "payment_type_id": 2,
    "required_note": "CHOXEMHANGKHONGTHU",
    "note": "Giao giờ hành chính",
    "pick_shift": 2,
    "items": [
      {"name": "Áo sơ mi", "code": "ASM01", "quantity": 2, "price": 150000}
    ]
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "order_code": "FFFNL9HH",
      "total_fee": 33000,
      "fee": {
        "main_service": 22000,
        "insurance": 11000,
        "coupon": 0,
        "station_do": 0,
        "station_pu": 0,
        "return": 0,
        "r2s": 0
      },
      "expected_delivery_time": "2020-06-03T16:00:00Z",
      // ...
    }
  }
  ```  
  `order_code` là mã đơn GHN mới tạo, `total_fee` là tổng cước phải trả.

### Thông tin đơn hàng theo mã KH (Order Info by ClientOrderCode)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/detail-by-client-code`  
- **Mô tả:** Tương tự **Order Info**, nhưng tìm theo `client_order_code` thay vì mã GHN.  
- **Tham số:**  
  - `client_order_code` (String, **bắt buộc**) – Mã đơn do hệ thống bạn cấp (trường hợp gọi tạo đơn với `client_order_code`).  
- **Ví dụ request:**  
  ```json
  { "client_order_code": "DH123456" }
  ```  
- **Ví dụ response:** (tương tự API Order Info, trả thông tin đơn hàng tương ứng).

### Ca lấy hàng (Pick Shift)  
- **Method:** `GET` (hoặc `POST`)  
- **Endpoint:** `shiip/public-api/v2/shift/date`  
- **Mô tả:** Lấy danh sách ca lấy hàng sắp tới. Dùng để gán trường `pick_shift` khi tạo đơn.  
- **Tham số:** (đã gửi trong header)  
  - `Token` (trong header) – Chuỗi xác thực.  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"id": 2, "title": "Ca lấy 12-03-2021 (12h00 - 18h00)", "from_time": 43200, "to_time": 64800},
      {"id": 3, "title": "Ca lấy 13-03-2021 (7h00 - 12h00)", "from_time": 111600, "to_time": 129600},
      // ...
    ]
  }
  ```  
  Mỗi mục gồm `id` (ID ca), `title`, `from_time`/`to_time` (thời gian tính từ 00:00).  

### Xem trước đơn hàng (Preview Order)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/preview`  
- **Mô tả:** Tính toán và xem trước thông tin đơn hàng (mã đơn, cước phí, thời gian giao dự kiến) **không tạo đơn**. Giúp kiểm tra trước khi tạo đơn thực.  
- **Tham số:** Tương tự **Create Order** nhưng không có `shop_id` vì dùng header.  
  - Gồm các trường như `to_name`, `to_phone`, `to_address`, `to_ward_code`, `to_district_id`, `cod_amount`, `content`, `weight`, `length`, `width`, `height`, `insurance_value`, `service_type_id`/`service_id`, `payment_type_id`, `required_note`, v.v. (như phần Tạo đơn).  
- **Ví dụ request:**  
  ```json
  {
    "to_name": "Khách Hàng A",
    "to_phone": "0912345678",
    "to_address": "72 Thành Thái, P.14, Q.10, TP.HCM",
    "to_ward_code": "20107",
    "to_district_id": 1442,
    "cod_amount": 100000,
    "content": "Sách giáo khoa",
    "weight": 500,
    "length": 10,
    "width": 15,
    "height": 5,
    "service_type_id": 2,
    "payment_type_id": 2,
    "required_note": "KHONGCHOXEMHANG"
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "order_code": "",
      "total_fee": "33000",
      "fee": {
        "main_service": 22000,
        "insurance": 11000,
        // ...
      },
      "expected_delivery_time": "2020-06-03T16:00:00Z"
    }
  }
  ```  
  `order_code` là giá trị trống (`""`) vì chưa tạo đơn, nhưng trả về `total_fee` và cước chi tiết.

## Tính phí (Calculate Fee)

### Cước đơn hàng (Fee of Order Info)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/soc`  
- **Mô tả:** Lấy chi tiết các loại phí đã áp cho đơn hàng GHN hiện tại.  
- **Tham số:**  
  - `shop_id` (Int, **bắt buộc**) – ID cửa hàng.  
  - `order_code` (String, **bắt buộc**) – Mã đơn GHN.  
- **Ví dụ request:**  
  ```json
  { "order_code": "5F5NH3LN" }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "_id": "...",
      "order_code": "5F5NH3LN",
      "detail": {
        "main_service": 29700,
        "insurance": 11000,
        "station_do": 0,
        "station_pu": 0,
        "return": 22000,
        "r2s": 0,
        "coupon": 0
      },
      "payment": [
        {"value": 40700, "payment_type": 2, "paid_date": "..."},
        {"value": 22000, "payment_type": 2, "paid_date": "..."}
      ],
      "cod_collect_date": null,
      "transaction_id": "...",
      // ...
    }
  }
  ```  
  `detail` cho biết các loại phí: cước dịch vụ chính (`main_service`), bảo hiểm, trả hàng, v.v.; `payment` là lịch sử thanh toán.

### Tính cước (Calculate Fee)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/fee`  
- **Mô tả:** Tính toán cước vận chuyển trước khi tạo đơn.  
- **Tham số:**  
  - `shop_id` (Int, **bắt buộc**) – ID cửa hàng.  
  - `service_id` (Int) – ID dịch vụ (xem Get Service) (nếu đã biết).  
  - `service_type_id` (Int) – Loại dịch vụ (2: E-Commerce, 5: Truyền thống). Nếu nhập `service_type_id`, có thể bỏ `service_id`. Mặc định 2.  
  - `insurance_value` (Int) – Giá trị khai báo bảo hiểm (mặc định 0, tối đa 5.000.000).  
  - `coupon` (String) – Mã giảm giá (nếu có).  
  - `cod_failed_amount` (Int) – Số tiền khi giao hàng thất bại (nếu có).  
  - `from_district_id` (Int) – ID quận/huyện gửi (mặc định lấy từ ShopID).  
  - `from_ward_code` (String) – Mã phường/xã gửi (mặc định từ ShopID).  
  - `to_district_id` (Int, **bắt buộc**) – ID quận/huyện nhận hàng.  
  - `to_ward_code` (String, **bắt buộc**) – Mã phường/xã nhận hàng.  
  - `weight`, `length`, `width`, `height` – Kích thước/khối lượng kiện hàng (gram/cm).  
  - `cod_value` (Int) – Số tiền cần thu hộ (tối đa 5.000.000, mặc định 0).  
- **Ví dụ request:**  
  ```json
  {
    "from_district_id": 1454,
    "from_ward_code": "21211",
    "service_id": 53320,
    "insurance_value": 1000000,
    "to_district_id": 1442,
    "to_ward_code": "20107",
    "weight": 2000,
    "length": 30,
    "width": 20,
    "height": 15,
    "cod_value": 100000
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "total": 36300,
      "service_fee": 36300,
      "insurance_fee": 0,
      "pick_station_fee": 0,
      "coupon_value": 0,
      "r2s_fee": 0,
      "document_return": 0,
      "double_check": 0,
      "cod_fee": 0,
      "pick_remote_areas_fee": 0,
      "deliver_remote_areas_fee": 0,
      "cod_failed_fee": 0
    }
  }
  ```  
  Trường `total` là tổng cước, `service_fee` cước chính, `insurance_fee` phí bảo hiểm, v.v.

### Lấy dịch vụ (Get Service)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shipping-order/available-services`  
- **Mô tả:** Lấy danh sách dịch vụ GHN phù hợp với tuyến đường từ quận lấy đến quận giao.  
- **Tham số:**  
  - `shop_id` (Int, **bắt buộc**) – ID cửa hàng.  
  - `from_district` (Int, **bắt buộc**) – ID quận/huyện lấy hàng.  
  - `to_district` (Int, **bắt buộc**) – ID quận/huyện giao hàng.  
- **Ví dụ request:**  
  ```json
  {
    "shop_id": 885,
    "from_district": 1447,
    "to_district": 1442
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"service_id": 53319, "short_name": "Nhanh", "service_type_id": 1},
      {"service_id": 53320, "short_name": "Chuẩn", "service_type_id": 2},
      {"service_id": 53330, "short_name": "Rẻ", "service_type_id": 3}
    ]
  }
  ```  

## Cửa hàng (Store)

### Lấy danh sách cửa hàng (Get Store)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shop/all`  
- **Mô tả:** Lấy danh sách các cửa hàng (Shop) hiện có của khách hàng. Mỗi cửa hàng chứa thông tin địa chỉ lấy hàng.  
- **Tham số:**  
  - `offset` (Int) – Vị trí bắt đầu (mặc định 0).  
  - `limit` (Int) – Số lượng tối đa (<=200).  
  - `clientphone` (String) – Số điện thoại nhân viên (nếu lọc theo nhân viên).  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "last_offset": 60429,
      "shops": [
        {
          "_id": 59037,
          "name": "Kinh doanh Corgi",
          "phone": "0705099871",
          "address": "3 Đồng Đen, Tân Bình, TP.HCM",
          "ward_code": "21410",
          "district_id": 1455,
          "client_id": 2500055,
          "status": 1,
          // ...
        },
        // ...
      ]
    }
  }
  ```  

### Tạo cửa hàng mới (API Create Store)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shop/register`  
- **Mô tả:** Tạo mới một cửa hàng cho tài khoản.  
- **Tham số:**  
  - `district_id` (Int, **bắt buộc**) – ID quận/huyện (xem API Get District).  
  - `ward_code` (String, **bắt buộc**) – Mã phường/xã (xem Get Ward).  
  - `name` (String, **bắt buộc**) – Tên cửa hàng.  
  - `phone` (String, **bắt buộc**) – Số điện thoại cửa hàng.  
  - `address` (String, **bắt buộc**) – Địa chỉ chi tiết.  
- **Ví dụ request:**  
  ```json
  {
    "district_id": 1550,
    "ward_code": "420112",
    "name": "Cửa hàng XYZ",
    "phone": "0123456789",
    "address": "35 Đồng Đen, P.10, Q.3"
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": { "shop_id": 72359 }
  }
  ```  
  Trả về `shop_id` của cửa hàng vừa tạo.

## Địa chỉ (Address)

### Lấy quận/huyện (Get District)  
- **Method:** `GET` (hoặc `POST`)  
- **Endpoint:** `public-api/master-data/district`  
- **Mô tả:** Lấy danh sách quận/huyện theo Tỉnh/Thành phố.  
- **Tham số:**  
  - `province_id` (Int, **bắt buộc**) – ID Tỉnh/TP (xem API Get Province).  
- **Ví dụ request:**  
  ```json
  { "province_id": 202 }
  ```  
- **Ví dụ response (trích):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"DistrictID": 1442, "ProvinceID": 202, "DistrictName": "Quận 1", ...},
      {"DistrictID": 1443, "ProvinceID": 202, "DistrictName": "Quận 2", ...},
      // ...
    ]
  }
  ```  

### Lấy phường/xã (Get Ward)  
- **Method:** `GET` (có thể theo query `?district_id=...`)  
- **Endpoint:** `public-api/master-data/ward`  
- **Mô tả:** Lấy danh sách phường/xã theo quận/huyện.  
- **Tham số:**  
  - `district_id` (Int, **bắt buộc**) – ID quận/huyện (xem API Get District).  
- **Ví dụ request:** `GET https://.../ward?district_id=1566`  
- **Ví dụ response (trích):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"WardCode": 510101, "DistrictID": 1566, "WardName": "Phường Mỹ Bình", ...},
      {"WardCode": 510102, "DistrictID": 1566, "WardName": "Phường Mỹ Long", ...},
      // ...
    ]
  }
  ```  

### Lấy tỉnh/thành phố (Get Province)  
- **Method:** `GET`  
- **Endpoint:** `public-api/master-data/province`  
- **Mô tả:** Lấy danh sách các tỉnh/thành phố hỗ trợ.  
- **Tham số:** (không có, chỉ gửi `Token` header)  
- **Ví dụ response (trích):**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {"ProvinceID": 201, "ProvinceName": "Hà Nội", ...},
      {"ProvinceID": 202, "ProvinceName": "Hồ Chí Minh", ...},
      // ...
    ]
  }
  ```  

## Webhook

*Lưu ý:* Đây là các thông báo GHN gửi đến hệ thống của bạn (không phải API để gọi từ khách sang GHN). Khi cấu hình webhook tại GHN, hệ thống của bạn phải nhận `POST` từ GHN với định dạng JSON như sau:

- **Webhook phản hồi Ticket (Callback of Ticket):**  
  Khi có cập nhật trạng thái của Ticket, GHN sẽ gửi thông tin đến URL bạn cấu hình. JSON chứa các trường như:  
  - `Type`: Loại yêu cầu (ví dụ `"Tư vấn"`, `"Khiếu nại"`,...)  
  - `Status`: Trạng thái ticket (`1`=Đang xử lý, `2`=Chờ KH phản hồi, `3`=Hoàn thành)  
  - Thông tin khác: email, tên, số điện thoại khách (`C_Email`, `C_Name`, `C_Phone`), mã Ticket, nội dung,...  
  Bạn cần trả về HTTP 200 (đúng định dạng JSON) để xác nhận đã nhận được. Nếu trả về mã khác, GHN sẽ thử lại 10 lần.

- **Webhook trạng thái đơn hàng (Callback order status):**  
  Khi đơn hàng tạo mới hoặc thay đổi trạng thái trên GHN, GHN sẽ `POST` về URL webhook của bạn dữ liệu sau:  
  ```json
  {
    "Type": "create" | "switch_status" | "update_weight" | "update_cod" | "update_fee",
    "OrderCode": "Z82BS",
    "ShopID": 81558,
    "ClientOrderCode": "DH123456",
    "Status": "ready_to_pick" | "picked" | ...,
    "Description": "Tạo đơn hàng",
    "Weight": 10, "Length": 10, "Width": 10, "Height": 10,
    "CODAmount": 3000000,
    "Fee": {
      "MainService": 53900,
      "Insurance": 17500,
      "Return": 0,
      "R2S": 0,
      "StationDO": 0,
      "StationPU": 0,
      "CODFailedFee": 0,
      "CODFee": 0,
      "Coupon": 0
    },
    "TotalFee": 71400,
    "Time": "2021-11-11T03:52:50.158Z",
    // ...
  }
  ```  
  Bạn cần trả về HTTP 200 để xác nhận. Nếu không, GHN sẽ tự động gửi lại 10 lần.

## Ticket (Hỗ trợ)

### Tạo Ticket (Create Ticket)  
- **Method:** `POST`  
- **Endpoint:** `public-api/ticket/create`  
- **Mô tả:** Tạo yêu cầu hỗ trợ (ticket) liên quan đến một đơn hàng GHN.  
- **Tham số:**  
  - `order_code` (String, **bắt buộc**) – Mã đơn GHN liên quan đến Ticket.  
  - `category` (String, **bắt buộc**) – Chủ đề yêu cầu: `"Tư vấn"`, `"Hối Giao/Lấy/Trả hàng"`, `"Thay đổi thông tin"`, `"Khiếu nại"`.  
  - `description` (String, **bắt buộc**) – Nội dung yêu cầu.  
  - `c_email` (String) – Email khách hàng.  
  - `attachments` (File) – Tệp đính kèm (Hình, Excel, CSV) nếu có.  
- **Ví dụ sử dụng curl:**  
  ```
  curl --location --request POST 'https://dev-online-gateway.ghn.vn/shiip/public-api/ticket/create' \
       --header 'Token: <token>' \
       -F 'order_code=5ENLKKHD' \
       -F 'category=Tư vấn' \
       -F 'description=Tạo yêu cầu test' \
       -F 'c_email=<EMAIL>'
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "id": 1605822,
      "order_code": "5ENLKKHD",
      "type": "Tư vấn",
      "description": "Tạo yêu cầu test",
      "status": "Đang xử lý",
      "status_id": 1,
      "created_at": "2020-05-29T15:24:15Z",
      // ...
    }
  }
  ```  

### Phản hồi Ticket (Create Feedback of Ticket)  
- **Method:** `POST`  
- **Endpoint:** `public-api/ticket/reply`  
- **Mô tả:** Gửi phản hồi (comment) vào một ticket đã tạo.  
- **Tham số:**  
  - `ticket_id` (Int, **bắt buộc**) – ID của Ticket (được trả khi tạo Ticket).  
  - `description` (String, **bắt buộc**) – Nội dung phản hồi.  
  - `attachments` (File) – Tệp đính kèm nếu có.  
- **Ví dụ request (curl):**  
  ```
  curl --location --request POST 'https://dev-online-gateway.ghn.vn/shiip/public-api/ticket/reply' \
       --header 'Token: <token>' \
       --form 'ticket_id=1605822' \
       --form 'description=Tech test' \
       --form 'attachments=@/path/to/file.png'
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "body": "Tech test",
      "from_email": "\"Email Support\" <<EMAIL>>",
      "created_at": "2020-05-29T15:42:13Z",
      // ...
    }
  }
  ```  

### Danh sách Ticket (Get Ticket List)  
- **Method:** `GET` (cũng có thể `POST`)  
- **Endpoint:** `public-api/ticket/index`  
- **Mô tả:** Lấy danh sách các ticket của cửa hàng (theo ShopID).  
- **Tham số:** (trong header) `Token`, `Shopid` (ID cửa hàng).  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": [
      {
        "id": 1605822,
        "order_code": "5ENLKKHD",
        "type": "Tư vấn",
        "description": "Tạo yêu cầu test",
        "status": "Hoàn thành",
        "status_id": 1,
        "created_at": "2020-05-29T15:24:15Z",
        "updated_at": "2020-05-29T15:24:15Z",
        // ...
      },
      // ...
    ]
  }
  ```  
- **Lưu ý:** API **Get Ticket** riêng lẻ (theo `ticket_id`) không có trong tài liệu công khai.  

## Thông tin thêm (More Information)

### Mã lỗi vận chuyển (Fail Code - Reason)  
Tài liệu liệt kê mã lỗi và giải thích bằng tiếng Việt trong quá trình lấy hoặc giao hàng. Ví dụ:

- **Lấy hàng thất bại:**  
  - `GHN-PFA1A0`: Người gửi hẹn lại ngày lấy hàng.  
  - `GHN-PFA2A2`: Thông tin lấy hàng sai (địa chỉ/SĐT).  
  - ...  
- **Giao hàng thất bại:**  
  - `GHN-DFC1A0`: Người nhận hẹn lại ngày giao.  
  - `GHN-DFC1A2`: Không liên lạc được người nhận/Chặn số.  
  - ...  

(Chi tiết các mã lỗi đầy đủ xem tài liệu GHN.)

### Trạng thái đơn hàng (List Of Shipping Status)  
Danh sách trạng thái của đơn hàng GHN và mô tả:

| Trạng thái               | Mô tả                                 |
|--------------------------|---------------------------------------|
| `ready_to_pick`          | Đơn vừa được tạo (chờ lấy hàng)        |
| `picking`                | Shipper đang đến lấy hàng             |
| `cancel`                 | Đơn bị hủy                            |
| `money_collect_picking`  | Shipper tương tác với người gửi        |
| `picked`                 | Shipper đã lấy hàng                    |
| `storing`                | Hàng đã về kho GHN (đang lưu kho)       |
| `transporting`           | Hàng đang vận chuyển (chuyển kho)      |
| `sorting`                | Hàng đang phân loại ở kho             |
| `delivering`             | Shipper đang giao hàng                |
| `money_collect_delivering`| Shipper tương tác với người nhận     |
| `delivered`              | Đã giao thành công                   |
| `delivery_fail`          | Giao không thành công                |
| `waiting_to_return`      | Đơn đang chờ giao lại                |
| `return`                 | Hàng đang trả về người gửi             |

## Đối tác (Affiliate)

Đây là các API liên quan cho đối tác (affiliate) của GHN, bao gồm tạo cửa hàng/nhân viên qua OTP.

### Thêm nhân viên vào cửa hàng qua OTP (Add Staff to Store by OTP)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shop/affiliateCreateWithShop`  
- **Mô tả:** Thêm một khách hàng đối tác vào cửa hàng đã có sẵn, cần xác thực OTP.  
- **Tham số:**  
  - `phone` (String, **bắt buộc**) – Số điện thoại của khách hàng đối tác.  
  - `otp` (String, **bắt buộc**) – Mã OTP nhận được từ API Lấy OTP.  
  - `shop_id` (Int, **bắt buộc**) – ID cửa hàng.  
- **Ví dụ request:**  
  ```json
  {
    "phone": "0705099871",
    "otp": "831973",
    "shop_id": 59037
  }
  ```  
- **Ví dụ response:**  
  ```json
  { "code": 200, "message": "Success", "data": null }
  ```  
  Hoặc lỗi nếu user đã tồn tại.

### Tạo cửa hàng qua OTP (Create Store by OTP)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shop/affiliateCreate`  
- **Mô tả:** Đối tác thêm mới một cửa hàng cho mình bằng OTP (không phải ShopID của doanh nghiệp chính).  
- **Tham số:**  
  - `phone` (String, **bắt buộc**) – Số điện thoại đăng ký tài khoản GHN của chủ cửa hàng.  
  - `otp` (String, **bắt buộc**) – Mã OTP nhận được.  
  - `address` (String, **bắt buộc**) – Địa chỉ cửa hàng.  
  - `district_id` (Int, **bắt buộc**) – ID quận/huyện (xem API Get District).  
  - `ward_code` (String, **bắt buộc**) – Mã phường/xã (xem Get Ward).  
- **Ví dụ request:**  
  ```json
  {
    "phone": "0988367330",
    "otp": "262965",
    "address": "35 Đồng Đen, Tân Bình, TP.HCM",
    "district_id": 1442,
    "ward_code": "20106"
  }
  ```  
- **Ví dụ response:**  
  ```json
  {
    "code": 200,
    "message": "Success",
    "data": {
      "client_id": 2500024,
      "shop_id": 53620
    }
  }
  ```  

### Lấy OTP (Get OTP)  
- **Method:** `POST`  
- **Endpoint:** `shiip/public-api/v2/shop/affiliateOTP`  
- **Mô tả:** Lấy mã OTP để đăng ký hoặc thêm cửa hàng cho đối tác.  
- **Tham số:**  
  - `phone` (String, **bắt buộc**) – Số điện thoại của chủ cửa hàng đối tác.  
- **Ví dụ request:**  
  ```json
  { "phone": "0984330700" }
  ```  
- **Ví dụ response:**  
  ```json
  { "code": 200, "message": "Success", "data": { "TLL": 600 } }
  ```  
  `TLL` là thời gian hợp lệ của mã OTP (giây).  

> **Lưu ý chung:** Mọi yêu cầu API cần thêm header `Token: <mã_token>` và `Content-Type: application/json` (trừ trường hợp upload file/tương tác multipart). Địa chỉ `online-gateway.ghn.vn` là môi trường sản xuất; `dev-online-gateway.ghn.vn` là sandbox.  

