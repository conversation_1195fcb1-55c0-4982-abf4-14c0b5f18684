import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_shop_info trong cơ sở dữ liệu
 * Bảng lưu thông tin cửa hàng của người dùng để sử dụng cho vận chuyển
 * Một user có thể có nhiều shop
 */
@Entity('user_shop_info')
export class UserShopInfo {
  /**
   * ID của thông tin shop
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng sở hữu shop
   */
  @Column({ name: 'user_id', type: 'integer', nullable: false, comment: 'ID người dùng sở hữu shop' })
  userId: number;

  /**
   * Tên cửa hàng
   */
  @Column({ name: 'shop_name', type: 'varchar', length: 255, nullable: false, comment: 'Tên cửa hàng' })
  shopName: string;

  /**
   * <PERSON><PERSON> điện thoại cửa hàng
   */
  @Column({ name: 'shop_phone', type: 'varchar', length: 20, nullable: false, comment: 'Số điện thoại cửa hàng' })
  shopPhone: string;

  /**
   * Địa chỉ cửa hàng
   */
  @Column({ name: 'shop_address', type: 'varchar', length: 500, nullable: false, comment: 'Địa chỉ cửa hàng' })
  shopAddress: string;

  /**
   * Tỉnh/Thành phố
   */
  @Column({ name: 'shop_province', type: 'varchar', length: 100, nullable: false, comment: 'Tỉnh/Thành phố' })
  shopProvince: string;

  /**
   * Quận/Huyện
   */
  @Column({ name: 'shop_district', type: 'varchar', length: 100, nullable: false, comment: 'Quận/Huyện' })
  shopDistrict: string;

  /**
   * Phường/Xã
   */
  @Column({ name: 'shop_ward', type: 'varchar', length: 100, nullable: true, comment: 'Phường/Xã' })
  shopWard: string | null;

  /**
   * Thời gian tạo (millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    nullable: false,
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint",
    comment: 'Thời gian tạo (millis)'
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint",
    comment: 'Thời gian cập nhật (millis)'
  })
  updatedAt: number;
}
