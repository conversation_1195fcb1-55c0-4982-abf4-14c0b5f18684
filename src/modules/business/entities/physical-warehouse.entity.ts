import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng physical_warehouse trong cơ sở dữ liệu
 * Bảng quản lý kho vật lý
 */
@Entity('physical_warehouse')
export class PhysicalWarehouse {
  /**
   * ID của kho
   */
  @PrimaryColumn({ name: 'warehouse_id' })
  warehouseId: number;

  /**
   * Địa chỉ kho
   */
  @Column({
    name: 'address',
    length: 255,
    nullable: false,
    comment: 'Địa chỉ kho',
  })
  address: string;

  /**
   * S<PERSON><PERSON> chứa kho
   */
  @Column({ name: 'capacity', nullable: true, comment: 'Sức chứa kho' })
  capacity: number;
}
