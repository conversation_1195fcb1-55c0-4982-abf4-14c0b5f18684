# Hướng dẫn chạy test cho module Business User

Th<PERSON> mục này chứa các file test cho các thành phần của module Business User, bao gồm DTO, Repository, Service và Controller cho các chức năng File, Folder, Warehouse, Virtual Warehouse, Physical Warehouse, Warehouse Custom Field và Inventory.

## C<PERSON>u trúc thư mục

```
tests/
├── controllers/                # Test cho controllers
│   ├── user-file.controller.spec.ts
│   ├── user-folder.controller.spec.ts
│   ├── user-warehouse.controller.spec.ts
│   ├── user-virtual-warehouse.controller.spec.ts
│   ├── user-physical-warehouse.controller.spec.ts
│   ├── user-warehouse-custom-field.controller.spec.ts
│   └── user-inventory.controller.spec.ts
├── dto/                        # Test cho DTOs
│   ├── create-file.dto.spec.ts
│   ├── update-file.dto.spec.ts
│   ├── file-response.dto.spec.ts
│   ├── query-file.dto.spec.ts
│   ├── create-folder.dto.spec.ts
│   ├── update-folder.dto.spec.ts
│   ├── folder-response.dto.spec.ts
│   ├── query-folder.dto.spec.ts
│   ├── create-warehouse.dto.spec.ts
│   ├── update-warehouse.dto.spec.ts
│   ├── warehouse-response.dto.spec.ts
│   ├── query-warehouse.dto.spec.ts
│   ├── create-virtual-warehouse.dto.spec.ts
│   ├── update-virtual-warehouse.dto.spec.ts
│   ├── virtual-warehouse-response.dto.spec.ts
│   ├── query-virtual-warehouse.dto.spec.ts
│   ├── create-physical-warehouse.dto.spec.ts
│   ├── update-physical-warehouse.dto.spec.ts
│   ├── physical-warehouse-response.dto.spec.ts
│   ├── query-physical-warehouse.dto.spec.ts
│   ├── create-warehouse-custom-field.dto.spec.ts
│   ├── update-warehouse-custom-field.dto.spec.ts
│   ├── warehouse-custom-field-response.dto.spec.ts
│   ├── create-inventory.dto.spec.ts
│   ├── update-inventory.dto.spec.ts
│   ├── inventory-response.dto.spec.ts
│   └── query-inventory.dto.spec.ts
├── repositories/               # Test cho repositories
│   ├── file.repository.spec.ts
│   ├── folder.repository.spec.ts
│   ├── warehouse.repository.spec.ts
│   ├── virtual-warehouse.repository.spec.ts
│   ├── physical-warehouse.repository.spec.ts
│   ├── warehouse-custom-field.repository.spec.ts
│   └── inventory.repository.spec.ts
├── services/                   # Test cho services
│   ├── user-file.service.spec.ts
│   ├── user-folder.service.spec.ts
│   ├── user-warehouse.service.spec.ts
│   ├── user-virtual-warehouse.service.spec.ts
│   ├── user-physical-warehouse.service.spec.ts
│   ├── user-warehouse-custom-field.service.spec.ts
│   └── user-inventory.service.spec.ts
└── README.md                   # File này
```

## Chạy test

### Chạy test cho từng file

#### Test cho DTO của File Module:

```bash
# Test cho QueryFileDto
npx jest src/modules/business/user/tests/dto/query-file.dto.spec.ts --config=jest.config.js

# Test cho CreateFileDto
npx jest src/modules/business/user/tests/dto/create-file.dto.spec.ts --config=jest.config.js

# Test cho UpdateFileDto
npx jest src/modules/business/user/tests/dto/update-file.dto.spec.ts --config=jest.config.js

# Test cho FileResponseDto
npx jest src/modules/business/user/tests/dto/file-response.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Folder Module:

```bash
# Test cho CreateFolderDto
npx jest src/modules/business/user/tests/dto/create-folder.dto.spec.ts --config=jest.config.js

# Test cho UpdateFolderDto
npx jest src/modules/business/user/tests/dto/update-folder.dto.spec.ts --config=jest.config.js

# Test cho FolderResponseDto
npx jest src/modules/business/user/tests/dto/folder-response.dto.spec.ts --config=jest.config.js

# Test cho QueryFolderDto
npx jest src/modules/business/user/tests/dto/query-folder.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Warehouse Module:

```bash
# Test cho CreateWarehouseDto
npx jest src/modules/business/user/tests/dto/create-warehouse.dto.spec.ts --config=jest.config.js

# Test cho UpdateWarehouseDto
npx jest src/modules/business/user/tests/dto/update-warehouse.dto.spec.ts --config=jest.config.js

# Test cho WarehouseResponseDto
npx jest src/modules/business/user/tests/dto/warehouse-response.dto.spec.ts --config=jest.config.js

# Test cho QueryWarehouseDto
npx jest src/modules/business/user/tests/dto/query-warehouse.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Virtual Warehouse Module:

```bash
# Test cho CreateVirtualWarehouseDto
npx jest src/modules/business/user/tests/dto/create-virtual-warehouse.dto.spec.ts --config=jest.config.js

# Test cho UpdateVirtualWarehouseDto
npx jest src/modules/business/user/tests/dto/update-virtual-warehouse.dto.spec.ts --config=jest.config.js

# Test cho VirtualWarehouseResponseDto
npx jest src/modules/business/user/tests/dto/virtual-warehouse-response.dto.spec.ts --config=jest.config.js

# Test cho QueryVirtualWarehouseDto
npx jest src/modules/business/user/tests/dto/query-virtual-warehouse.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Physical Warehouse Module:

```bash
# Test cho CreatePhysicalWarehouseDto
npx jest src/modules/business/user/tests/dto/create-physical-warehouse.dto.spec.ts --config=jest.config.js

# Test cho UpdatePhysicalWarehouseDto
npx jest src/modules/business/user/tests/dto/update-physical-warehouse.dto.spec.ts --config=jest.config.js

# Test cho PhysicalWarehouseResponseDto
npx jest src/modules/business/user/tests/dto/physical-warehouse-response.dto.spec.ts --config=jest.config.js

# Test cho QueryPhysicalWarehouseDto
npx jest src/modules/business/user/tests/dto/query-physical-warehouse.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Warehouse Custom Field Module:

```bash
# Test cho CreateWarehouseCustomFieldDto
npx jest src/modules/business/user/tests/dto/create-warehouse-custom-field.dto.spec.ts --config=jest.config.js

# Test cho UpdateWarehouseCustomFieldDto
npx jest src/modules/business/user/tests/dto/update-warehouse-custom-field.dto.spec.ts --config=jest.config.js

# Test cho WarehouseCustomFieldResponseDto
npx jest src/modules/business/user/tests/dto/warehouse-custom-field-response.dto.spec.ts --config=jest.config.js
```

#### Test cho DTO của Inventory Module:

```bash
# Test cho CreateInventoryDto
npx jest src/modules/business/user/tests/dto/create-inventory.dto.spec.ts --config=jest.config.js

# Test cho UpdateInventoryDto
npx jest src/modules/business/user/tests/dto/update-inventory.dto.spec.ts --config=jest.config.js

# Test cho InventoryResponseDto
npx jest src/modules/business/user/tests/dto/inventory-response.dto.spec.ts --config=jest.config.js

# Test cho QueryInventoryDto
npx jest src/modules/business/user/tests/dto/query-inventory.dto.spec.ts --config=jest.config.js
```

#### Test cho Repository:

```bash
# Test cho FileRepository
npx jest src/modules/business/user/tests/repositories/file.repository.spec.ts --config=jest.config.js

# Test cho FolderRepository
npx jest src/modules/business/user/tests/repositories/folder.repository.spec.ts --config=jest.config.js

# Test cho WarehouseRepository
npx jest src/modules/business/user/tests/repositories/warehouse.repository.spec.ts --config=jest.config.js

# Test cho VirtualWarehouseRepository
npx jest src/modules/business/user/tests/repositories/virtual-warehouse.repository.spec.ts --config=jest.config.js

# Test cho PhysicalWarehouseRepository
npx jest src/modules/business/user/tests/repositories/physical-warehouse.repository.spec.ts --config=jest.config.js

# Test cho WarehouseCustomFieldRepository
npx jest src/modules/business/user/tests/repositories/warehouse-custom-field.repository.spec.ts --config=jest.config.js

# Test cho InventoryRepository
npx jest src/modules/business/user/tests/repositories/inventory.repository.spec.ts --config=jest.config.js
```

#### Test cho Service:

```bash
# Test cho UserFileService
npx jest src/modules/business/user/tests/services/user-file.service.spec.ts --config=jest.config.js

# Test cho UserFolderService
npx jest src/modules/business/user/tests/services/user-folder.service.spec.ts --config=jest.config.js

# Test cho UserWarehouseService
npx jest src/modules/business/user/tests/services/user-warehouse.service.spec.ts --config=jest.config.js

# Test cho UserVirtualWarehouseService
npx jest src/modules/business/user/tests/services/user-virtual-warehouse.service.spec.ts --config=jest.config.js

# Test cho UserPhysicalWarehouseService
npx jest src/modules/business/user/tests/services/user-physical-warehouse.service.spec.ts --config=jest.config.js

# Test cho UserWarehouseCustomFieldService
npx jest src/modules/business/user/tests/services/user-warehouse-custom-field.service.spec.ts --config=jest.config.js

# Test cho UserInventoryService
npx jest src/modules/business/user/tests/services/user-inventory.service.spec.ts --config=jest.config.js
```

#### Test cho Controller:

```bash
# Test cho UserFileController
npx jest src/modules/business/user/tests/controllers/user-file.controller.spec.ts --config=jest.config.js

# Test cho UserFolderController
npx jest src/modules/business/user/tests/controllers/user-folder.controller.spec.ts --config=jest.config.js

# Test cho UserWarehouseController
npx jest src/modules/business/user/tests/controllers/user-warehouse.controller.spec.ts --config=jest.config.js

# Test cho UserVirtualWarehouseController
npx jest src/modules/business/user/tests/controllers/user-virtual-warehouse.controller.spec.ts --config=jest.config.js

# Test cho UserPhysicalWarehouseController
npx jest src/modules/business/user/tests/controllers/user-physical-warehouse.controller.spec.ts --config=jest.config.js

# Test cho UserWarehouseCustomFieldController
npx jest src/modules/business/user/tests/controllers/user-warehouse-custom-field.controller.spec.ts --config=jest.config.js

# Test cho UserInventoryController
npx jest src/modules/business/user/tests/controllers/user-inventory.controller.spec.ts --config=jest.config.js
```

### Chạy test cho từng nhóm

```bash
# Chạy tất cả các test DTO
npx jest src/modules/business/user/tests/dto --config=jest.config.js

# Chạy tất cả các test Repository
npx jest src/modules/business/user/tests/repositories --config=jest.config.js

# Chạy tất cả các test Service
npx jest src/modules/business/user/tests/services --config=jest.config.js

# Chạy tất cả các test Controller
npx jest src/modules/business/user/tests/controllers --config=jest.config.js
```

### Chạy tất cả các test

```bash
npx jest src/modules/business/user/tests --config=jest.config.js
```

### Tùy chọn bổ sung

#### Chạy test với coverage

```bash
npx jest src/modules/business/user/tests --config=jest.config.js --coverage
```

#### Chạy test với output chi tiết

```bash
npx jest src/modules/business/user/tests --config=jest.config.js --verbose
```

#### Chạy test trong chế độ watch (tự động chạy lại khi có thay đổi)

```bash
npx jest src/modules/business/user/tests --config=jest.config.js --watch
```

## Cấu trúc của một file test

Mỗi file test tuân theo cấu trúc sau:

1. **Import các thư viện và module cần test**
2. **Mô tả test suite** - Mô tả chung về module đang được test
3. **Các test case** - Các trường hợp test cụ thể:
   - **Arrange**: Chuẩn bị dữ liệu test
   - **Act**: Thực hiện hành động cần test
   - **Assert**: Kiểm tra kết quả

Ví dụ:

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import { UserFileService } from '../../services/user-file.service';
import { FileRepository } from '@modules/business/repositories';

describe('UserFileService', () => {
  let service: UserFileService;
  let repository: FileRepository;

  beforeEach(async () => {
    // Arrange - Thiết lập module test
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserFileService,
        {
          provide: FileRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserFileService>(UserFileService);
    repository = module.get<FileRepository>(FileRepository);
  });

  describe('getFileById', () => {
    it('nên lấy thông tin file theo ID thành công', async () => {
      // Arrange - Chuẩn bị dữ liệu test
      const fileId = 1;
      const mockFile = { id: 1, name: 'Test file' };
      jest.spyOn(repository, 'findById').mockResolvedValue(mockFile);

      // Act - Thực hiện hành động cần test
      const result = await service.getFileById(fileId);

      // Assert - Kiểm tra kết quả
      expect(repository.findById).toHaveBeenCalledWith(fileId);
      expect(result).toEqual(mockFile);
    });
  });
});
```

## Tạo file cấu hình Jest riêng (tùy chọn)

Nếu bạn muốn tạo một file cấu hình Jest riêng cho module này, bạn có thể tạo file `jest.config.js` trong thư mục `tests`:

```javascript
module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../../',
  testRegex: 'src/modules/business/user/tests/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/business/user/**/*.(t|j)s'],
  coverageDirectory: './coverage/business-user',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
  },
  passWithNoTests: true,
}
```

Sau đó, bạn có thể chạy test với file cấu hình này:

```bash
npx jest --config=src/modules/business/user/tests/jest.config.js
```
