import { Test, TestingModule } from '@nestjs/testing';
import { UserInventoryController } from '../../controllers/user-inventory.controller';
import { UserInventoryService } from '../../services/user-inventory.service';
import { CreateInventoryDto, UpdateInventoryDto, QueryInventoryDto } from '../../dto/inventory';
import { InventoryResponseDto } from '../../dto/inventory/inventory-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { PaginatedResult } from '@common/response';

describe('UserInventoryController', () => {
  let controller: UserInventoryController;
  let service: UserInventoryService;

  // Mock data
  const mockWarehouse = {
    warehouseId: 1,
    name: '<PERSON><PERSON> hàng 1',
    description: '<PERSON><PERSON> tả kho hàng 1',
    type: WarehouseTypeEnum.PHYSICAL,
  };

  const mockInventoryResponse: InventoryResponseDto = {
    id: 1,
    productId: 1,
    warehouseId: 1,
    warehouse: mockWarehouse,
    currentQuantity: 100,
    totalQuantity: 150,
    availableQuantity: 90,
    reservedQuantity: 5,
    defectiveQuantity: 5,
    lastUpdated: 1715270400000,
  };

  const mockInventoryResponseList: InventoryResponseDto[] = [
    mockInventoryResponse,
    {
      id: 2,
      productId: 2,
      warehouseId: 1,
      warehouse: mockWarehouse,
      currentQuantity: 200,
      totalQuantity: 250,
      availableQuantity: 180,
      reservedQuantity: 10,
      defectiveQuantity: 10,
      lastUpdated: 1715270500000,
    },
  ];

  const mockPaginatedResult: PaginatedResult<InventoryResponseDto> = {
    items: mockInventoryResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserInventoryController],
      providers: [
        {
          provide: UserInventoryService,
          useValue: {
            createInventory: jest.fn(),
            updateInventory: jest.fn(),
            getInventoryById: jest.fn(),
            getInventoryByProductAndWarehouse: jest.fn(),
            getInventories: jest.fn(),
            deleteInventory: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserInventoryController>(UserInventoryController);
    service = module.get<UserInventoryService>(UserInventoryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createInventory', () => {
    it('nên tạo tồn kho mới thành công', async () => {
      // Arrange
      const createDto: CreateInventoryDto = {
        productId: 3,
        warehouseId: 1,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };

      jest.spyOn(service, 'createInventory').mockResolvedValue(mockInventoryResponse);

      // Act
      const result = await controller.createInventory(createDto);

      // Assert
      expect(service.createInventory).toHaveBeenCalledWith(createDto);
      expect(result.data).toEqual(mockInventoryResponse);
      expect(result.message).toBe('Tạo tồn kho thành công');
    });

    it('nên ném lỗi khi tạo tồn kho thất bại', async () => {
      // Arrange
      const createDto: CreateInventoryDto = {
        productId: 3,
        warehouseId: 1,
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED, 'Lỗi khi tạo tồn kho');

      jest.spyOn(service, 'createInventory').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createInventory(createDto)).rejects.toThrow(AppException);
      expect(service.createInventory).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updateInventory', () => {
    it('nên cập nhật tồn kho thành công', async () => {
      // Arrange
      const inventoryId = 1;
      const updateDto: UpdateInventoryDto = {
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };

      jest.spyOn(service, 'updateInventory').mockResolvedValue(mockInventoryResponse);

      // Act
      const result = await controller.updateInventory(inventoryId, updateDto);

      // Assert
      expect(service.updateInventory).toHaveBeenCalledWith(inventoryId, updateDto);
      expect(result.data).toEqual(mockInventoryResponse);
      expect(result.message).toBe('Cập nhật tồn kho thành công');
    });

    it('nên ném lỗi khi cập nhật tồn kho thất bại', async () => {
      // Arrange
      const inventoryId = 1;
      const updateDto: UpdateInventoryDto = {
        availableQuantity: 100,
        reservedQuantity: 10,
        defectiveQuantity: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED, 'Lỗi khi cập nhật tồn kho');

      jest.spyOn(service, 'updateInventory').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateInventory(inventoryId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateInventory).toHaveBeenCalledWith(inventoryId, updateDto);
    });
  });

  describe('getInventoryById', () => {
    it('nên lấy thông tin tồn kho theo ID thành công', async () => {
      // Arrange
      const inventoryId = 1;

      jest.spyOn(service, 'getInventoryById').mockResolvedValue(mockInventoryResponse);

      // Act
      const result = await controller.getInventoryById(inventoryId);

      // Assert
      expect(service.getInventoryById).toHaveBeenCalledWith(inventoryId);
      expect(result.data).toEqual(mockInventoryResponse);
      expect(result.message).toBe('Lấy thông tin tồn kho thành công');
    });

    it('nên ném lỗi khi lấy thông tin tồn kho thất bại', async () => {
      // Arrange
      const inventoryId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND, 'Tồn kho không tồn tại');

      jest.spyOn(service, 'getInventoryById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getInventoryById(inventoryId)).rejects.toThrow(AppException);
      expect(service.getInventoryById).toHaveBeenCalledWith(inventoryId);
    });
  });

  describe('getInventoryByProductAndWarehouse', () => {
    it('nên lấy thông tin tồn kho theo productId và warehouseId thành công', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;

      jest.spyOn(service, 'getInventoryByProductAndWarehouse').mockResolvedValue(mockInventoryResponse);

      // Act
      const result = await controller.getInventoryByProductAndWarehouse(productId, warehouseId);

      // Assert
      expect(service.getInventoryByProductAndWarehouse).toHaveBeenCalledWith(productId, warehouseId);
      expect(result.data).toEqual(mockInventoryResponse);
      expect(result.message).toBe('Lấy thông tin tồn kho theo sản phẩm và kho thành công');
    });

    it('nên ném lỗi khi lấy thông tin tồn kho thất bại', async () => {
      // Arrange
      const productId = 1;
      const warehouseId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND, 'Tồn kho không tồn tại');

      jest.spyOn(service, 'getInventoryByProductAndWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getInventoryByProductAndWarehouse(productId, warehouseId)).rejects.toThrow(AppException);
      expect(service.getInventoryByProductAndWarehouse).toHaveBeenCalledWith(productId, warehouseId);
    });
  });

  describe('getInventories', () => {
    it('nên lấy danh sách tồn kho với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryInventoryDto = {
        page: 1,
        limit: 10,
        productId: 1,
        warehouseId: 1,
        sortBy: 'currentQuantity',
        sortDirection: 'DESC',
      };

      jest.spyOn(service, 'getInventories').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getInventories(queryDto);

      // Assert
      expect(service.getInventories).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách tồn kho thành công');
    });

    it('nên ném lỗi khi lấy danh sách tồn kho thất bại', async () => {
      // Arrange
      const queryDto: QueryInventoryDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED, 'Lỗi khi lấy danh sách tồn kho');

      jest.spyOn(service, 'getInventories').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getInventories(queryDto)).rejects.toThrow(AppException);
      expect(service.getInventories).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteInventory', () => {
    it('nên xóa tồn kho thành công', async () => {
      // Arrange
      const inventoryId = 1;

      jest.spyOn(service, 'deleteInventory').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteInventory(inventoryId);

      // Assert
      expect(service.deleteInventory).toHaveBeenCalledWith(inventoryId);
      expect(result.message).toBe('Xóa tồn kho thành công');
    });

    it('nên ném lỗi khi xóa tồn kho thất bại', async () => {
      // Arrange
      const inventoryId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.INVENTORY_DELETE_FAILED, 'Lỗi khi xóa tồn kho');

      jest.spyOn(service, 'deleteInventory').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteInventory(inventoryId)).rejects.toThrow(AppException);
      expect(service.deleteInventory).toHaveBeenCalledWith(inventoryId);
    });
  });
});
