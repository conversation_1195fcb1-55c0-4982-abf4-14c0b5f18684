import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateInventoryDto } from '../../dto/inventory/update-inventory.dto';

describe('UpdateInventoryDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      warehouseId: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      warehouseId: 2,
      availableQuantity: 110,
      reservedQuantity: 8,
      defectiveQuantity: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      warehouseId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi availableQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      availableQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const availableQuantityErrors = errors.find(e => e.property === 'availableQuantity');
    expect(availableQuantityErrors).toBeDefined();
    expect(availableQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi availableQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      availableQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const availableQuantityErrors = errors.find(e => e.property === 'availableQuantity');
    expect(availableQuantityErrors).toBeDefined();
    expect(availableQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi reservedQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      reservedQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const reservedQuantityErrors = errors.find(e => e.property === 'reservedQuantity');
    expect(reservedQuantityErrors).toBeDefined();
    expect(reservedQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi reservedQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      reservedQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const reservedQuantityErrors = errors.find(e => e.property === 'reservedQuantity');
    expect(reservedQuantityErrors).toBeDefined();
    expect(reservedQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi defectiveQuantity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      defectiveQuantity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const defectiveQuantityErrors = errors.find(e => e.property === 'defectiveQuantity');
    expect(defectiveQuantityErrors).toBeDefined();
    expect(defectiveQuantityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi defectiveQuantity nhỏ hơn 0', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      defectiveQuantity: -1,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const defectiveQuantityErrors = errors.find(e => e.property === 'defectiveQuantity');
    expect(defectiveQuantityErrors).toBeDefined();
    expect(defectiveQuantityErrors?.constraints).toHaveProperty('min');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateInventoryDto, {
      warehouseId: '2',
      availableQuantity: '110',
      reservedQuantity: '8',
      defectiveQuantity: '2',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.warehouseId).toBe('number');
    expect(typeof dto.availableQuantity).toBe('number');
    expect(typeof dto.reservedQuantity).toBe('number');
    expect(typeof dto.defectiveQuantity).toBe('number');
    expect(dto.warehouseId).toBe(2);
    expect(dto.availableQuantity).toBe(110);
    expect(dto.reservedQuantity).toBe(8);
    expect(dto.defectiveQuantity).toBe(2);
  });
});
