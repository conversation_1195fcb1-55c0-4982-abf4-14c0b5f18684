import { plainToInstance } from 'class-transformer';
import { InventoryResponseDto } from '../../dto/inventory/inventory-response.dto';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('InventoryResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      id: 1,
      productId: 1,
      warehouseId: 1,
      warehouse: {
        warehouseId: 1,
        name: '<PERSON><PERSON> hàng <PERSON>',
        description: '<PERSON>ho chứa các sản phẩm chính của công ty',
        type: WarehouseTypeEnum.PHYSICAL,
      },
      currentQuantity: 100,
      totalQuantity: 150,
      availableQuantity: 90,
      reservedQuantity: 5,
      defectiveQuantity: 5,
      lastUpdated: 1715270400000,
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(InventoryResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(InventoryResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.productId).toBe(1);
    expect(dto.warehouseId).toBe(1);
    expect(dto.warehouse).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.warehouse.warehouseId).toBe(1);
    expect(dto.warehouse.name).toBe('Kho hàng chính');
    expect(dto.warehouse.description).toBe('Kho chứa các sản phẩm chính của công ty');
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.currentQuantity).toBe(100);
    expect(dto.totalQuantity).toBe(150);
    expect(dto.availableQuantity).toBe(90);
    expect(dto.reservedQuantity).toBe(5);
    expect(dto.defectiveQuantity).toBe(5);
    expect(dto.lastUpdated).toBe(1715270400000);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với warehouse là undefined', () => {
    // Arrange
    const plainData = {
      id: 1,
      productId: 1,
      warehouseId: 1,
      currentQuantity: 100,
      totalQuantity: 150,
      availableQuantity: 90,
      reservedQuantity: 5,
      defectiveQuantity: 5,
      lastUpdated: 1715270400000,
    };

    // Act
    const dto = plainToInstance(InventoryResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(InventoryResponseDto);
    expect(dto.warehouse).toBeUndefined();
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        id: 1,
        productId: 1,
        warehouseId: 1,
        currentQuantity: 100,
        totalQuantity: 150,
        availableQuantity: 90,
        reservedQuantity: 5,
        defectiveQuantity: 5,
        lastUpdated: 1715270400000,
      },
      {
        id: 2,
        productId: 2,
        warehouseId: 1,
        currentQuantity: 200,
        totalQuantity: 250,
        availableQuantity: 180,
        reservedQuantity: 10,
        defectiveQuantity: 10,
        lastUpdated: 1715270500000,
      },
    ];

    // Act
    const dtoArray = plainToInstance(InventoryResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(InventoryResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(InventoryResponseDto);
    expect(dtoArray[0].id).toBe(1);
    expect(dtoArray[1].id).toBe(2);
    expect(dtoArray[0].productId).toBe(1);
    expect(dtoArray[1].productId).toBe(2);
    expect(dtoArray[0].currentQuantity).toBe(100);
    expect(dtoArray[1].currentQuantity).toBe(200);
  });
});
