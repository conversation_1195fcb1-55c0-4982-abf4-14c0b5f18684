import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateWarehouseCustomFieldDto } from '../../dto/warehouse/update-warehouse-custom-field.dto';

describe('UpdateWarehouseCustomFieldDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseCustomFieldDto, {
      value: { value: 'Giá trị mẫu đã cập nhật' },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu value', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseCustomFieldDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreater<PERSON>han(0);
    const valueErrors = errors.find(e => e.property === 'value');
    expect(valueErrors).toBeDefined();
    expect(valueErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi value không phải là đối tượng', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseCustomFieldDto, {
      value: 'not-an-object',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const valueErrors = errors.find(e => e.property === 'value');
    expect(valueErrors).toBeDefined();
    expect(valueErrors?.constraints).toHaveProperty('isObject');
  });

  it('nên xác thực DTO hợp lệ với value là đối tượng phức tạp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateWarehouseCustomFieldDto, {
      value: {
        value: 'Giá trị mẫu đã cập nhật',
        additionalInfo: {
          key1: 'value1',
          key2: 123,
          key3: true,
        },
        items: [1, 2, 3],
      },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
