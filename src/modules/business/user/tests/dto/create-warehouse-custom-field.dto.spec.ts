import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateWarehouseCustomFieldDto } from '../../dto/warehouse/create-warehouse-custom-field.dto';

describe('CreateWarehouseCustomFieldDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      fieldId: 2,
      value: { value: 'Giá trị mẫu' },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu fieldId', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      value: { value: 'Giá trị mẫu' },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const fieldIdErrors = errors.find(e => e.property === 'fieldId');
    expect(fieldIdErrors).toBeDefined();
    expect(fieldIdErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi fieldId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      fieldId: 'not-a-number',
      value: { value: 'Giá trị mẫu' },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const fieldIdErrors = errors.find(e => e.property === 'fieldId');
    expect(fieldIdErrors).toBeDefined();
    expect(fieldIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi thiếu value', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      fieldId: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const valueErrors = errors.find(e => e.property === 'value');
    expect(valueErrors).toBeDefined();
    expect(valueErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi value không phải là đối tượng', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      fieldId: 2,
      value: 'not-an-object',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const valueErrors = errors.find(e => e.property === 'value');
    expect(valueErrors).toBeDefined();
    expect(valueErrors?.constraints).toHaveProperty('isObject');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseCustomFieldDto, {
      fieldId: '2', // String that should be converted to number
      value: { value: 'Giá trị mẫu' },
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.fieldId).toBe('number');
    expect(dto.fieldId).toBe(2);
  });
});
