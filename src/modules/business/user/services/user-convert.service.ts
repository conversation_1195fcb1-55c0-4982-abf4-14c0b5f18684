import { Injectable, Logger } from '@nestjs/common';
import { UserConvertRepository } from '@modules/business/repositories';
import { PaginatedResult } from '@common/response';
import { QueryUserConvertDto, UserConvertListItemDto, UserConvertResponseDto } from '../dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

/**
 * Service xử lý logic nghiệp vụ cho chuyển đổi khách hàng
 */
@Injectable()
export class UserConvertService {
  private readonly logger = new Logger(UserConvertService.name);

  constructor(
    private readonly userConvertRepository: UserConvertRepository,
  ) {}

  /**
   * Lấy danh sách bản ghi chuyển đổi của người dùng với phân trang
   * @param userId ID người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách bản ghi chuyển đổi với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserConvertDto): Promise<PaginatedResult<UserConvertListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách bản ghi chuyển đổi cho userId=${userId}`);

      // Lấy danh sách bản ghi chuyển đổi từ repository
      const result = await this.userConvertRepository.findAll(userId, queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => {
        return plainToInstance(UserConvertListItemDto, item, { excludeExtraneousValues: true });
      });

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách bản ghi chuyển đổi: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_FIND_FAILED,
        `Lỗi khi lấy danh sách bản ghi chuyển đổi: ${error.message}`
      );
    }
  }

  /**
   * Lấy chi tiết bản ghi chuyển đổi theo ID với thông tin khách hàng
   * @param id ID bản ghi chuyển đổi
   * @param userId ID người dùng
   * @returns Chi tiết bản ghi chuyển đổi với thông tin khách hàng
   */
  async findById(id: number, userId: number): Promise<UserConvertResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết bản ghi chuyển đổi id=${id} cho userId=${userId}`);

      // Lấy bản ghi chuyển đổi với thông tin khách hàng từ repository
      const convert = await this.userConvertRepository.findByIdWithCustomer(id);

      // Kiểm tra bản ghi chuyển đổi tồn tại
      if (!convert) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_NOT_FOUND,
          `Không tìm thấy bản ghi chuyển đổi với ID ${id}`
        );
      }

      // Kiểm tra bản ghi chuyển đổi thuộc về người dùng
      if (convert.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CONVERT_ACCESS_DENIED,
          `Bạn không có quyền truy cập bản ghi chuyển đổi này`
        );
      }

      // Chuyển đổi sang DTO response với thông tin khách hàng
      const responseDto = plainToInstance(UserConvertResponseDto, convert, { excludeExtraneousValues: true });

      return responseDto;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết bản ghi chuyển đổi: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        BUSINESS_ERROR_CODES.CONVERT_FIND_FAILED,
        `Lỗi khi lấy chi tiết bản ghi chuyển đổi: ${error.message}`
      );
    }
  }
}