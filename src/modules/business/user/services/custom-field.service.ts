import { Injectable, Logger } from '@nestjs/common';
import { CustomFieldRepository } from '@modules/business/repositories';
import { CreateCustomFieldDto, CustomFieldResponseDto, QueryCustomFieldDto, CustomFieldListItemDto, UpdateCustomFieldDto, BulkDeleteCustomFieldDto, BulkDeleteCustomFieldResponseDto } from '../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CustomField, CustomFieldStatus } from '@modules/business/entities';
import { Transactional } from 'typeorm-transactional';
import { ValidationHelper } from '../helpers';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh
 */
@Injectable()
export class CustomFieldService {
  private readonly logger = new Logger(CustomFieldService.name);

  constructor(
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo trường tùy chỉnh mới
   * @param createDto DTO chứa thông tin tạo trường tùy chỉnh
   * @returns Thông tin trường tùy chỉnh đã tạo
   */
  @Transactional()
  async create(createDto: CreateCustomFieldDto): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Tạo trường tùy chỉnh mới: ${JSON.stringify(createDto)}`);

      // Kiểm tra tính hợp lệ của dữ liệu
      this.validationHelper.validateCreateCustomFieldData(createDto);

      // Kiểm tra configId đã tồn tại chưa
      const existingField = await this.customFieldRepository.findOne({
        where: {
          configId: createDto.configId
        },
      });

      if (existingField) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_CONFIG_ID_EXISTS,
          `ConfigId "${createDto.configId}" đã tồn tại`,
        );
      }

      // Tạo entity mới
      const newCustomField = new CustomField();
      newCustomField.configId = createDto.configId;
      newCustomField.label = createDto.label;
      newCustomField.type = createDto.type;
      newCustomField.required = createDto.required;
      newCustomField.configJson = createDto.configJson;
      newCustomField.userId = createDto.userId || null;
      newCustomField.employeeId = createDto.employeeId || null;
      newCustomField.status = CustomFieldStatus.PENDING;
      newCustomField.tags = createDto.tags || [];

      // Lưu vào database
      const savedCustomField = await this.customFieldRepository.save(newCustomField);

      // Chuyển đổi sang DTO response
      return plainToInstance(CustomFieldResponseDto, savedCustomField, {
        excludeExtraneousValues: true
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
        'Lỗi khi tạo trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy danh sách trường tùy chỉnh với các điều kiện lọc và phân trang
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách trường tùy chỉnh với phân trang
   */
  async findAll(queryDto: QueryCustomFieldDto): Promise<PaginatedResult<CustomFieldListItemDto>> {
    try {
      this.logger.log(`Lấy danh sách trường tùy chỉnh với điều kiện: ${JSON.stringify(queryDto)}`);

      // Lấy danh sách trường tùy chỉnh từ repository
      const result = await this.customFieldRepository.findAll(queryDto);
      this.logger.log(`Repository trả về ${result.items.length} items`);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => {
        return plainToInstance(CustomFieldListItemDto, item, { excludeExtraneousValues: true });
      });

      this.logger.log(`Đã chuyển đổi thành công ${items.length} items sang DTO`);

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   * @param id ID của trường tùy chỉnh
   * @returns Thông tin chi tiết trường tùy chỉnh
   */
  async findById(id: number): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết trường tùy chỉnh với ID: ${id}`);

      // Lấy thông tin trường tùy chỉnh
      const customField = await this.customFieldRepository.findOne({
        where: {
          id,
          status: CustomFieldStatus.PENDING
        }
      });

      if (!customField) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id}`,
        );
      }

      return plainToInstance(CustomFieldResponseDto, customField, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy chi tiết trường tùy chỉnh',
      );
    }
  }

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID và userId (chỉ của user đang đăng nhập)
   * @param id ID của trường tùy chỉnh
   * @param userId ID của user đang đăng nhập
   * @returns Thông tin chi tiết trường tùy chỉnh
   */
  async findByIdAndUserId(id: number, userId: number): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết trường tùy chỉnh với ID: ${id} cho userId: ${userId}`);

      // Lấy thông tin trường tùy chỉnh chỉ của user đang đăng nhập
      const customField = await this.customFieldRepository.findOne({
        where: {
          id,
          userId, // Chỉ lấy của user đang đăng nhập
          status: CustomFieldStatus.PENDING
        }
      });

      if (!customField) {
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
          `Không tìm thấy trường tùy chỉnh với ID ${id} hoặc bạn không có quyền truy cập`,
        );
      }

      return plainToInstance(CustomFieldResponseDto, customField, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy chi tiết trường tùy chỉnh',
      );
    }
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param id ID của trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Thông tin trường tùy chỉnh đã cập nhật
   */
  @Transactional()
  async update(id: number, updateDto: UpdateCustomFieldDto): Promise<CustomFieldResponseDto> {
    try {
      this.logger.log(`Cập nhật trường tùy chỉnh với ID ${id}: ${JSON.stringify(updateDto)}`);

      // Kiểm tra trường tùy chỉnh tồn tại và không bị xóa
      const customField = await this.customFieldRepository.findByIdAndNotDeleted(id);
      this.validationHelper.validateCustomFieldExists(customField, id);

      // Kiểm tra tính hợp lệ của dữ liệu cập nhật
      this.validationHelper.validateUpdateCustomFieldData(updateDto, customField);

      // Cập nhật các trường được cung cấp
      if (updateDto.label) {
        customField.label = updateDto.label;
      }

      if (updateDto.type) {
        customField.type = updateDto.type;
      }

      if (updateDto.required !== undefined) {
        customField.required = updateDto.required;
      }

      if (updateDto.configJson) {
        customField.configJson = updateDto.configJson;
      }

      // Lưu vào database
      const savedCustomField = await this.customFieldRepository.save(customField);

      // Chuyển đổi sang DTO response
      const processedData = {
        ...savedCustomField
      };

      return plainToInstance(CustomFieldResponseDto, processedData, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
        'Lỗi khi cập nhật trường tùy chỉnh',
      );
    }
  }

  /**
   * Xóa trường tùy chỉnh (soft delete)
   * @param id ID của trường tùy chỉnh
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async delete(id: number, userId: number): Promise<void> {
    try {
      this.logger.log(`Xóa trường tùy chỉnh với ID ${id} bởi người dùng ${userId}`);

      // Kiểm tra trường tùy chỉnh tồn tại và không bị xóa
      const customField = await this.customFieldRepository.findByIdAndNotDeleted(id);
      this.validationHelper.validateCustomFieldExists(customField, id);

      // Kiểm tra xem có phải là user hiện tại không thì mới được quyền xóa
      if (customField.userId !== userId) {
        this.logger.error(`Người dùng ${userId} không có quyền xóa trường tùy chỉnh ${id} của người dùng ${customField.userId}`);
        throw new AppException(
          BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
          'Bạn không có quyền xóa trường tùy chỉnh này'
        );
      }

      // Cập nhật trạng thái thành DELETED
      customField.status = CustomFieldStatus.DELETED;
      await this.customFieldRepository.save(customField);

      this.logger.log(`Đã xóa trường tùy chỉnh với ID ${id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa trường tùy chỉnh: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
        'Lỗi khi xóa trường tùy chỉnh',
      );
    }
  }

  /**
   * Xóa nhiều trường tùy chỉnh (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID trường tùy chỉnh cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều trường tùy chỉnh
   */
  @Transactional()
  async bulkDeleteCustomFields(
    bulkDeleteDto: BulkDeleteCustomFieldDto,
    userId: number,
  ): Promise<BulkDeleteCustomFieldResponseDto> {
    try {
      const { customFieldIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${customFieldIds.length} trường tùy chỉnh cho userId=${userId}`,
      );

      // Xử lý từng trường tùy chỉnh một để có thể báo cáo chi tiết
      for (const customFieldId of customFieldIds) {
        try {
          // Kiểm tra trường tùy chỉnh tồn tại và không bị xóa
          const customField = await this.customFieldRepository.findByIdAndNotDeleted(customFieldId);

          if (!customField) {
            results.push({
              customFieldId,
              status: 'error',
              message: `Không tìm thấy trường tùy chỉnh với ID ${customFieldId}`,
            });
            failureCount++;
            continue;
          }

          // Kiểm tra quyền xóa
          if (customField.userId !== userId) {
            results.push({
              customFieldId,
              status: 'error',
              message: 'Bạn không có quyền xóa trường tùy chỉnh này',
            });
            failureCount++;
            continue;
          }

          // Cập nhật trạng thái thành DELETED
          customField.status = CustomFieldStatus.DELETED;
          await this.customFieldRepository.save(customField);

          results.push({
            customFieldId,
            status: 'success',
            message: 'Xóa trường tùy chỉnh thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa trường tùy chỉnh ${customFieldId}: ${error.message}`,
            error.stack,
          );

          results.push({
            customFieldId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa trường tùy chỉnh: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteCustomFieldResponseDto = {
        totalRequested: customFieldIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${customFieldIds.length} trường tùy chỉnh`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk trường tùy chỉnh: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk trường tùy chỉnh: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED,
        `Lỗi khi xóa bulk trường tùy chỉnh: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách thành phần của admin và user
   * @param userId ID người dùng (tùy chọn)
   * @param employeeId ID nhân viên (tùy chọn)
   * @returns Danh sách thành phần
   */
  async getComponents(userId?: number, employeeId?: number): Promise<any> { // ComponentListResponseDto đã bị xóa
    try {
      this.logger.log(`Lấy danh sách thành phần với userId=${userId}, employeeId=${employeeId}`);

      // Lấy danh sách thành phần mặc định
      // const defaultComponents = [...DEFAULT_COMPONENTS]; // DEFAULT_COMPONENTS đã bị xóa
      const defaultComponents = []; // Thay thế bằng mảng rỗng

      // Lấy danh sách trường tùy chỉnh của người dùng hoặc nhân viên
      const where: any = {
        // status: EntityStatusEnum.PENDING, // EntityStatusEnum đã bị xóa
        status: 'PENDING', // Thay thế bằng string literal
      };

      if (userId) {
        where.userId = userId;
      }

      if (employeeId) {
        where.employeeId = employeeId;
      }

      const customFields = await this.customFieldRepository.find({ where });

      // Chuyển đổi trường tùy chỉnh thành định dạng thành phần
      const customComponents = customFields.map(field => {
        // Tạo cấu hình từ configJson
        const config = {
          id: field.configId,
          label: field.label,
          type: field.type,
          required: field.required,
          ...field.configJson,
        };

        // Nếu có validation trong configJson, đưa nó lên cấp cao nhất
        if (field.configJson && field.configJson.validation) {
          Object.assign(config, field.configJson.validation);
        }

        return {
          component: field.component,
          config,
        };
      });

      // Kết hợp thành phần mặc định và tùy chỉnh
      const allComponents = [...defaultComponents, ...customComponents];

      // Chuyển đổi sang DTO response
      const response = {
        data: allComponents,
        total: allComponents.length,
      };

      // return plainToInstance(ComponentListResponseDto, response, { // ComponentListResponseDto đã bị xóa
      return response; // Trả về response trực tiếp
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách thành phần: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách thành phần',
      );
    }
  }

  /**
   * Lấy danh sách custom fields theo tags
   * @param tags Danh sách tags cần tìm
   * @returns Danh sách custom fields
   */
  async findByTags(tags: string[]): Promise<CustomField[]> {
    try {
      this.logger.log(`Tìm custom fields theo tags: ${JSON.stringify(tags)}`);

      const customFields = await this.customFieldRepository.findByTags(tags);
      this.logger.log(`Tìm thấy ${customFields.length} custom fields`);

      return customFields;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm custom fields theo tags: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi tìm custom fields theo tags',
      );
    }
  }

  /**
   * Lấy custom fields có sẵn cho sản phẩm
   * @returns Danh sách custom fields cho sản phẩm
   */
  async getAvailableForProduct(): Promise<CustomField[]> {
    return this.findByTags(['product', 'general']);
  }

  /**
   * Lấy custom fields có sẵn cho classification
   * @returns Danh sách custom fields cho classification
   */
  async getAvailableForClassification(): Promise<CustomField[]> {
    return this.findByTags(['classification', 'general']);
  }

  /**
   * Lấy custom fields có sẵn cho warehouse
   * @returns Danh sách custom fields cho warehouse
   */
  async getAvailableForWarehouse(): Promise<CustomField[]> {
    return this.findByTags(['warehouse', 'general']);
  }

  /**
   * Lấy custom fields theo IDs
   * @param ids Danh sách IDs
   * @returns Danh sách custom fields
   */
  async findByIds(ids: number[]): Promise<CustomField[]> {
    try {
      this.logger.log(`Tìm custom fields theo IDs: ${JSON.stringify(ids)}`);

      if (!ids || ids.length === 0) {
        return [];
      }

      const customFields = await this.customFieldRepository.findByIds(ids);
      this.logger.log(`Tìm thấy ${customFields.length} custom fields`);

      return customFields;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm custom fields theo IDs: ${error.message}`);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi tìm custom fields theo IDs',
      );
    }
  }
}
