import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiCreatedResponse, ApiExtraModels, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CustomFieldService } from '@modules/business/user/services';
import { CreateCustomFieldDto, CustomFieldResponseDto, QueryCustomFieldDto, CustomFieldListItemDto, CustomFieldDetailResponseDto, UpdateCustomFieldDto, ComponentListResponseDto, CreateCustomFieldSwaggerDto, UpdateCustomFieldSwaggerDto, BulkDeleteCustomFieldDto, BulkDeleteCustomFieldResponseDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentUser } from '@modules/auth/decorators';
import { SkipValidation } from '../decorators';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { AppException } from '@common/exceptions/app.exception';

/**
 * Controller xử lý các endpoint liên quan đến trường tùy chỉnh cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_BUSINESS)
@Controller('user/custom-fields')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, CustomFieldResponseDto, CustomFieldListItemDto, CustomFieldDetailResponseDto, PaginatedResult, ComponentListResponseDto, CreateCustomFieldSwaggerDto, UpdateCustomFieldSwaggerDto, BulkDeleteCustomFieldDto, BulkDeleteCustomFieldResponseDto)
export class CustomFieldController {
  constructor(private readonly customFieldService: CustomFieldService) {}

  /**
   * Tạo trường tùy chỉnh mới
   * @param {any} requestBody - Dữ liệu tạo trường tùy chỉnh (không qua validation)
   * @param userId
   * @returns {Promise<ApiResponseDto<CustomFieldResponseDto>>} Thông tin trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({ summary: 'Tạo trường tùy chỉnh mới (config.id bắt buộc nhập)' })
  @ApiBody({ type: CreateCustomFieldSwaggerDto })
  @ApiCreatedResponse({
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_CREATION_FAILED,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.GROUP_FORM_NOT_FOUND
  )
  async create(@SkipValidation() requestBody: any, @CurrentUser('id') userId: number): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    // Tạo DTO thủ công từ request body theo cấu trúc mới
    if (!requestBody.config?.id) {
      throw new AppException(
        BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED,
        'ID cấu hình (config.id) là bắt buộc'
      );
    }

    const createDto: CreateCustomFieldDto = {
      configId: requestBody.config.id, // configId là bắt buộc
      label: requestBody.config?.label || '',
      type: requestBody.config?.type || '',
      required: requestBody.config?.required || false,
      configJson: {
        validation: requestBody.config?.validation,
        placeholder: requestBody.config?.placeholder,
        defaultValue: requestBody.config?.defaultValue,
        ...Object.keys(requestBody.config || {}).reduce((acc, key) => {
          if (!['id', 'label', 'type', 'required', 'validation', 'placeholder', 'defaultValue'].includes(key)) {
            acc[key] = requestBody.config[key];
          }
          return acc;
        }, {})
      },
      formGroupId: requestBody.formGroupId ? Number(requestBody.formGroupId) : undefined,
      grid: requestBody.grid,
      tags: requestBody.tags || [],
      value: requestBody.config?.defaultValue ? { value: requestBody.config.defaultValue } : undefined,
      userId: userId // Luôn sử dụng userId từ người dùng đăng nhập
    };

    const customField = await this.customFieldService.create(createDto);
    return ApiResponseDto.success(customField, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Lấy danh sách trường tùy chỉnh của user đang đăng nhập
   * Tham số search sẽ tìm kiếm trong các trường: label, configId, và type
   * @param {QueryCustomFieldDto} queryDto - DTO chứa các tham số truy vấn
   * @param {number} userId - ID của user đang đăng nhập
   * @returns {Promise<ApiResponseDto<PaginatedResult<CustomFieldListItemDto>>>} Danh sách trường tùy chỉnh với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh của user đang đăng nhập' })
  @ApiOkResponse({
    description: 'Danh sách trường tùy chỉnh của user đang đăng nhập',
    schema: ApiResponseDto.getPaginatedSchema(CustomFieldListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED)
  async findAll(
    @Query() queryDto: QueryCustomFieldDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<CustomFieldListItemDto>>> {
    // Tạo một đối tượng mới chỉ với các trường cần thiết và force userId từ token
    const queryParams: QueryCustomFieldDto = {
      page: queryDto.page,
      limit: queryDto.limit,
      search: queryDto.search,
      sortBy: queryDto.sortBy,
      sortDirection: queryDto.sortDirection,
      userId: userId, // Force sử dụng userId từ token, không cho phép override
      employeeId: undefined, // Không cho phép lấy của employee
      type: queryDto.type
    };

    const result = await this.customFieldService.findAll(queryParams);
    return ApiResponseDto.success(result, 'Lấy danh sách trường tùy chỉnh thành công');
  }

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID (chỉ của user đang đăng nhập)
   * @param id ID của trường tùy chỉnh
   * @param userId ID của user đang đăng nhập
   * @returns Thông tin chi tiết trường tùy chỉnh
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết trường tùy chỉnh của user đang đăng nhập' })
  @ApiOkResponse({
    description: 'Chi tiết trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_FIND_FAILED
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldService.findByIdAndUserId(id, userId);
    return ApiResponseDto.success(result, 'Lấy chi tiết trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param {number} id - ID của trường tùy chỉnh
   * @param {any} requestBody - Dữ liệu cập nhật trường tùy chỉnh (không qua validation)
   * @param {number} userId - ID của người dùng hiện tại
   * @returns {Promise<ApiResponseDto<CustomFieldResponseDto>>} Thông tin trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật trường tùy chỉnh' })
  @ApiBody({ type: UpdateCustomFieldSwaggerDto })
  @ApiOkResponse({
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_VALIDATION_FAILED
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @SkipValidation() requestBody: any,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    // Tạo DTO thủ công từ request body theo cấu trúc mới
    const updateDto: UpdateCustomFieldDto = {
      label: requestBody.config?.label,
      type: requestBody.config?.type,
      required: requestBody.config?.required,
      configJson: requestBody.config ? {
        validation: requestBody.config.validation,
        placeholder: requestBody.config.placeholder,
        defaultValue: requestBody.config.defaultValue,
        ...Object.keys(requestBody.config).reduce((acc, key) => {
          if (!['id', 'label', 'type', 'required', 'validation', 'placeholder', 'defaultValue'].includes(key)) {
            acc[key] = requestBody.config[key];
          }
          return acc;
        }, {})
      } : undefined
    };

    const result = await this.customFieldService.update(id, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật trường tùy chỉnh thành công');
  }

  /**
   * Xóa nhiều trường tùy chỉnh (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID trường tùy chỉnh cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều trường tùy chỉnh
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa nhiều trường tùy chỉnh' })
  @ApiOkResponse({
    description: 'Xóa nhiều trường tùy chỉnh thành công',
    type: () => ApiResponseDto.getSchema(BulkDeleteCustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED
  )
  @ApiBody({ type: BulkDeleteCustomFieldDto })
  async bulkDeleteCustomFields(
    @Body() bulkDeleteDto: BulkDeleteCustomFieldDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<BulkDeleteCustomFieldResponseDto>> {
    const result = await this.customFieldService.bulkDeleteCustomFields(bulkDeleteDto, userId);
    return ApiResponseDto.success(result, result.message);
  }

  /**
   * Xóa trường tùy chỉnh (soft delete)
   * @param id ID của trường tùy chỉnh
   * @param userId ID của người dùng hiện tại
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa trường tùy chỉnh' })
  @ApiOkResponse({
    description: 'Trường tùy chỉnh đã được xóa thành công',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CUSTOM_FIELD_DELETE_FAILED
  )
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number
  ): Promise<ApiResponseDto<null>> {
    await this.customFieldService.delete(id, userId);
    return ApiResponseDto.success(null, 'Xóa trường tùy chỉnh thành công');
  }

  /**
   * Lấy custom fields có sẵn cho sản phẩm
   * @returns Danh sách custom fields cho sản phẩm
   */
  @Get('available/product')
  @ApiOperation({ summary: 'Lấy custom fields có sẵn cho sản phẩm' })
  @ApiOkResponse({
    description: 'Danh sách custom fields cho sản phẩm',
    schema: ApiResponseDto.getArraySchema(CustomFieldResponseDto),
  })
  async getAvailableForProduct(): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.getAvailableForProduct();
    return ApiResponseDto.success(result, 'Lấy custom fields cho sản phẩm thành công');
  }

  /**
   * Lấy custom fields có sẵn cho classification
   * @returns Danh sách custom fields cho classification
   */
  @Get('available/classification')
  @ApiOperation({ summary: 'Lấy custom fields có sẵn cho classification' })
  @ApiOkResponse({
    description: 'Danh sách custom fields cho classification',
    schema: ApiResponseDto.getArraySchema(CustomFieldResponseDto),
  })
  async getAvailableForClassification(): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.getAvailableForClassification();
    return ApiResponseDto.success(result, 'Lấy custom fields cho classification thành công');
  }

  /**
   * Lấy custom fields có sẵn cho warehouse
   * @returns Danh sách custom fields cho warehouse
   */
  @Get('available/warehouse')
  @ApiOperation({ summary: 'Lấy custom fields có sẵn cho warehouse' })
  @ApiOkResponse({
    description: 'Danh sách custom fields cho warehouse',
    schema: ApiResponseDto.getArraySchema(CustomFieldResponseDto),
  })
  async getAvailableForWarehouse(): Promise<ApiResponseDto<CustomFieldResponseDto[]>> {
    const result = await this.customFieldService.getAvailableForWarehouse();
    return ApiResponseDto.success(result, 'Lấy custom fields cho warehouse thành công');
  }
}
