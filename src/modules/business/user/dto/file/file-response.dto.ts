import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho phản hồi thông tin file
 */
export class FileResponseDto {
  /**
   * ID của file
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của file',
    example: 1,
  })
  id: number;

  /**
   * ID kho ảo chứa file
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID kho ảo chứa file',
    example: 1,
  })
  warehouseId: number;

  /**
   * Tên file
   * @example "Tài liệu hướng dẫn.pdf"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
  })
  name: string;

  /**
   * ID thư mục chứa file
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID thư mục chứa file',
    example: 1,
    nullable: true,
  })
  folderId: number;

  /**
   * <PERSON><PERSON><PERSON><PERSON> lưu trữ trên hệ thống storage
   * @example "files/2023/05/document-1625097600000-abcdef123456.pdf"
   */
  @Expose()
  @ApiProperty({
    description: 'Khóa lưu trữ trên hệ thống storage',
    example: 'files/2023/05/document-1625097600000-abcdef123456.pdf',
  })
  storageKey: string;

  /**
   * Kích thước file (byte)
   * @example 1024000
   */
  @Expose()
  @ApiProperty({
    description: 'Kích thước file (byte)',
    example: 1024000,
    nullable: true,
  })
  size: number | null;

  /**
   * URL xem file
   * @example "https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf"
   */
  @Expose()
  @ApiProperty({
    description: 'URL xem file',
    example: 'https://cdn.example.com/files/2023/05/document-1625097600000-abcdef123456.pdf',
  })
  viewUrl: string;

  /**
   * URL để upload file
   * @example "https://storage.example.com/presigned-url?token=abc123"
   */
  @Expose()
  @ApiProperty({
    description: 'URL ký sẵn để tải file lên',
    example: 'https://storage.example.com/presigned-url?token=abc123',
    required: false,
  })
  uploadUrl?: string;

  /**
   * Thời gian tạo (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   * @example 1625097600000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625097600000,
  })
  updatedAt: number;
}
