import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { PhysicalWarehouseResponseDto } from '../warehouse/physical-warehouse-response.dto';

/**
 * DTO cho phản hồi thông tin tồn kho
 */
export class InventoryResponseDto {
  /**
   * ID bản ghi tồn kho
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID bản ghi tồn kho',
    example: 1,
  })
  id: number;

  /**
   * ID sản phẩm
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  productId: number;

  /**
   * ID kho chứa sản phẩm
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID kho chứa sản phẩm',
    example: 1,
    required: false,
  })
  warehouseId: number | null;

  /**
   * Thông tin kho vật lý chứa sản phẩm
   */
  @Expose()
  @Type(() => PhysicalWarehouseResponseDto)
  @ApiProperty({
    description: 'Thông tin kho vật lý chứa sản phẩm (bao gồm địa chỉ và sức chứa)',
    type: PhysicalWarehouseResponseDto,
    required: false,
  })
  warehouse?: PhysicalWarehouseResponseDto;

  /**
   * Số lượng hiện tại trong kho
   * @example 100
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng hiện tại trong kho',
    example: 100,
  })
  currentQuantity: number;

  /**
   * Tổng số lượng đã nhập vào kho
   * @example 150
   */
  @Expose()
  @ApiProperty({
    description: 'Tổng số lượng đã nhập vào kho',
    example: 150,
  })
  totalQuantity: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng
   * @example 90
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng sẵn sàng để bán hoặc sử dụng',
    example: 90,
  })
  availableQuantity: number;

  /**
   * Số lượng bị giữ chỗ
   * @example 5
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng bị giữ chỗ',
    example: 5,
  })
  reservedQuantity: number;

  /**
   * Số lượng sản phẩm lỗi
   * @example 5
   */
  @Expose()
  @ApiProperty({
    description: 'Số lượng sản phẩm lỗi',
    example: 5,
  })
  defectiveQuantity: number;

  /**
   * Thời gian cập nhật tồn kho gần nhất
   * @example 1715270400000
   */
  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật tồn kho gần nhất',
    example: 1715270400000,
  })
  lastUpdated: number;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong kho
   * @example "SKU-001"
   */
  @Expose()
  @ApiProperty({
    description: 'Mã SKU (Stock Keeping Unit) của sản phẩm trong kho',
    example: 'SKU-001',
    required: false,
  })
  sku: string | null;

  /**
   * Mã vạch (Barcode) của sản phẩm trong kho
   * @example "1234567890123"
   */
  @Expose()
  @ApiProperty({
    description: 'Mã vạch (Barcode) của sản phẩm trong kho',
    example: '1234567890123',
    required: false,
  })
  barcode: string | null;
}
