import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều trường tùy chỉnh
 */
export class BulkDeleteCustomFieldDto {
  /**
   * Danh sách ID trường tùy chỉnh cần xóa
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID trường tùy chỉnh cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID trường tùy chỉnh phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một trường tùy chỉnh để xóa' })
  @ArrayUnique({ message: 'Danh sách ID trường tùy chỉnh không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID trường tùy chỉnh phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: 'Danh sách ID trường tùy chỉnh không được để trống' })
  customFieldIds: number[];
}
