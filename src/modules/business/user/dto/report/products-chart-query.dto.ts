import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum, IsDateString, Validate, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { DateRangeValidator } from './report-overview-query.dto';

/**
 * Enum cho cách sắp xếp sản phẩm
 */
export enum ProductSortByEnum {
  REVENUE = 'revenue',
  QUANTITY = 'quantity',
  ORDERS = 'orders'
}

/**
 * DTO cho query parameters của API biểu đồ sản phẩm
 */
export class ProductsChartQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'ID danh mục sản phẩm',
    example: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID danh mục phải là số' })
  @Min(1, { message: 'ID danh mục phải lớn hơn 0' })
  @Type(() => Number)
  categoryId?: number;

  @ApiPropertyOptional({
    description: 'Số lượng sản phẩm tối đa trả về',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Cách sắp xếp sản phẩm',
    enum: ProductSortByEnum,
    example: ProductSortByEnum.REVENUE,
    default: ProductSortByEnum.REVENUE,
  })
  @IsOptional()
  @IsEnum(ProductSortByEnum, { message: 'Cách sắp xếp không hợp lệ' })
  sortBy?: ProductSortByEnum = ProductSortByEnum.REVENUE;
}
