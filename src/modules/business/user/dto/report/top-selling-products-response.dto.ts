import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một sản phẩm bán chạy
 */
export class TopSellingProductItemDto {
  @Expose()
  @ApiProperty({
    description: 'Thứ hạng',
    example: 1,
  })
  rank: number;

  @Expose()
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 1,
  })
  productId: number;

  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam cao cấp',
  })
  productName: string;

  @Expose()
  @ApiProperty({
    description: 'Tên danh mục',
    example: 'Thời trang nam',
  })
  categoryName: string;

  @Expose()
  @ApiProperty({
    description: 'URL hình ảnh sản phẩm',
    example: 'https://example.com/product-image.jpg',
    nullable: true,
  })
  imageUrl?: string;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON> lượng đã bán',
    example: 150,
  })
  quantitySold: number;

  @Expose()
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thu từ sản phẩm',
    example: 15000000,
  })
  revenue: number;

  @Expose()
  @ApiProperty({
    description: 'Số đơn hàng chứa sản phẩm',
    example: 75,
  })
  ordersCount: number;

  @Expose()
  @ApiProperty({
    description: 'Đánh giá trung bình',
    example: 4.5,
    nullable: true,
  })
  averageRating?: number;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng so với kỳ trước (%)',
    example: 25.5,
  })
  growthRate: number;
}

/**
 * DTO cho metadata của danh sách sản phẩm bán chạy
 */
export class TopSellingProductsMetaDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng số sản phẩm',
    example: 150,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Khoảng thời gian báo cáo',
    example: '2024-01-01 đến 2024-12-31',
  })
  period: string;
}

/**
 * DTO cho response API sản phẩm bán chạy
 */
export class TopSellingProductsResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách sản phẩm bán chạy',
    type: [TopSellingProductItemDto],
  })
  data: TopSellingProductItemDto[];

  @Expose()
  @ApiProperty({
    description: 'Metadata',
    type: TopSellingProductsMetaDto,
  })
  meta: TopSellingProductsMetaDto;
}
