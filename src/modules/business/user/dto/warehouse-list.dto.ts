import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * DTO cho danh sách kho đơn giản (để chọn kho)
 */
export class WarehouseListDto {
  /**
   * ID của kho
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho',
    example: 1,
  })
  warehouseId: number;

  /**
   * Tên kho
   * @example "Kho chính"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho chính',
  })
  name: string;

  /**
   * <PERSON>ô tả kho
   * @example "Kho chính tại Hà Nội"
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả kho',
    example: 'Kho chính tại Hà Nội',
    required: false,
  })
  description?: string;

  /**
   * <PERSON>ại kho
   * @example "PHYSICAL"
   */
  @Expose()
  @ApiProperty({
    description: 'Loại kho',
    example: WarehouseTypeEnum.PHYSICAL,
    enum: WarehouseTypeEnum,
  })
  type: WarehouseTypeEnum;

  /**
   * Địa chỉ kho vật lý
   * @example "123 Storage St, Warehouse City"
   */
  @Expose()
  @ApiProperty({
    description: 'Địa chỉ kho vật lý',
    example: '123 Storage St, Warehouse City',
    required: false,
  })
  address?: string;

  /**
   * Sức chứa kho vật lý
   * @example 5000
   */
  @Expose()
  @ApiProperty({
    description: 'Sức chứa kho vật lý',
    example: 5000,
    required: false,
  })
  capacity?: number;
}
