import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều sản phẩm
 */
export class BulkDeleteProductDto {
  /**
   * Danh sách ID sản phẩm cần xóa
   * @example [1, 2, 3]
   */
  @ApiProperty({
    description: 'Danh sách ID sản phẩm cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'Danh sách ID sản phẩm phải là mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất một sản phẩm để xóa' })
  @ArrayUnique({ message: 'Danh sách ID sản phẩm không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID sản phẩm phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: '<PERSON><PERSON> sách ID sản phẩm không được để trống' })
  productIds: number[];
}
