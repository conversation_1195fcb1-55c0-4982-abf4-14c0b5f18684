import { IsString, IsOptional, IsBoolean, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho cấu hình GHN
 */
export class GHNConfigDto {
  @ApiProperty({
    description: 'Token API của GHN',
    example: 'your_ghn_token_here'
  })
  @IsString()
  token: string;

  @ApiProperty({
    description: 'Shop ID do GHN cung cấp',
    example: '123456'
  })
  @IsString()
  shopId: string;

  @ApiProperty({
    description: 'Sử dụng môi trường test hay production',
    example: true,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isTestMode?: boolean = true;
}

/**
 * DTO cho sản phẩm GHN
 */
export class GHNProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Laptop Asus'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: '<PERSON>ã sản phẩm',
    example: 'LAPTOP001',
    required: false
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    description: 'Số lượng',
    example: 1
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Giá sản phẩm (VND)',
    example: 15000000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiProperty({
    description: 'Chiều dài (cm) - bắt buộc cho dịch vụ truyền thống',
    example: 30,
    required: false
  })
  @IsOptional()
  @IsNumber()
  length?: number;

  @ApiProperty({
    description: 'Chiều rộng (cm) - bắt buộc cho dịch vụ truyền thống',
    example: 20,
    required: false
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Chiều cao (cm) - bắt buộc cho dịch vụ truyền thống',
    example: 5,
    required: false
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Khối lượng (gram) - bắt buộc cho dịch vụ truyền thống',
    example: 2000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    description: 'Phân loại sản phẩm',
    example: {
      level1: 'Điện tử',
      level2: 'Máy tính',
      level3: 'Laptop'
    },
    required: false
  })
  @IsOptional()
  category?: {
    level1?: string;
    level2?: string;
    level3?: string;
  };
}

/**
 * DTO cho thông tin đơn hàng GHN
 */
export class GHNOrderDto {
  @ApiProperty({
    description: 'ID cửa hàng GHN',
    example: 123456
  })
  @IsNumber()
  shopId: number;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A'
  })
  @IsString()
  toName: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0912345678'
  })
  @IsString()
  toPhone: string;

  @ApiProperty({
    description: 'Địa chỉ người nhận',
    example: '123 Đường ABC, Quận 1, TP.HCM'
  })
  @IsString()
  toAddress: string;

  @ApiProperty({
    description: 'Mã phường/xã người nhận',
    example: '20107'
  })
  @IsString()
  toWardCode: string;

  @ApiProperty({
    description: 'ID quận/huyện người nhận',
    example: 1442
  })
  @IsNumber()
  toDistrictId: number;

  @ApiProperty({
    description: 'Tên người gửi',
    example: 'Cửa hàng XYZ',
    required: false
  })
  @IsOptional()
  @IsString()
  fromName?: string;

  @ApiProperty({
    description: 'Số điện thoại người gửi',
    example: '0987654321',
    required: false
  })
  @IsOptional()
  @IsString()
  fromPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ người gửi',
    example: '456 Đường DEF, Quận 2, TP.HCM',
    required: false
  })
  @IsOptional()
  @IsString()
  fromAddress?: string;

  @ApiProperty({
    description: 'Mã phường/xã người gửi',
    example: '20201',
    required: false
  })
  @IsOptional()
  @IsString()
  fromWardCode?: string;

  @ApiProperty({
    description: 'ID quận/huyện người gửi',
    example: 1443,
    required: false
  })
  @IsOptional()
  @IsNumber()
  fromDistrictId?: number;

  @ApiProperty({
    description: 'Tên phường/xã người gửi',
    example: 'Phường 1',
    required: false
  })
  @IsOptional()
  @IsString()
  fromWardName?: string;

  @ApiProperty({
    description: 'Tên quận/huyện người gửi',
    example: 'Quận 2',
    required: false
  })
  @IsOptional()
  @IsString()
  fromDistrictName?: string;

  @ApiProperty({
    description: 'Tên tỉnh/thành phố người gửi',
    example: 'Hồ Chí Minh',
    required: false
  })
  @IsOptional()
  @IsString()
  fromProvinceName?: string;

  @ApiProperty({
    description: 'Số điện thoại liên hệ trả hàng',
    example: '0987654321',
    required: false
  })
  @IsOptional()
  @IsString()
  returnPhone?: string;

  @ApiProperty({
    description: 'Địa chỉ trả hàng',
    example: '456 Đường DEF, Quận 2, TP.HCM',
    required: false
  })
  @IsOptional()
  @IsString()
  returnAddress?: string;

  @ApiProperty({
    description: 'ID quận/huyện trả hàng',
    example: 1443,
    required: false
  })
  @IsOptional()
  @IsNumber()
  returnDistrictId?: number;

  @ApiProperty({
    description: 'Mã phường/xã trả hàng',
    example: '20201',
    required: false
  })
  @IsOptional()
  @IsString()
  returnWardCode?: string;

  @ApiProperty({
    description: 'Mã đơn hàng từ hệ thống khách hàng',
    example: 'ORDER_123456',
    required: false
  })
  @IsOptional()
  @IsString()
  clientOrderCode?: string;

  @ApiProperty({
    description: 'Số tiền thu hộ (VND)',
    example: 500000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  codAmount?: number;

  @ApiProperty({
    description: 'Nội dung/mô tả đơn hàng',
    example: 'Laptop Asus Gaming'
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Khối lượng (gram)',
    example: 2000
  })
  @IsNumber()
  weight: number;

  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 30
  })
  @IsNumber()
  length: number;

  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 20
  })
  @IsNumber()
  width: number;

  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5
  })
  @IsNumber()
  height: number;

  @ApiProperty({
    description: 'ID bưu cục lấy hàng',
    example: 1888,
    required: false
  })
  @IsOptional()
  @IsNumber()
  pickStationId?: number;

  @ApiProperty({
    description: 'Giá trị bảo hiểm (VND)',
    example: 1000000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  insuranceValue?: number;

  @ApiProperty({
    description: 'Mã giảm giá',
    example: 'DISCOUNT10',
    required: false
  })
  @IsOptional()
  @IsString()
  coupon?: string;

  @ApiProperty({
    description: 'Loại dịch vụ (2: E-commerce, 5: Truyền thống)',
    example: 2
  })
  @IsNumber()
  serviceTypeId: number;

  @ApiProperty({
    description: 'ID dịch vụ cụ thể',
    example: 53320,
    required: false
  })
  @IsOptional()
  @IsNumber()
  serviceId?: number;

  @ApiProperty({
    description: 'Ai trả phí vận chuyển (1: Người gửi, 2: Người nhận)',
    example: 1
  })
  @IsNumber()
  paymentTypeId: number;

  @ApiProperty({
    description: 'Ghi chú cho người giao hàng',
    example: 'Gọi trước khi giao',
    required: false
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Ghi chú bắt buộc (CHOTHUHANG, CHOXEMHANGKHONGTHU, KHONGCHOXEMHANG)',
    example: 'CHOXEMHANGKHONGTHU'
  })
  @IsString()
  requiredNote: string;

  @ApiProperty({
    description: 'Ca lấy hàng',
    example: 2,
    required: false
  })
  @IsOptional()
  @IsNumber()
  pickShift?: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [GHNProductDto],
    required: false
  })
  @IsOptional()
  items?: GHNProductDto[];
}
