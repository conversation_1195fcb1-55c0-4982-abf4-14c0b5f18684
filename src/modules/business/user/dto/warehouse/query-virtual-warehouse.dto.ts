import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * DTO cho các tham số truy vấn danh sách kho ảo
 */
export class QueryVirtualWarehouseDto extends QueryDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;
  /**
   * Trường sắp xếp
   * @example "warehouseId"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['warehouseId', 'name', 'description', 'associatedSystem', 'purpose'],
    default: 'warehouseId',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'warehouseId';

  /**
   * Hướng sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsString()
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.ASC;

  /**
   * Vị trí bắt đầu
   * @example 0
   */
  @ApiProperty({
    description: 'Vị trí bắt đầu',
    example: 0,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  offset?: number;
}
