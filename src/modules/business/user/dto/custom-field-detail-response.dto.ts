import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin nhóm liên kết với trường tùy chỉnh
 */
export class LinkedGroupDto {
  @Expose()
  @ApiProperty({
    description: 'ID của nhóm trường tùy chỉnh',
    example: 1,
  })
  formGroupId: number;

  @Expose()
  @ApiProperty({
    description: 'Tên nhóm trường tùy chỉnh',
    example: 'Thông tin cá nhân',
  })
  groupLabel: string;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình grid',
    example: { i: 'text-input', x: 0, y: 0, w: 4, h: 2 },
  })
  grid: any;

  @Expose()
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: { value: 'Nguyen Van A' },
  })
  value: { value: string };
}

/**
 * DTO cho response khi lấy chi tiết trường tùy chỉnh
 */
export class CustomFieldDetailResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'Text Input',
  })
  component: string;

  @Expose()
  @ApiProperty({
    description: 'ID cấu hình',
    example: 'text-input',
  })
  configId: string;

  @Expose()
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Họ và tên',
  })
  label: string;

  @Expose()
  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  type: string;

  @Expose()
  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  required: boolean;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình JSON',
    example: {
      validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9 ]*$' },
      placeholder: 'Nhập họ và tên',
      variant: 'outlined',
      size: 'small',
    },
  })
  configJson: any;

  @Expose()
  @ApiProperty({
    description: 'ID người dùng tạo',
    example: 1001,
    nullable: true,
  })
  userId: number | null;

  @Expose()
  @ApiProperty({
    description: 'ID nhân viên tạo',
    example: null,
    nullable: true,
  })
  employeeId: number | null;

  @Expose()
  @ApiProperty({
    description: 'Trạng thái của trường',
    example: 'PENDING',
  })
  status: string;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1741708800000,
  })
  createAt: number;

  @Expose()
  @ApiProperty({
    description: 'Danh sách nhóm liên kết',
    type: [LinkedGroupDto],
    required: false,
  })
  @Type(() => LinkedGroupDto)
  linkedGroups?: LinkedGroupDto[];
}
