import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, ValidateNested, ArrayMinSize, ArrayMaxSize, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateUserConvertCustomerDto } from './create-user-convert-customer.dto';

/**
 * DTO cho việc tạo nhiều khách hàng chuyển đổi cùng lúc
 */
export class CreateBulkUserConvertCustomerDto {
  /**
   * Danh sách khách hàng chuyển đổi cần tạo
   * @example [
   *   {
   *     "name": "Nguyễn Văn A",
   *     "phone": "0912345678",
   *     "email": "<EMAIL>",
   *     "platform": "facebook",
   *     "timezone": "Asia/Ho_Chi_Minh",
   *     "tags": ["vip", "potential"],
   *     "customFields": [
   *       {
   *         "configId": "customer_age",
   *         "label": "Tuổi khách hàng",
   *         "type": "number"
   *       }
   *     ]
   *   },
   *   {
   *     "name": "Trần Thị B",
   *     "phone": "0987654321",
   *     "email": "<EMAIL>",
   *     "platform": "web",
   *     "timezone": "Asia/Ho_Chi_Minh",
   *     "tags": ["new"],
   *     "metadata": [
   *       {
   *         "configId": "customer_job",
   *         "value": "Developer"
   *       }
   *     ]
   *   }
   * ]
   */
  @ApiProperty({
    description: 'Danh sách khách hàng chuyển đổi cần tạo (hỗ trợ tags và metadata)',
    type: [CreateUserConvertCustomerDto],
    example: [
      {
        name: 'Nguyễn Văn A',
        phone: '0912345678',
        email: '<EMAIL>',
        platform: 'facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        tags: ['vip', 'potential'],
        metadata: [
          {
            configId: 'day_of_birth',
            value: '28/11/2003'
          },
          {
            configId: 'sâs',
            value: 'Nguyễn Văn A'
          }
        ]
      },
      {
        name: 'Trần Thị B',
        phone: '0987654321',
        email: '<EMAIL>',
        platform: 'web',
        timezone: 'Asia/Ho_Chi_Minh',
        tags: ['new', 'interested'],
        metadata: [
          {
            configId: 'haianh',
            value: 'Thông tin bổ sung'
          }
        ]
      },
    ],
  })
  @IsNotEmpty({ message: 'Danh sách khách hàng không được để trống' })
  @IsArray({ message: 'Danh sách khách hàng phải là mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 khách hàng' })
  @ArrayMaxSize(100, { message: 'Không được tạo quá 100 khách hàng cùng lúc' })
  @ValidateNested({ each: true })
  @Type(() => CreateUserConvertCustomerDto)
  customers: CreateUserConvertCustomerDto[];

  /**
   * Có bỏ qua các khách hàng bị trùng số điện thoại hay không
   * Nếu true: bỏ qua khách hàng trùng và tiếp tục tạo các khách hàng khác
   * Nếu false: dừng toàn bộ quá trình khi gặp trùng lặp
   * @example true
   */
  @ApiProperty({
    description: 'Có bỏ qua các khách hàng bị trùng số điện thoại hay không',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'skipDuplicates phải là boolean' })
  skipDuplicates?: boolean = false;

  /**
   * Có tiếp tục tạo các khách hàng khác khi gặp lỗi hay không
   * Nếu true: tiếp tục tạo các khách hàng khác khi gặp lỗi
   * Nếu false: dừng toàn bộ quá trình khi gặp lỗi đầu tiên
   * @example true
   */
  @ApiProperty({
    description: 'Có tiếp tục tạo các khách hàng khác khi gặp lỗi hay không',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'continueOnError phải là boolean' })
  continueOnError?: boolean = false;
}
