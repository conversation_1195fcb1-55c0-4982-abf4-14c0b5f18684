import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO đơn giản cho trường tùy chỉnh (chỉ các trường cơ bản)
 */
export class SimpleCustomFieldDto {
  /**
   * ID cấu hình (unique)
   * @example "customer_age"
   */
  @ApiProperty({
    description: 'ID cấu hình (unique)',
    example: 'customer_age',
  })
  @IsNotEmpty({ message: 'ID cấu hình không được để trống' })
  @IsString({ message: 'ID cấu hình phải là chuỗi' })
  configId: string;

  /**
   * Nhãn hiển thị
   * @example "Tuổi khách hàng"
   */
  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Tuổi khách hàng',
  })
  @IsNotEmpty({ message: 'Nhãn hiển thị không được để trống' })
  @IsString({ message: 'Nhãn hiển thị phải là chuỗi' })
  label: string;

  /**
   * Loại trường
   * @example "number"
   */
  @ApiProperty({
    description: 'Loại trường',
    example: 'number',
    enum: ['text', 'number', 'email', 'phone', 'select', 'multiselect', 'date', 'datetime', 'textarea', 'checkbox', 'radio'],
  })
  @IsNotEmpty({ message: 'Loại trường không được để trống' })
  @IsString({ message: 'Loại trường phải là chuỗi' })
  type: string;
}
