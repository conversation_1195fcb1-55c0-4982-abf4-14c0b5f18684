# 📋 Update Social Links API

## 🎯 Tổng quan

API này cho phép cập nhật các link mạng xã hội (Facebook, Twitter, LinkedIn, Zalo, Website) cho khách hàng chuyển đổi.

## 🔗 Endpoint

```
PUT /v1/user/convert-customers/:id/social-links
```

## 🔐 Authentication

Yêu cầu JWT token trong header:
```
Authorization: Bearer <your-jwt-token>
```

## 📥 Request Parameters

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| id | number | Yes | ID của khách hàng chuyển đổi |

### Request Body

```typescript
{
  facebookLink?: string,   // Link Facebook (tùy chọn)
  twitterLink?: string,    // Link Twitter (tùy chọn)
  linkedinLink?: string,   // Link LinkedIn (tùy chọn)
  zaloLink?: string,       // Link <PERSON>alo (tùy chọn)
  websiteLink?: string     // Link Website (tùy chọn)
}
```

### Ví dụ Request

```json
{
  "facebookLink": "https://facebook.com/user123",
  "twitterLink": "https://twitter.com/user123",
  "linkedinLink": "https://linkedin.com/in/user123",
  "zaloLink": "https://zalo.me/user123",
  "websiteLink": "https://example.com"
}
```

## 📤 Response

### Success Response (200)

```json
{
  "success": true,
  "message": "Cập nhật các link mạng xã hội thành công",
  "data": {
    "customerId": 123,
    "facebookLink": "https://facebook.com/user123",
    "twitterLink": "https://twitter.com/user123",
    "linkedinLink": "https://linkedin.com/in/user123",
    "zaloLink": "https://zalo.me/user123",
    "websiteLink": "https://example.com",
    "updatedAt": 1641708800000
  }
}
```

### Error Responses

#### 404 - Khách hàng không tồn tại
```json
{
  "success": false,
  "message": "Không tìm thấy khách hàng chuyển đổi với ID 123",
  "errorCode": "CONVERT_CUSTOMER_NOT_FOUND"
}
```

#### 403 - Không có quyền truy cập
```json
{
  "success": false,
  "message": "Bạn không có quyền cập nhật khách hàng chuyển đổi này",
  "errorCode": "CONVERT_CUSTOMER_ACCESS_DENIED"
}
```

#### 400 - Validation Error
```json
{
  "success": false,
  "message": "Link Facebook phải là URL hợp lệ",
  "errorCode": "VALIDATION_ERROR"
}
```

## 📝 Validation Rules

- Tất cả các trường đều là tùy chọn
- Các link phải là URL hợp lệ nếu được cung cấp
- Độ dài tối đa cho mỗi link là 500 ký tự
- Chỉ có thể cập nhật khách hàng thuộc về user hiện tại

## 💡 Lưu ý

- API này chỉ cập nhật các link mạng xã hội, không ảnh hưởng đến thông tin khác của khách hàng
- Có thể gửi chỉ một số trường cần cập nhật, không cần gửi tất cả
- Để xóa một link, có thể gửi giá trị `null` hoặc chuỗi rỗng `""`

## 🔧 Ví dụ sử dụng

### Cập nhật chỉ Facebook link
```bash
curl -X PUT "https://api.example.com/v1/user/convert-customers/123/social-links" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "facebookLink": "https://facebook.com/newuser123"
  }'
```

### Cập nhật nhiều links
```bash
curl -X PUT "https://api.example.com/v1/user/convert-customers/123/social-links" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "facebookLink": "https://facebook.com/user123",
    "linkedinLink": "https://linkedin.com/in/user123",
    "websiteLink": "https://example.com"
  }'
```

### Xóa một link
```bash
curl -X PUT "https://api.example.com/v1/user/convert-customers/123/social-links" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "facebookLink": null
  }'
```
