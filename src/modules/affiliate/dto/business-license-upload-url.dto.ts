import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { FileTypeEnum } from '@shared/utils';

export class BusinessLicenseUploadUrlDto {
  @ApiProperty({
    description: 'Loại file giấy phép kinh doanh',
    enum: FileTypeEnum,
    example: FileTypeEnum.PDF,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(FileTypeEnum)
  mediaType: FileTypeEnum;
}
