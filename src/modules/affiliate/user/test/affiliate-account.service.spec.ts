import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateAccountService } from '../services/affiliate-account.service';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AppException, ErrorCode } from '@common/exceptions';
import { AffiliateRankRepository } from '../../repositories/affiliate-rank.repository';
import { UserRepository } from '@/modules/user/repositories/user.repository';

describe('AffiliateAccountService', () => {
  let service: AffiliateAccountService;
  let affiliateAccountRepository: AffiliateAccountRepository;
  let affiliateRankRepository: AffiliateRankRepository;
  let userRepository: UserRepository;

  // Mock repositories
  const mockAffiliateAccountRepository = {
    findByUserId: jest.fn()
  };

  const mockAffiliateRankRepository = {
    findById: jest.fn()
  };

  const mockUserRepository = {
    findById: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateAccountService,
        { provide: AffiliateAccountRepository, useValue: mockAffiliateAccountRepository },
        { provide: AffiliateRankRepository, useValue: mockAffiliateRankRepository },
        { provide: UserRepository, useValue: mockUserRepository }
      ]
    }).compile();

    service = module.get<AffiliateAccountService>(AffiliateAccountService);
    affiliateAccountRepository = module.get<AffiliateAccountRepository>(AffiliateAccountRepository);
    affiliateRankRepository = module.get<AffiliateRankRepository>(AffiliateRankRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAccount', () => {
    it('should return account information when all data exists', async () => {
      // Arrange
      const userId = 1;
      const mockAffiliateAccount = {
        id: 123,
        status: 'ACTIVE',
        createdAt: **********,
        rankId: 2
      };

      const mockUser = {
        id: userId,
        fullName: 'Nguyễn Văn A'
      };

      const mockRank = {
        id: 2,
        rankName: 'Silver',
        rankBadge: 'silver_badge.png',
        commission: 5.5,
        minCondition: 10,
        maxCondition: 49
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockAffiliateRankRepository.findById.mockResolvedValue(mockRank);

      // Act
      const result = await service.getAccount(userId);

      // Assert
      expect(affiliateAccountRepository.findByUserId).toHaveBeenCalledWith(userId);
      expect(userRepository.findById).toHaveBeenCalledWith(userId);
      expect(affiliateRankRepository.findById).toHaveBeenCalledWith(mockAffiliateAccount.rankId);

      expect(result).toEqual({
        accountInfo: {
          id: 123,
          partnerName: 'Nguyễn Văn A',
          accountType: 'PERSONAL',
          status: 'ACTIVE',
          createdAt: **********
        },
        rankInfo: {
          id: 2,
          rankName: 'Silver',
          rankBadge: 'silver_badge.png',
          commission: 5.5,
          minCondition: 10,
          maxCondition: 49
        },
        referralCode: 'NGUYENA123',
        referralLink: 'https://redai.vn/ref/NGUYENA123'
      });
    });

    it('should throw an exception when affiliate account does not exist', async () => {
      // Arrange
      const userId = 1;
      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getAccount(userId)).rejects.toThrow(
        new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy tài khoản affiliate')
      );
    });

    it('should throw an exception when user does not exist', async () => {
      // Arrange
      const userId = 1;
      const mockAffiliateAccount = {
        id: 123,
        status: 'ACTIVE',
        createdAt: **********,
        rankId: 2
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockUserRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getAccount(userId)).rejects.toThrow(
        new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin người dùng')
      );
    });

    it('should throw an exception when rank does not exist', async () => {
      // Arrange
      const userId = 1;
      const mockAffiliateAccount = {
        id: 123,
        status: 'ACTIVE',
        createdAt: **********,
        rankId: 2
      };

      const mockUser = {
        id: userId,
        fullName: 'Nguyễn Văn A'
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockAffiliateRankRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(service.getAccount(userId)).rejects.toThrow(
        new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin rank')
      );
    });
  });
});
