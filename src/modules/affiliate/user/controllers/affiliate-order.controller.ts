import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateOrderService } from '../services';
import { AffiliateOrderQueryDto, AffiliateOrderDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/orders')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_ORDER)
@ApiExtraModels(ApiResponseDto, AffiliateOrderDto)
export class AffiliateOrderController {
  constructor(
    private readonly affiliateOrderService: AffiliateOrderService,
  ) {}

  /**
   * Lấy danh sách đơn hàng affiliate
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Danh sách đơn hàng affiliate với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách đơn hàng affiliate' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách đơn hàng affiliate thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliateOrderDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number', example: 100 },
                    itemCount: { type: 'number', example: 10 },
                    itemsPerPage: { type: 'number', example: 10 },
                    totalPages: { type: 'number', example: 10 },
                    currentPage: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách đơn hàng affiliate',
  })
  async getOrders(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: AffiliateOrderQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateOrderDto>>> {
    const orders = await this.affiliateOrderService.getOrders(
      user.id,
      queryDto,
    );
    return ApiResponseDto.success(
      orders,
      'Lấy danh sách đơn hàng affiliate thành công',
    );
  }
}
