# Module Affiliate

## Tổng quan

Module Affiliate cung cấp các chức năng quản lý tiếp thị liên kết trong hệ thống. Module này cho phép người dùng tham gia chương trình tiếp thị liên kết, tạo và quản lý liên kết, theo dõi chuyển đổi và quản lý hoa hồng.

## Cấu trúc module

```
affiliate/
├── entities/               # Entities mapping với database
├── repositories/           # Repositories tương tác với database
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   └── services/           # Services xử lý logic nghiệp vụ cho admin
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   └── services/           # Services xử lý logic nghiệp vụ cho người dùng
└── affiliate.module.ts     # Module definition
```

## Các entity chính

1. **AffiliateProgram**: Chương trình tiếp thị liên kết
2. **AffiliateUser**: Người dùng tham gia chương trình
3. **AffiliateLink**: Liên kết tiếp thị
4. **AffiliateClick**: Lượt click vào liên kết
5. **AffiliateConversion**: Chuyển đổi từ liên kết
6. **AffiliateCommission**: Hoa hồng từ chuyển đổi
7. **AffiliateWithdrawal**: Yêu cầu rút tiền

## Chức năng chính

### Quản lý chương trình tiếp thị liên kết
- Tạo, cập nhật, xóa chương trình
- Cấu hình tỷ lệ hoa hồng
- Quản lý điều khoản và điều kiện

### Quản lý người dùng tiếp thị liên kết
- Đăng ký tham gia chương trình
- Phê duyệt/từ chối người dùng
- Quản lý thông tin thanh toán

### Quản lý liên kết
- Tạo liên kết tiếp thị
- Theo dõi lượt click
- Phân tích hiệu suất liên kết

### Quản lý chuyển đổi
- Theo dõi chuyển đổi
- Tính toán hoa hồng
- Quản lý trạng thái chuyển đổi

### Quản lý hoa hồng
- Tính toán hoa hồng
- Quản lý thanh toán
- Xử lý yêu cầu rút tiền

## API Endpoints

### User Endpoints

- `GET /affiliate/dashboard` - Lấy thông tin dashboard
- `GET /affiliate/links` - Lấy danh sách liên kết
- `POST /affiliate/links` - Tạo liên kết mới
- `GET /affiliate/clicks` - Lấy thông tin lượt click
- `GET /affiliate/conversions` - Lấy thông tin chuyển đổi
- `GET /affiliate/commissions` - Lấy thông tin hoa hồng
- `POST /affiliate/withdrawals` - Tạo yêu cầu rút tiền
- `GET /affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền

### Admin Endpoints

- `GET /admin/affiliate/programs` - Lấy danh sách chương trình
- `POST /admin/affiliate/programs` - Tạo chương trình mới
- `PUT /admin/affiliate/programs/:id` - Cập nhật chương trình
- `GET /admin/affiliate/users` - Lấy danh sách người dùng
- `PUT /admin/affiliate/users/:id/approve` - Phê duyệt người dùng
- `PUT /admin/affiliate/users/:id/reject` - Từ chối người dùng
- `GET /admin/affiliate/conversions` - Lấy danh sách chuyển đổi
- `GET /admin/affiliate/commissions` - Lấy danh sách hoa hồng
- `GET /admin/affiliate/withdrawals` - Lấy danh sách yêu cầu rút tiền
- `PUT /admin/affiliate/withdrawals/:id/approve` - Phê duyệt yêu cầu rút tiền
- `PUT /admin/affiliate/withdrawals/:id/reject` - Từ chối yêu cầu rút tiền

## Cách sử dụng

### Đăng ký tham gia chương trình

```typescript
// Đăng ký tham gia chương trình tiếp thị liên kết
const affiliateUser = await affiliateService.register({
  userId: 123,
  programId: 1,
  paymentMethod: 'bank_transfer',
  paymentDetails: {
    bankName: 'Example Bank',
    accountNumber: '**********',
    accountName: 'John Doe'
  }
});
```

### Tạo liên kết tiếp thị

```typescript
// Tạo liên kết tiếp thị mới
const affiliateLink = await affiliateLinkService.create({
  userId: 123,
  programId: 1,
  targetUrl: 'https://example.com/product/123',
  campaign: 'summer_sale'
});
```

### Theo dõi chuyển đổi

```typescript
// Ghi nhận chuyển đổi từ liên kết
const conversion = await affiliateConversionService.track({
  linkId: 'abc123',
  orderId: 'order_123',
  amount: 99.99
});
```

## Liên kết với các module khác

- **User Module**: Quản lý thông tin người dùng
- **Auth Module**: Xác thực và phân quyền
- **Payment Module**: Xử lý thanh toán hoa hồng
- **Order Module**: Theo dõi đơn hàng từ liên kết
