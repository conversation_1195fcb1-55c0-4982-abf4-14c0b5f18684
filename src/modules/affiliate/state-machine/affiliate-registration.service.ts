import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository, InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateContract } from '../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { AffiliateAccountStatus, ContractStatus, ContractType, SignMethod } from '../enums';
import { AppException, ErrorCode } from '@common/exceptions';

/**
 * Interface định nghĩa dữ liệu người dùng trong quy trình đăng ký
 */
export interface UserRegistrationData {
  fullName?: string;
  email?: string;
  address?: string;
  phoneNumber?: string;
  citizenId?: string;
  dateOfBirth?: string;
  citizenIssuePlace?: string;
  citizenIssueDate?: string;
  signature?: string;
}

/**
 * Interface định nghĩa thông tin hợp đồng trước đó
 */
interface PreviousContract {
  id: number;
  path?: string;
  type: 'PERSONAL' | 'BUSINESS';
  status: ContractStatus;
  rejectionReason?: string;
}

/**
 * Interface định nghĩa ngữ cảnh của máy trạng thái
 */
interface AffiliateRegistrationContext {
  userId: number;
  accountType: 'PERSONAL' | 'BUSINESS';
  userData: UserRegistrationData;
  businessData?: any;
  contractId?: number;
  contractPath?: string;
  citizenIdFrontUrl?: string;  // URL ảnh mặt trước CCCD
  citizenIdBackUrl?: string;   // URL ảnh mặt sau CCCD
  signatureUrl?: string;
  businessLicenseUrl?: string;
  signedContractUrl?: string;
  otpVerified: boolean;
  rejectionReason?: string;
  previousContracts?: PreviousContract[]; // Lưu trữ các hợp đồng trước đó
}

// Removed unused event type definitions

/**
 * Service quản lý máy trạng thái cho quy trình đăng ký affiliate
 */
@Injectable()
export class AffiliateRegistrationService {
  private readonly logger = new Logger(AffiliateRegistrationService.name);
  private machines = new Map<string, any>(); // Lưu trữ theo userId

  constructor(
    @InjectRepository(AffiliateAccount)
    private affiliateAccountRepository: Repository<AffiliateAccount>,
    @InjectRepository(AffiliateContract)
    private affiliateContractRepository: Repository<AffiliateContract>,
    @InjectRepository(BusinessInfo)
    private businessInfoRepository: Repository<BusinessInfo>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectEntityManager()
    private entityManager: EntityManager,
  ) {}

  /**
   * Định nghĩa máy trạng thái cho quy trình đăng ký affiliate
   */
  private createAffiliateRegistrationMachine() {
    return {
      id: 'affiliateRegistration',
      initial: 'selectAccountType',
      context: {
        userId: 0,
        accountType: 'PERSONAL',
        userData: {},
        businessData: {},
        businessLicenseUrl: null,
        signedContractUrl: null,
        otpVerified: false,
        rejectionReason: null,
      },
      states: {
        selectAccountType: {
          on: {
            SELECT_PERSONAL: {
              target: 'termsAcceptance',
              actions: [
                (context) => {
                  context.accountType = 'PERSONAL';
                },
              ],
            },
            SELECT_BUSINESS: {
              target: 'termsAcceptance',
              actions: [
                (context) => {
                  context.accountType = 'BUSINESS';
                },
              ],
            },
          },
        },
        termsAcceptance: {
          on: {
            ACCEPT_TERMS: {
              target: 'infoInput',
              actions: [
                (context, event) => {
                  if (event.userData) {
                    context.userData = {
                      ...context.userData,
                      ...event.userData,
                    };
                  }
                },
              ],
            },
          },
        },
        infoInput: {
          on: {
            SUBMIT_PERSONAL_INFO: {
              target: 'citizenIdUpload',
              actions: [
                (context, event) => {
                  if (event.userData) {
                    context.userData = {
                      ...context.userData,
                      ...event.userData,
                    };
                  }
                  if (event.contractId) {
                    context.contractId = event.contractId;
                  }
                  if (event.contractPath) {
                    context.contractPath = event.contractPath;
                  }
                },
              ],
              cond: (context) => context.accountType === 'PERSONAL',
            },
            SUBMIT_BUSINESS_INFO: {
              target: 'businessLicenseUpload',
              actions: [
                (context, event) => {
                  if (event.businessData) {
                    context.businessData = {
                      ...context.businessData,
                      ...event.businessData,
                    };
                  }
                  if (event.contractId) {
                    context.contractId = event.contractId;
                  }
                  if (event.contractPath) {
                    context.contractPath = event.contractPath;
                  }
                },
              ],
              cond: (context) => context.accountType === 'BUSINESS',
            },
          },
        },
        // Luồng cá nhân
        citizenIdUpload: {
          on: {
            UPLOAD_CITIZEN_ID: {
              target: 'signatureUpload',
              actions: [
                (context, event) => {
                  if (event.citizenIdFrontUrl) {
                    context.citizenIdFrontUrl = event.citizenIdFrontUrl;
                  }
                  if (event.citizenIdBackUrl) {
                    context.citizenIdBackUrl = event.citizenIdBackUrl;
                  }
                },
              ],
            },
          },
        },
        signatureUpload: {
          on: {
            SUBMIT_SIGNATURE: {
              target: 'contractReview',
              actions: [
                (context, event) => {
                  if (event.signatureUrl) {
                    context.signatureUrl = event.signatureUrl;
                  }
                },
              ],
            },
          },
        },
        contractReview: {
          on: {
            PROCEED_TO_SIGN: 'otpVerification',
          },
        },
        otpVerification: {
          on: {
            VERIFY_OTP: {
              target: 'pendingApproval',
              actions: [
                (context, event) => {
                  context.otpVerified = !!event.verified;
                },
              ],
            },
          },
        },
        // Luồng doanh nghiệp
        businessLicenseUpload: {
          on: {
            UPLOAD_BUSINESS_LICENSE: {
              target: 'contractSigningWithToken',
              actions: [
                (context, event) => {
                  if (event.businessLicenseUrl) {
                    context.businessLicenseUrl = event.businessLicenseUrl;
                  }
                },
              ],
            },
          },
        },
        contractSigningWithToken: {
          on: {
            UPLOAD_SIGNED_CONTRACT: {
              target: 'pendingApproval',
              actions: [
                (context, event) => {
                  if (event.signedContractUrl) {
                    context.signedContractUrl = event.signedContractUrl;
                  }
                },
              ],
            },
          },
        },
        // Trạng thái chung
        pendingApproval: {
          on: {
            ADMIN_APPROVE: 'approved',
            ADMIN_REJECT: {
              target: 'rejected',
              actions: [
                (context, event) => {
                  if (event.rejectionReason) {
                    context.rejectionReason = event.rejectionReason;
                  }
                },
              ],
            },
          },
        },
        approved: {
          on: {
            UPGRADE_TO_BUSINESS: {
              target: 'infoInput',
              actions: [
                (context) => {
                  // Lưu hợp đồng hiện tại vào danh sách các hợp đồng trước đó
                  if (context.contractId) {
                    context.previousContracts = [
                      ...(context.previousContracts || []),
                      {
                        id: context.contractId,
                        path: context.contractPath,
                        type: context.accountType,
                        status: ContractStatus.APPROVED,
                      },
                    ];
                  }

                  // Cập nhật loại tài khoản thành BUSINESS
                  context.accountType = 'BUSINESS';

                  // Xóa thông tin hợp đồng hiện tại
                  context.contractId = undefined;
                  context.contractPath = undefined;
                  context.signatureUrl = undefined;
                  context.citizenIdFrontUrl = undefined;
                  context.citizenIdBackUrl = undefined;
                  context.businessLicenseUrl = undefined;
                  context.signedContractUrl = undefined;
                  context.rejectionReason = undefined;
                },
              ],
            },
          },
        },
        rejected: {
          on: {
            RESTART_AFTER_REJECTION: [
              {
                target: 'businessLicenseUpload',
                cond: (context) => context.accountType === 'BUSINESS',
                actions: [
                  (context) => {
                    // Lưu hợp đồng hiện tại vào danh sách các hợp đồng trước đó
                    if (context.contractId) {
                      context.previousContracts = [
                        ...(context.previousContracts || []),
                        {
                          id: context.contractId,
                          path: context.contractPath,
                          type: context.accountType,
                          status: ContractStatus.REJECTED,
                          rejectionReason: context.rejectionReason,
                        },
                      ];
                    }

                    // Xóa thông tin hợp đồng hiện tại
                    context.contractId = undefined;
                    context.contractPath = undefined;
                    context.businessLicenseUrl = undefined;
                    context.signedContractUrl = undefined;
                    context.rejectionReason = undefined;
                  },
                ],
              },
              {
                target: 'citizenIdUpload',
                cond: (context) => context.accountType === 'PERSONAL',
                actions: [
                  (context) => {
                    // Lưu hợp đồng hiện tại vào danh sách các hợp đồng trước đó
                    if (context.contractId) {
                      context.previousContracts = [
                        ...(context.previousContracts || []),
                        {
                          id: context.contractId,
                          path: context.contractPath,
                          type: context.accountType,
                          status: ContractStatus.REJECTED,
                          rejectionReason: context.rejectionReason,
                        },
                      ];
                    }

                    // Xóa thông tin hợp đồng hiện tại
                    context.contractId = undefined;
                    context.contractPath = undefined;
                    context.signatureUrl = undefined;
                    context.citizenIdFrontUrl = undefined;
                    context.citizenIdBackUrl = undefined;
                    context.rejectionReason = undefined;
                  },
                ],
              },
            ],
          },
        },
      },
    };
  }

  /**
   * Khởi tạo máy trạng thái cho một người dùng
   * @param userId ID của người dùng
   * @param initialData Dữ liệu ban đầu
   * @returns Máy trạng thái đã khởi tạo
   */
  async startRegistration(userId: number, initialData: Partial<AffiliateRegistrationContext> = {}) {
    try {
      this.logger.log(`Starting affiliate registration for user ${userId}`);

      // Kiểm tra xem người dùng đã có tài khoản affiliate chưa
      const existingAccount = await this.affiliateAccountRepository.findOne({
        where: { userId },
      });

      // Xác định trạng thái ban đầu dựa trên trạng thái hiện tại trong DB
      let initialState = 'selectAccountType';
      let context: AffiliateRegistrationContext = {
        userId,
        accountType: initialData.accountType || 'PERSONAL',
        userData: initialData.userData || {},
        otpVerified: false,
      };

      if (existingAccount) {
        // Nếu đã có tài khoản, lấy thông tin từ DB để khôi phục trạng thái
        initialState = this.mapDbStatusToState(existingAccount.status);

        // Tìm hợp đồng hiện có
        const existingContract = await this.affiliateContractRepository.findOne({
          where: { userId },
          order: { createdAt: 'DESC' },
        });

        if (existingContract) {
          context = {
            ...context,
            contractId: existingContract.id,
            contractPath: existingContract.documentPath,
            otpVerified: existingContract.status === ContractStatus.PENDING_REVIEW,
          };
        }
      }

      // Sử dụng máy trạng thái đã định nghĩa
      const machine = this.createAffiliateRegistrationMachine();

      // Tạo một actor giả
      const actor = {
        currentState: initialState,
        context: context,
        listeners: [] as Array<(state: any) => void>,

        getSnapshot: () => ({
          value: actor.currentState,
          context: actor.context,
        }),

        send: (event: any) => {
          // Tìm trạng thái hiện tại trong máy trạng thái
          const currentStateConfig = machine.states[actor.currentState];

          // Kiểm tra xem sự kiện có được định nghĩa trong trạng thái hiện tại không
          if (currentStateConfig && currentStateConfig.on && currentStateConfig.on[event.type]) {
            const transition = currentStateConfig.on[event.type];

            // Thực hiện các actions nếu có
            if (transition.actions) {
              for (const action of transition.actions) {
                action(actor.context, event);
              }
            }

            // Cập nhật trạng thái
            if (transition.target) {
              const nextState = transition.target;
              if (nextState !== actor.currentState) {
                actor.currentState = nextState;

                // Thông báo cho tất cả listeners
                actor.listeners.forEach(listener => listener({
                  value: actor.currentState,
                  context: actor.context,
                }));
              }
            }
          }
        },

        subscribe: (listener: (state: any) => void) => {
          actor.listeners.push(listener);
          return {
            unsubscribe: () => {
              const index = actor.listeners.indexOf(listener);
              if (index >= 0) {
                actor.listeners.splice(index, 1);
              }
            },
          };
        },

        start: () => {
          // Thông báo trạng thái ban đầu
          actor.listeners.forEach(listener => listener({
            value: actor.currentState,
            context: actor.context,
          }));
        },
      };

      // Đăng ký listener cho sự kiện chuyển đổi trạng thái
      actor.subscribe((state) => {
        this.logger.log(`State changed for user ${userId}: ${JSON.stringify(state.value)}`);
        this.handleStateChange(userId.toString(), state);
      });

      // Khởi động actor
      actor.start();

      // Lưu actor vào map
      this.machines.set(userId.toString(), actor);
      return actor;
    } catch (error) {
      this.logger.error(`Error starting registration for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể khởi tạo quy trình đăng ký');
    }
  }

  /**
   * Gửi sự kiện đến máy trạng thái
   * @param userId ID của người dùng
   * @param event Sự kiện cần gửi
   * @param payload Dữ liệu kèm theo sự kiện
   * @returns true nếu gửi thành công, false nếu không
   */
  sendEvent(userId: number, event: string, payload?: any) {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);

    if (actor) {
      actor.send({ type: event, ...payload });
      return true;
    }

    return false;
  }

  /**
   * Lấy trạng thái hiện tại của máy trạng thái
   * @param userId ID của người dùng
   * @returns Trạng thái hiện tại hoặc null nếu không tìm thấy
   */
  getCurrentState(userId: number) {
    const userIdStr = userId.toString();
    const actor = this.machines.get(userIdStr);
    return actor ? actor.getSnapshot() : null;
  }

  /**
   * Xử lý khi trạng thái thay đổi
   * @param userId ID của người dùng
   * @param snapshot Snapshot của trạng thái mới
   */
  private async handleStateChange(userId: string, snapshot: any) {
    try {
      const userIdNum = parseInt(userId);
      const currentState = typeof snapshot.value === 'string'
        ? snapshot.value
        : Object.keys(snapshot.value)[0];
      const context = snapshot.context;

      // Chuyển đổi trạng thái máy sang trạng thái DB
      const accountStatus = this.mapStateToDbStatus(currentState);

      // Cập nhật trạng thái tài khoản affiliate
      await this.updateAffiliateAccount(userIdNum, accountStatus, context);

      // Xử lý các hành động bổ sung dựa trên trạng thái
      switch (currentState) {
        case 'termsAcceptance':
          await this.handleTermsAcceptance(userIdNum, context);
          break;
        case 'infoInput':
          // Không cần xử lý gì đặc biệt ở trạng thái này
          break;
        // Luồng cá nhân
        case 'personalInfo':
          await this.handlePersonalInfo(userIdNum, context);
          break;
        case 'citizenIdUpload':
          await this.handleCitizenIdUpload(userIdNum, context);
          break;
        case 'signatureUpload':
          await this.handleSignatureUpload(userIdNum, context);
          break;
        // Luồng doanh nghiệp
        case 'businessLicenseUpload':
          await this.handleBusinessLicenseUpload(userIdNum, context);
          break;
        case 'contractSigningWithToken':
          await this.handleContractSigningWithToken(userIdNum, context);
          break;
        // Trạng thái chung
        case 'pendingApproval':
          await this.handlePendingApproval(userIdNum, context);
          break;
        case 'approved':
          await this.handleApproved(userIdNum, context);
          break;
        case 'rejected':
          await this.handleRejected(userIdNum, context);
          break;
      }
    } catch (error) {
      this.logger.error(`Error handling state change for user ${userId}: ${error.message}`, error.stack);
    }
  }

  /**
   * Cập nhật tài khoản affiliate trong database
   * @param userId ID của người dùng
   * @param status Trạng thái mới
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async updateAffiliateAccount(userId: number, status: AffiliateAccountStatus, context: AffiliateRegistrationContext) {
    try {
      // Kiểm tra xem tài khoản đã tồn tại chưa
      const existingAccount = await this.affiliateAccountRepository.findOne({
        where: { userId },
      });

      const now = Math.floor(Date.now() / 1000); // Unix timestamp in seconds

      if (existingAccount) {
        // Cập nhật tài khoản hiện có
        await this.affiliateAccountRepository.update(
          { userId },
          {
            status,
            accountType: context.accountType,
            step: this.getStepFromState(context.accountType, status),
            updatedAt: now,
          },
        );
      } else {
        // Tạo tài khoản mới
        const newAccount = this.affiliateAccountRepository.create({
          userId,
          status,
          accountType: context.accountType,
          step: this.getStepFromState(context.accountType, status),
          createdAt: now,
          updatedAt: now,
          totalEarned: 0,
          totalPaidOut: 0,
          availableBalance: 0,
        });

        await this.affiliateAccountRepository.save(newAccount);
      }
    } catch (error) {
      this.logger.error(`Error updating affiliate account for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật tài khoản affiliate');
    }
  }

  /**
   * Xử lý khi người dùng chấp nhận điều khoản
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleTermsAcceptance(userId: number, context: AffiliateRegistrationContext) {
    try {
      const now = Math.floor(Date.now() / 1000); // Unix timestamp in seconds

      // Kiểm tra xem đã có hợp đồng chưa
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (existingContract) {
        // Cập nhật hợp đồng hiện có
        await this.affiliateContractRepository.update(
          { id: existingContract.id },
          {
            termsAccepted: true,
            updatedAt: now,
          },
        );
      } else {
        // Tạo hợp đồng mới
        const contractType = context.accountType === 'PERSONAL' ? ContractType.INDIVIDUAL : ContractType.BUSINESS;

        const newContract = this.affiliateContractRepository.create({
          userId,
          contractType,
          status: ContractStatus.DRAFT,
          termsAccepted: true,
          createdAt: now,
          updatedAt: now,
        });

        await this.affiliateContractRepository.save(newContract);
      }
    } catch (error) {
      this.logger.error(`Error handling terms acceptance for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật trạng thái chấp nhận điều khoản');
    }
  }

  /**
   * Xử lý khi người dùng nhập thông tin cá nhân
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handlePersonalInfo(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Tạo hợp đồng từ thông tin người dùng
      const contractPath = await this.generateContract(userId, context);

      // Cập nhật hợp đồng
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          documentPath: contractPath,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // Cập nhật thông tin tài khoản ngân hàng cho người dùng
      if (context.userData) {
        const userData = context.userData as any;
        if (userData.bankCode || userData.accountNumber || userData.accountHolder) {
          await this.updateUserBankInfo(userId, {
            bankCode: userData.bankCode,
            accountNumber: userData.accountNumber,
            accountHolder: userData.accountHolder,
            bankBranch: userData.bankBranch,
          });
        }
      }
    } catch (error) {
      this.logger.error(`Error handling personal info for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý thông tin cá nhân');
    }
  }

  /**
   * Cập nhật thông tin tài khoản ngân hàng cho người dùng
   * @param userId ID của người dùng
   * @param bankInfo Thông tin tài khoản ngân hàng
   */
  private async updateUserBankInfo(userId: number, bankInfo: {
    bankCode?: string;
    accountNumber?: string;
    accountHolder?: string;
    bankBranch?: string;
  }): Promise<void> {
    try {
      // Cập nhật thông tin tài khoản ngân hàng
      const updateData: any = {
        updatedAt: Math.floor(Date.now() / 1000),
      };

      // Chỉ cập nhật các trường có giá trị
      if (bankInfo.bankCode) {
        updateData.bankCode = bankInfo.bankCode;
      }

      if (bankInfo.accountNumber) {
        updateData.accountNumber = bankInfo.accountNumber;
      }

      if (bankInfo.accountHolder) {
        updateData.accountHolder = bankInfo.accountHolder;
      }

      if (bankInfo.bankBranch) {
        updateData.bankBranch = bankInfo.bankBranch;
      }

      // Cập nhật thông tin người dùng
      await this.userRepository.update(
        { id: userId },
        updateData
      );

      this.logger.log(`Updated bank info for user ${userId}: ${JSON.stringify(bankInfo)}`);
    } catch (error) {
      this.logger.error(`Error updating bank info for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật thông tin tài khoản ngân hàng');
    }
  }

  /**
   * Xử lý khi người dùng tải lên ảnh CCCD
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleCitizenIdUpload(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật URL ảnh CCCD
      const updateData: any = {
        updatedAt: Math.floor(Date.now() / 1000),
      };

      // Chỉ cập nhật các trường có giá trị
      if (context.citizenIdFrontUrl) {
        updateData.citizenIdFrontUrl = context.citizenIdFrontUrl;
      }

      if (context.citizenIdBackUrl) {
        updateData.citizenIdBackUrl = context.citizenIdBackUrl;
      }

      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        updateData,
      );

      this.logger.log(`Citizen ID uploaded for user ${userId}: Front: ${context.citizenIdFrontUrl}, Back: ${context.citizenIdBackUrl}`);
    } catch (error) {
      this.logger.error(`Error handling citizen ID upload for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý ảnh CCCD');
    }
  }

  /**
   * Xử lý khi người dùng tải lên chữ ký
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleSignatureUpload(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật hợp đồng với thông tin chữ ký
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          signMethod: SignMethod.ELECTRONIC,
          signatureUrl: context.signatureUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Signature uploaded for user ${userId}: ${context.signatureUrl}`);
    } catch (error) {
      this.logger.error(`Error handling signature upload for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý chữ ký');
    }
  }

  /**
   * Xử lý khi đơn đăng ký chờ phê duyệt
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handlePendingApproval(userId: number, _context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật trạng thái hợp đồng
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          status: ContractStatus.PENDING_REVIEW,
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // Tải hợp đồng lên cloud (giả định)
      await this.uploadContractToCloud(existingContract.id, existingContract.documentPath);
    } catch (error) {
      this.logger.error(`Error handling pending approval for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể cập nhật trạng thái chờ phê duyệt');
    }
  }

  /**
   * Xử lý khi đơn đăng ký được phê duyệt
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleApproved(userId: number, _context: AffiliateRegistrationContext) {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật trạng thái hợp đồng
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          status: ContractStatus.APPROVED,
          approvedAt: now,
          updatedAt: now,
        },
      );

      // Gửi email thông báo phê duyệt
      await this.sendApprovalEmail(userId);
    } catch (error) {
      this.logger.error(`Error handling approval for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể cập nhật trạng thái phê duyệt');
    }
  }

  /**
   * Xử lý khi đơn đăng ký bị từ chối
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleRejected(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật trạng thái hợp đồng
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          status: ContractStatus.REJECTED,
          rejectionReason: context.rejectionReason || 'Không đáp ứng yêu cầu',
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      // Gửi email thông báo từ chối
      await this.sendRejectionEmail(userId, context.rejectionReason);
    } catch (error) {
      this.logger.error(`Error handling rejection for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể cập nhật trạng thái từ chối');
    }
  }

  /**
   * Xử lý khi doanh nghiệp tải lên giấy phép kinh doanh
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleBusinessLicenseUpload(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm thông tin doanh nghiệp
      const businessInfo = await this.getBusinessInfo(userId);

      if (!businessInfo) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy thông tin doanh nghiệp');
      }

      // Cập nhật thông tin giấy phép kinh doanh
      if (context.businessLicenseUrl) {
        await this.updateBusinessRegistrationCertificate(userId, context.businessLicenseUrl);
        this.logger.log(`Business license uploaded for user ${userId}: ${context.businessLicenseUrl}`);
      } else {
        this.logger.warn(`No business license URL provided for user ${userId}`);
      }
    } catch (error) {
      this.logger.error(`Error handling business license upload for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý giấy phép kinh doanh');
    }
  }

  /**
   * Lấy thông tin doanh nghiệp
   * @param userId ID của người dùng
   * @returns Thông tin doanh nghiệp
   */
  private async getBusinessInfo(userId: number): Promise<BusinessInfo | null> {
    try {
      // Tìm thông tin doanh nghiệp
      const businessInfo = await this.businessInfoRepository.findOne({
        where: { userId },
      });

      return businessInfo;
    } catch (error) {
      this.logger.error(`Error getting business info for user ${userId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Cập nhật giấy phép kinh doanh
   * @param userId ID của người dùng
   * @param certificateUrl URL của giấy phép kinh doanh
   */
  private async updateBusinessRegistrationCertificate(userId: number, certificateUrl: string): Promise<void> {
    try {
      // Cập nhật giấy phép kinh doanh
      await this.businessInfoRepository.update(
        { userId },
        {
          businessRegistrationCertificate: certificateUrl,
          updatedAt: Math.floor(Date.now() / 1000),
        }
      );
    } catch (error) {
      this.logger.error(`Error updating business registration certificate for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.DATABASE_ERROR, 'Không thể cập nhật giấy phép kinh doanh');
    }
  }

  /**
   * Xử lý khi doanh nghiệp ký hợp đồng bằng USB Token
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   */
  private async handleContractSigningWithToken(userId: number, context: AffiliateRegistrationContext) {
    try {
      // Tìm hợp đồng hiện có
      const existingContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!existingContract) {
        throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy hợp đồng');
      }

      // Cập nhật hợp đồng với thông tin hợp đồng đã ký
      await this.affiliateContractRepository.update(
        { id: existingContract.id },
        {
          signMethod: SignMethod.ELECTRONIC,
          documentPath: context.signedContractUrl, // Sử dụng trường documentPath thay vì signedContractUrl
          updatedAt: Math.floor(Date.now() / 1000),
        },
      );

      this.logger.log(`Contract signed with USB Token for user ${userId}: ${context.signedContractUrl}`);
    } catch (error) {
      this.logger.error(`Error handling contract signing for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không thể xử lý hợp đồng đã ký');
    }
  }

  /**
   * Tạo hợp đồng từ thông tin người dùng
   * @param userId ID của người dùng
   * @param context Ngữ cảnh của máy trạng thái
   * @returns Đường dẫn đến hợp đồng
   */
  private async generateContract(userId: number, _context: AffiliateRegistrationContext): Promise<string> {
    // Đây là phương thức giả định, trong thực tế sẽ tạo hợp đồng thực sự
    this.logger.log(`Generating contract for user ${userId}`);

    // Giả định đường dẫn hợp đồng
    return `contracts/${userId}_${Date.now()}.pdf`;
  }

  /**
   * Tải hợp đồng lên cloud
   * @param contractId ID của hợp đồng
   * @param contractPath Đường dẫn đến hợp đồng
   */
  private async uploadContractToCloud(contractId: number, contractPath: string) {
    // Đây là phương thức giả định, trong thực tế sẽ tải lên cloud thực sự
    this.logger.log(`Uploading contract ${contractId} to cloud: ${contractPath}`);
  }

  /**
   * Gửi email thông báo phê duyệt
   * @param userId ID của người dùng
   */
  private async sendApprovalEmail(userId: number) {
    // Đây là phương thức giả định, trong thực tế sẽ gửi email thực sự
    this.logger.log(`Sending approval email to user ${userId}`);
  }

  /**
   * Gửi email thông báo từ chối
   * @param userId ID của người dùng
   * @param rejectionReason Lý do từ chối
   */
  private async sendRejectionEmail(userId: number, rejectionReason?: string) {
    // Đây là phương thức giả định, trong thực tế sẽ gửi email thực sự
    this.logger.log(`Sending rejection email to user ${userId}. Reason: ${rejectionReason || 'Không đáp ứng yêu cầu'}`);
  }

  /**
   * Chuyển đổi trạng thái máy sang trạng thái DB
   * @param state Trạng thái máy
   * @returns Trạng thái DB tương ứng
   */
  private mapStateToDbStatus(state: string): AffiliateAccountStatus {
    switch (state) {
      case 'selectAccountType':
        return AffiliateAccountStatus.DRAFT;
      case 'termsAcceptance':
        return AffiliateAccountStatus.DRAFT;
      case 'infoInput':
        return AffiliateAccountStatus.DRAFT;
      case 'personalInfo':
        return AffiliateAccountStatus.DRAFT;
      // Luồng cá nhân
      case 'citizenIdUpload':
        return AffiliateAccountStatus.DRAFT; // Vẫn là bản nháp vì đang nhập thông tin
      case 'signatureUpload':
      case 'contractReview':
      case 'otpVerification':
        return AffiliateAccountStatus.PENDING_APPROVAL; // Đang chờ phê duyệt
      // Luồng doanh nghiệp
      case 'businessLicenseUpload':
      case 'contractSigningWithToken':
        return AffiliateAccountStatus.PENDING_APPROVAL;
      // Trạng thái chung
      case 'pendingApproval':
        return AffiliateAccountStatus.PENDING_APPROVAL;
      case 'approved':
        return AffiliateAccountStatus.APPROVED;
      case 'rejected':
        return AffiliateAccountStatus.REJECTED;
      default:
        return AffiliateAccountStatus.DRAFT;
    }
  }

  /**
   * Chuyển đổi trạng thái DB sang trạng thái máy
   * @param status Trạng thái DB
   * @returns Trạng thái máy tương ứng
   */
  private mapDbStatusToState(status: AffiliateAccountStatus): string {
    switch (status) {
      case AffiliateAccountStatus.DRAFT:
        return 'termsAcceptance';
      case AffiliateAccountStatus.PENDING_APPROVAL:
        return 'pendingApproval';
      case AffiliateAccountStatus.APPROVED:
        return 'approved';
      case AffiliateAccountStatus.REJECTED:
        return 'rejected';
      case AffiliateAccountStatus.SUSPENDED:
      case AffiliateAccountStatus.TERMINATED:
        return 'rejected';
      default:
        return 'selectAccountType';
    }
  }

  /**
   * Lấy số bước từ trạng thái
   * @param accountType Loại tài khoản
   * @param status Trạng thái tài khoản
   * @returns Số bước
   */
  private getStepFromState(_accountType: string, status: AffiliateAccountStatus): number {
    switch (status) {
      case AffiliateAccountStatus.DRAFT:
        return 0;
      case AffiliateAccountStatus.PENDING_APPROVAL:
        return 1;
      case AffiliateAccountStatus.APPROVED:
        return 2;
      case AffiliateAccountStatus.REJECTED:
      case AffiliateAccountStatus.SUSPENDED:
      case AffiliateAccountStatus.TERMINATED:
        return 3;
      default:
        return 0;
    }
  }

  /**
   * Xử lý khi người dùng muốn ký lại hợp đồng sau khi bị từ chối
   * @param userId ID của người dùng
   */
  async restartAfterRejection(userId: number): Promise<boolean> {
    try {
      // Khởi tạo máy trạng thái nếu chưa tồn tại
      await this.startRegistration(userId);

      // Tìm hợp đồng hiện tại
      const currentContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!currentContract || currentContract.status !== ContractStatus.REJECTED) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể ký lại hợp đồng vì hợp đồng hiện tại không ở trạng thái bị từ chối'
        );
      }

      // Tạo bản ghi hợp đồng mới
      const now = Math.floor(Date.now() / 1000);
      const newContract = this.affiliateContractRepository.create({
        userId,
        contractType: currentContract.contractType,
        status: ContractStatus.DRAFT,
        termsAccepted: true,
        createdAt: now,
        updatedAt: now,
      });

      await this.affiliateContractRepository.save(newContract);

      // Gửi sự kiện để bắt đầu lại quy trình
      return this.sendEvent(userId, 'RESTART_AFTER_REJECTION', {
        contractId: newContract.id,
      });
    } catch (error) {
      this.logger.error(`Error restarting after rejection for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xử lý khi người dùng muốn nâng cấp lên tài khoản doanh nghiệp
   * @param userId ID của người dùng
   */
  async upgradeToBusinessAccount(userId: number): Promise<boolean> {
    try {
      // Khởi tạo máy trạng thái nếu chưa tồn tại
      await this.startRegistration(userId);

      // Tìm tài khoản affiliate hiện tại
      const affiliateAccount = await this.affiliateAccountRepository.findOne({
        where: { userId },
      });

      if (!affiliateAccount) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate'
        );
      }

      if (affiliateAccount.accountType === 'BUSINESS') {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Tài khoản đã là loại doanh nghiệp'
        );
      }

      // Tìm hợp đồng hiện tại
      const currentContract = await this.affiliateContractRepository.findOne({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      if (!currentContract || currentContract.status !== ContractStatus.APPROVED) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'Không thể nâng cấp lên tài khoản doanh nghiệp vì hợp đồng hiện tại chưa được phê duyệt'
        );
      }

      // Tạo bản ghi hợp đồng mới với loại BUSINESS
      const now = Math.floor(Date.now() / 1000);
      const newContract = this.affiliateContractRepository.create({
        userId,
        contractType: ContractType.BUSINESS,
        status: ContractStatus.DRAFT,
        termsAccepted: true,
        createdAt: now,
        updatedAt: now,
      });

      await this.affiliateContractRepository.save(newContract);

      // Gửi sự kiện để nâng cấp lên tài khoản doanh nghiệp
      return this.sendEvent(userId, 'UPGRADE_TO_BUSINESS', {
        contractId: newContract.id,
      });
    } catch (error) {
      this.logger.error(`Error upgrading to business account for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
