# Quy trình đăng ký Affiliate sử dụng XState

## Giới thiệu

Tài liệu này mô tả chi tiết cách triển khai quy trình đăng ký Affiliate trong RedAI Backend sử dụng XState. Quy trình đăng ký Affiliate là một quy trình nhiều bướ<PERSON>, yê<PERSON> cầu người dùng hoàn thành nhiều giai đoạn khác nhau trước khi trở thành đối tác tiếp thị liên kết.

## Mô hình trạng thái

Quy trình đăng ký Affiliate được mô hình hóa bằng máy trạng thái hữu hạn với các trạng thái sau:

1. **termsAcceptance**: Chấp nhận điều khoản và điều kiện
2. **personalInfo**: <PERSON>h<PERSON><PERSON> thông tin cá nhân
3. **bankInfo**: <PERSON><PERSON><PERSON><PERSON> thông tin ngân hàng
4. **handwrittenSignature**: <PERSON><PERSON><PERSON> lên chữ ký viết tay
5. **otpVerification**: Xác minh OTP
6. **pendingApproval**: Chờ phê duyệt từ admin
7. **approved**: Đã được phê duyệt
8. **rejected**: Đã bị từ chối

## Triển khai máy trạng thái

```typescript
import { Machine, assign } from 'xstate';

const personalAffiliateMachine = Machine({
  id: 'personalAffiliate',
  initial: 'termsAcceptance',
  context: {
    userData: null,
    bankInfo: null,
    signature: null,
    otpVerified: false,
  },
  states: {
    termsAcceptance: {
      on: {
        ACCEPT_TERMS: {
          target: 'personalInfo',
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
      },
    },
    personalInfo: {
      on: {
        SUBMIT_INFO: {
          target: 'bankInfo',
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
      },
    },
    bankInfo: {
      on: {
        SUBMIT_BANK_INFO: {
          target: 'handwrittenSignature',
          actions: assign({
            bankInfo: (_, event) => event.bankInfo,
          }),
        },
      },
    },
    handwrittenSignature: {
      on: {
        SUBMIT_SIGNATURE: {
          target: 'otpVerification',
          actions: assign({
            signature: (_, event) => event.signature,
          }),
        },
      },
    },
    otpVerification: {
      on: {
        VERIFY_OTP: {
          target: 'pendingApproval',
          actions: assign({
            otpVerified: (_, event) => event.verified,
          }),
        },
      },
    },
    pendingApproval: {
      on: {
        ADMIN_APPROVE: 'approved',
        ADMIN_REJECT: 'rejected',
      },
    },
    approved: {
      type: 'final',
    },
    rejected: {
      type: 'final',
    },
  },
});
```

## Service quản lý máy trạng thái

```typescript
import { Injectable } from '@nestjs/common';
import { Machine, interpret, assign } from 'xstate';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateAccountStatus } from '../enums/affiliate-account-status.enum';

@Injectable()
export class AffiliateStateMachineService {
  private machines = new Map<string, any>(); // Lưu trữ theo userId

  constructor(
    @InjectRepository(AffiliateAccount)
    private affiliateAccountRepository: Repository<AffiliateAccount>,
  ) {}

  private personalAffiliateMachine = Machine({
    // Định nghĩa máy trạng thái như ở trên
  });

  async startPersonalAffiliate(userId: string, initialData: any) {
    // Kiểm tra xem người dùng đã có tài khoản affiliate chưa
    const existingAccount = await this.affiliateAccountRepository.findOne({
      where: { userId: parseInt(userId) },
    });

    // Xác định trạng thái ban đầu dựa trên trạng thái hiện tại trong DB
    let initialState = 'termsAcceptance';
    if (existingAccount) {
      initialState = this.mapDbStatusToState(existingAccount.status);
    }

    // Khởi tạo máy trạng thái với trạng thái ban đầu
    const machine = interpret(this.personalAffiliateMachine)
      .onTransition((state) => {
        console.log(`State changed for user ${userId}:`, state.value);
        this.handleStateChange(userId, state);
      })
      .start(initialState);

    // Nếu có dữ liệu hiện có, cập nhật context
    if (existingAccount) {
      machine.state.context = {
        userData: existingAccount.userData || null,
        bankInfo: existingAccount.bankInfo || null,
        signature: existingAccount.signature || null,
        otpVerified: existingAccount.otpVerified || false,
      };
    }

    this.machines.set(userId, machine);
    return machine;
  }

  sendEvent(userId: string, event: string, payload?: any) {
    const machine = this.machines.get(userId);
    if (machine) {
      machine.send({ type: event, ...payload });
      return true;
    }
    return false;
  }

  getCurrentState(userId: string) {
    const machine = this.machines.get(userId);
    return machine ? machine.state : null;
  }

  private async handleStateChange(userId: string, state: any) {
    // Chuyển đổi trạng thái máy sang trạng thái DB
    const dbStatus = this.mapStateToDbStatus(state.value);
    
    // Cập nhật trạng thái trong database
    await this.affiliateAccountRepository.update(
      { userId: parseInt(userId) },
      {
        status: dbStatus,
        userData: state.context.userData,
        bankInfo: state.context.bankInfo,
        signature: state.context.signature,
        otpVerified: state.context.otpVerified,
      },
    );

    // Xử lý các hành động bổ sung dựa trên trạng thái
    switch (state.value) {
      case 'pendingApproval':
        // Gửi thông báo cho admin về yêu cầu phê duyệt mới
        this.notifyAdminForApproval(userId);
        break;
      case 'approved':
        // Gửi email chúc mừng cho người dùng
        this.sendApprovalEmail(userId);
        break;
      case 'rejected':
        // Gửi email thông báo từ chối
        this.sendRejectionEmail(userId);
        break;
    }
  }

  // Chuyển đổi trạng thái máy sang trạng thái DB
  private mapStateToDbStatus(state: string): AffiliateAccountStatus {
    switch (state) {
      case 'termsAcceptance':
        return AffiliateAccountStatus.STEP0;
      case 'personalInfo':
        return AffiliateAccountStatus.STEP1;
      case 'bankInfo':
      case 'handwrittenSignature':
      case 'otpVerification':
      case 'pendingApproval':
        return AffiliateAccountStatus.STEP2;
      case 'approved':
        return AffiliateAccountStatus.ACTIVE;
      case 'rejected':
        return AffiliateAccountStatus.TERMINATED;
      default:
        return AffiliateAccountStatus.STEP0;
    }
  }

  // Chuyển đổi trạng thái DB sang trạng thái máy
  private mapDbStatusToState(status: AffiliateAccountStatus): string {
    switch (status) {
      case AffiliateAccountStatus.STEP0:
        return 'termsAcceptance';
      case AffiliateAccountStatus.STEP1:
        return 'personalInfo';
      case AffiliateAccountStatus.STEP2:
        return 'pendingApproval'; // Giả sử đã hoàn thành các bước trước
      case AffiliateAccountStatus.ACTIVE:
        return 'approved';
      case AffiliateAccountStatus.SUSPENDED:
        return 'pendingApproval'; // Có thể cần xử lý đặc biệt
      case AffiliateAccountStatus.TERMINATED:
        return 'rejected';
      default:
        return 'termsAcceptance';
    }
  }

  private notifyAdminForApproval(userId: string) {
    // Gửi thông báo cho admin
    console.log(`Notification sent to admin for approval of user ${userId}`);
  }

  private sendApprovalEmail(userId: string) {
    // Gửi email chúc mừng
    console.log(`Approval email sent to user ${userId}`);
  }

  private sendRejectionEmail(userId: string) {
    // Gửi email từ chối
    console.log(`Rejection email sent to user ${userId}`);
  }
}
```

## Sử dụng trong Controller

```typescript
import { Controller, Post, Body, Param, Get, UseGuards } from '@nestjs/common';
import { AffiliateStateMachineService } from './affiliate-state-machine.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { ApiResponseDto } from '@common/response';
import { wrapResponse } from '@common/helpers';

@Controller('user/affiliate/registration')
@UseGuards(JwtUserGuard)
export class AffiliateRegistrationController {
  constructor(
    private readonly stateMachineService: AffiliateStateMachineService,
  ) {}

  @Post('start')
  async startRegistration(
    @CurrentUser() user: JWTPayload,
    @Body() initialData: any,
  ): Promise<ApiResponseDto<any>> {
    await this.stateMachineService.startPersonalAffiliate(
      user.id.toString(),
      initialData,
    );
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Quy trình đăng ký affiliate đã được khởi tạo',
    );
  }

  @Post('accept-terms')
  async acceptTerms(
    @CurrentUser() user: JWTPayload,
    @Body() userData: any,
  ): Promise<ApiResponseDto<any>> {
    const success = this.stateMachineService.sendEvent(
      user.id.toString(),
      'ACCEPT_TERMS',
      { userData },
    );
    
    if (!success) {
      // Khởi tạo máy trạng thái nếu chưa tồn tại
      await this.stateMachineService.startPersonalAffiliate(
        user.id.toString(),
        { userData },
      );
      this.stateMachineService.sendEvent(
        user.id.toString(),
        'ACCEPT_TERMS',
        { userData },
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã chấp nhận điều khoản và điều kiện',
    );
  }

  @Post('submit-personal-info')
  async submitPersonalInfo(
    @CurrentUser() user: JWTPayload,
    @Body() userData: any,
  ): Promise<ApiResponseDto<any>> {
    const success = this.stateMachineService.sendEvent(
      user.id.toString(),
      'SUBMIT_INFO',
      { userData },
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể cập nhật thông tin cá nhân. Vui lòng bắt đầu quy trình đăng ký trước.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã cập nhật thông tin cá nhân',
    );
  }

  @Post('submit-bank-info')
  async submitBankInfo(
    @CurrentUser() user: JWTPayload,
    @Body() bankInfo: any,
  ): Promise<ApiResponseDto<any>> {
    const success = this.stateMachineService.sendEvent(
      user.id.toString(),
      'SUBMIT_BANK_INFO',
      { bankInfo },
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể cập nhật thông tin ngân hàng. Vui lòng hoàn thành các bước trước.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã cập nhật thông tin ngân hàng',
    );
  }

  @Post('submit-signature')
  async submitSignature(
    @CurrentUser() user: JWTPayload,
    @Body() signature: any,
  ): Promise<ApiResponseDto<any>> {
    const success = this.stateMachineService.sendEvent(
      user.id.toString(),
      'SUBMIT_SIGNATURE',
      { signature },
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể cập nhật chữ ký. Vui lòng hoàn thành các bước trước.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã cập nhật chữ ký',
    );
  }

  @Post('verify-otp')
  async verifyOtp(
    @CurrentUser() user: JWTPayload,
    @Body() otpData: { otp: string },
  ): Promise<ApiResponseDto<any>> {
    // Giả sử có một service xác thực OTP
    const verified = await this.verifyOtpCode(user.id, otpData.otp);
    
    const success = this.stateMachineService.sendEvent(
      user.id.toString(),
      'VERIFY_OTP',
      { verified },
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể xác thực OTP. Vui lòng hoàn thành các bước trước.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      verified ? 'Đã xác thực OTP thành công' : 'Xác thực OTP thất bại',
      verified,
    );
  }

  @Get('status')
  async getRegistrationStatus(
    @CurrentUser() user: JWTPayload,
  ): Promise<ApiResponseDto<any>> {
    const currentState = this.stateMachineService.getCurrentState(user.id.toString());
    
    if (!currentState) {
      // Nếu chưa có máy trạng thái, khởi tạo từ dữ liệu trong DB
      await this.stateMachineService.startPersonalAffiliate(
        user.id.toString(),
        {},
      );
      const newState = this.stateMachineService.getCurrentState(user.id.toString());
      
      return wrapResponse(
        {
          state: newState ? newState.value : null,
          context: newState ? newState.context : null,
        },
        'Trạng thái đăng ký affiliate',
      );
    }
    
    return wrapResponse(
      {
        state: currentState.value,
        context: currentState.context,
      },
      'Trạng thái đăng ký affiliate',
    );
  }

  // Phương thức giả định để xác thực OTP
  private async verifyOtpCode(userId: number, otp: string): Promise<boolean> {
    // Trong thực tế, bạn sẽ gọi một service xác thực OTP
    return otp === '123456'; // Giả sử OTP đúng là 123456
  }
}
```

## Sử dụng trong Admin Controller

```typescript
import { Controller, Post, Param, UseGuards } from '@nestjs/common';
import { AffiliateStateMachineService } from './affiliate-state-machine.service';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto } from '@common/response';
import { wrapResponse } from '@common/helpers';

@Controller('admin/affiliate/registration')
@UseGuards(JwtEmployeeGuard)
export class AffiliateRegistrationAdminController {
  constructor(
    private readonly stateMachineService: AffiliateStateMachineService,
  ) {}

  @Post(':userId/approve')
  async approveRegistration(
    @Param('userId') userId: string,
  ): Promise<ApiResponseDto<any>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.stateMachineService.startPersonalAffiliate(userId, {});
    
    const success = this.stateMachineService.sendEvent(
      userId,
      'ADMIN_APPROVE',
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể phê duyệt đăng ký. Người dùng chưa hoàn thành quy trình.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(userId);
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã phê duyệt đăng ký affiliate',
    );
  }

  @Post(':userId/reject')
  async rejectRegistration(
    @Param('userId') userId: string,
  ): Promise<ApiResponseDto<any>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.stateMachineService.startPersonalAffiliate(userId, {});
    
    const success = this.stateMachineService.sendEvent(
      userId,
      'ADMIN_REJECT',
    );
    
    if (!success) {
      return wrapResponse(
        null,
        'Không thể từ chối đăng ký. Người dùng chưa hoàn thành quy trình.',
        false,
      );
    }
    
    const currentState = this.stateMachineService.getCurrentState(userId);
    
    return wrapResponse(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã từ chối đăng ký affiliate',
    );
  }
}
```

## Đăng ký trong Module

```typescript
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AffiliateAccount } from '../entities/affiliate-account.entity';
import { AffiliateStateMachineService } from './affiliate-state-machine.service';
import { AffiliateRegistrationController } from './affiliate-registration.controller';
import { AffiliateRegistrationAdminController } from './affiliate-registration-admin.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([AffiliateAccount]),
  ],
  controllers: [
    AffiliateRegistrationController,
    AffiliateRegistrationAdminController,
  ],
  providers: [AffiliateStateMachineService],
  exports: [AffiliateStateMachineService],
})
export class AffiliateStateMachineModule {}
```

## Mở rộng và tùy chỉnh

### 1. Thêm điều kiện chuyển đổi

Bạn có thể thêm điều kiện để kiểm tra trước khi chuyển đổi trạng thái:

```typescript
const personalAffiliateMachine = Machine({
  // ...
  states: {
    personalInfo: {
      on: {
        SUBMIT_INFO: {
          target: 'bankInfo',
          cond: (context, event) => {
            // Kiểm tra xem thông tin cá nhân có đầy đủ không
            const userData = event.userData;
            return (
              userData &&
              userData.fullName &&
              userData.email &&
              userData.phoneNumber &&
              userData.address
            );
          },
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
      },
    },
    // ...
  },
});
```

### 2. Thêm hoạt động bất đồng bộ

Bạn có thể thêm các hoạt động bất đồng bộ như gọi API hoặc xác thực:

```typescript
const personalAffiliateMachine = Machine({
  // ...
  states: {
    otpVerification: {
      invoke: {
        id: 'verifyOtp',
        src: (context, event) => (callback) => {
          // Giả lập gọi API xác thực OTP
          setTimeout(() => {
            if (event.otp === '123456') {
              callback({ type: 'OTP_VERIFIED', verified: true });
            } else {
              callback({ type: 'OTP_FAILED', error: 'Invalid OTP' });
            }
          }, 1000);
        },
      },
      on: {
        OTP_VERIFIED: {
          target: 'pendingApproval',
          actions: assign({
            otpVerified: (_, event) => event.verified,
          }),
        },
        OTP_FAILED: {
          // Ở lại trạng thái hiện tại, có thể thêm hành động xử lý lỗi
          actions: (context, event) => {
            console.error('OTP verification failed:', event.error);
          },
        },
      },
    },
    // ...
  },
});
```

### 3. Thêm trạng thái lịch sử

Bạn có thể thêm trạng thái lịch sử để quay lại trạng thái trước đó:

```typescript
const personalAffiliateMachine = Machine({
  // ...
  states: {
    personalInfo: {
      on: {
        SUBMIT_INFO: {
          target: 'bankInfo',
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
        BACK: 'termsAcceptance',
      },
    },
    bankInfo: {
      on: {
        SUBMIT_BANK_INFO: {
          target: 'handwrittenSignature',
          actions: assign({
            bankInfo: (_, event) => event.bankInfo,
          }),
        },
        BACK: 'personalInfo',
      },
    },
    // ...
  },
});
```

## Kết luận

Việc sử dụng XState để quản lý quy trình đăng ký Affiliate mang lại nhiều lợi ích:

1. **Rõ ràng và dễ hiểu**: Quy trình được mô hình hóa một cách trực quan, dễ dàng hiểu và theo dõi.
2. **Dễ bảo trì**: Các trạng thái và chuyển đổi được định nghĩa rõ ràng, dễ dàng thêm, sửa, xóa.
3. **Quản lý dữ liệu nhất quán**: Context của máy trạng thái giúp quản lý dữ liệu một cách nhất quán.
4. **Xử lý lỗi tốt hơn**: Dễ dàng xử lý các trường hợp đặc biệt và lỗi.
5. **Dễ dàng mở rộng**: Có thể dễ dàng thêm các trạng thái, sự kiện và hành động mới.

Bằng cách tích hợp XState với NestJS, bạn có thể xây dựng các quy trình nghiệp vụ phức tạp một cách rõ ràng và dễ bảo trì.
