import { AffiliateRegistrationService } from '../affiliate-registration.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AffiliateAccount } from '../../entities/affiliate-account.entity';
import { AffiliateContract } from '../../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { ContractStatus, ContractType } from '../../enums';

// Mock repositories
const mockAffiliateAccountRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockAffiliateContractRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockBusinessInfoRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockUserRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

describe('AffiliateRegistration State Machine', () => {
  let service: AffiliateRegistrationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateRegistrationService,
        {
          provide: getRepositoryToken(AffiliateAccount),
          useFactory: mockAffiliateAccountRepository,
        },
        {
          provide: getRepositoryToken(AffiliateContract),
          useFactory: mockAffiliateContractRepository,
        },
        {
          provide: getRepositoryToken(BusinessInfo),
          useFactory: mockBusinessInfoRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<AffiliateRegistrationService>(AffiliateRegistrationService);
  });

  describe('State transitions for rejected contracts', () => {
    it('should transition from rejected to citizenIdUpload for personal accounts', async () => {
      // Arrange
      const userId = 1;
      const initialState = {
        value: 'rejected',
        context: {
          userId,
          accountType: 'PERSONAL',
          contractId: 1,
          rejectionReason: 'Thông tin không hợp lệ',
        },
      };
      
      // Mock startRegistration to return a machine with the initial state
      jest.spyOn(service, 'startRegistration').mockImplementation(async () => {
        const machine = {
          currentState: initialState.value,
          context: initialState.context,
          listeners: [],
          
          getSnapshot: () => ({
            value: machine.currentState,
            context: machine.context,
          }),
          
          send: (event) => {
            if (event.type === 'RESTART_AFTER_REJECTION' && machine.currentState === 'rejected') {
              // Save previous contract
              machine.context.previousContracts = [
                ...(machine.context.previousContracts || []),
                {
                  id: machine.context.contractId,
                  type: machine.context.accountType,
                  status: ContractStatus.REJECTED,
                  rejectionReason: machine.context.rejectionReason,
                },
              ];
              
              // Clear current contract info
              machine.context.contractId = event.contractId;
              machine.context.rejectionReason = undefined;
              
              // Transition to citizenIdUpload for personal accounts
              machine.currentState = 'citizenIdUpload';
              
              // Notify listeners
              machine.listeners.forEach(listener => listener({
                value: machine.currentState,
                context: machine.context,
              }));
            }
          },
          
          subscribe: (listener) => {
            machine.listeners.push(listener);
            return {
              unsubscribe: () => {
                const index = machine.listeners.indexOf(listener);
                if (index >= 0) {
                  machine.listeners.splice(index, 1);
                }
              },
            };
          },
          
          start: () => {
            machine.listeners.forEach(listener => listener({
              value: machine.currentState,
              context: machine.context,
            }));
          },
        };
        
        machine.start();
        return machine;
      });
      
      // Mock getCurrentState to return the current state of the machine
      jest.spyOn(service, 'getCurrentState').mockImplementation((id) => {
        const machine = service['machines'].get(id.toString());
        return machine ? machine.getSnapshot() : null;
      });
      
      // Mock sendEvent to forward events to the machine
      jest.spyOn(service, 'sendEvent').mockImplementation((id, eventType, payload) => {
        const machine = service['machines'].get(id.toString());
        if (machine) {
          machine.send({ type: eventType, ...payload });
          return true;
        }
        return false;
      });
      
      // Act
      await service.startRegistration(userId);
      service.sendEvent(userId, 'RESTART_AFTER_REJECTION', { contractId: 2 });
      const currentState = service.getCurrentState(userId);
      
      // Assert
      expect(currentState.value).toBe('citizenIdUpload');
      expect(currentState.context.previousContracts).toHaveLength(1);
      expect(currentState.context.previousContracts[0].id).toBe(1);
      expect(currentState.context.previousContracts[0].status).toBe(ContractStatus.REJECTED);
      expect(currentState.context.contractId).toBe(2);
      expect(currentState.context.rejectionReason).toBeUndefined();
    });

    it('should transition from rejected to businessLicenseUpload for business accounts', async () => {
      // Arrange
      const userId = 1;
      const initialState = {
        value: 'rejected',
        context: {
          userId,
          accountType: 'BUSINESS',
          contractId: 1,
          rejectionReason: 'Thông tin không hợp lệ',
        },
      };
      
      // Mock startRegistration to return a machine with the initial state
      jest.spyOn(service, 'startRegistration').mockImplementation(async () => {
        const machine = {
          currentState: initialState.value,
          context: initialState.context,
          listeners: [],
          
          getSnapshot: () => ({
            value: machine.currentState,
            context: machine.context,
          }),
          
          send: (event) => {
            if (event.type === 'RESTART_AFTER_REJECTION' && machine.currentState === 'rejected') {
              // Save previous contract
              machine.context.previousContracts = [
                ...(machine.context.previousContracts || []),
                {
                  id: machine.context.contractId,
                  type: machine.context.accountType,
                  status: ContractStatus.REJECTED,
                  rejectionReason: machine.context.rejectionReason,
                },
              ];
              
              // Clear current contract info
              machine.context.contractId = event.contractId;
              machine.context.rejectionReason = undefined;
              
              // Transition to businessLicenseUpload for business accounts
              machine.currentState = 'businessLicenseUpload';
              
              // Notify listeners
              machine.listeners.forEach(listener => listener({
                value: machine.currentState,
                context: machine.context,
              }));
            }
          },
          
          subscribe: (listener) => {
            machine.listeners.push(listener);
            return {
              unsubscribe: () => {
                const index = machine.listeners.indexOf(listener);
                if (index >= 0) {
                  machine.listeners.splice(index, 1);
                }
              },
            };
          },
          
          start: () => {
            machine.listeners.forEach(listener => listener({
              value: machine.currentState,
              context: machine.context,
            }));
          },
        };
        
        machine.start();
        return machine;
      });
      
      // Mock getCurrentState to return the current state of the machine
      jest.spyOn(service, 'getCurrentState').mockImplementation((id) => {
        const machine = service['machines'].get(id.toString());
        return machine ? machine.getSnapshot() : null;
      });
      
      // Mock sendEvent to forward events to the machine
      jest.spyOn(service, 'sendEvent').mockImplementation((id, eventType, payload) => {
        const machine = service['machines'].get(id.toString());
        if (machine) {
          machine.send({ type: eventType, ...payload });
          return true;
        }
        return false;
      });
      
      // Act
      await service.startRegistration(userId);
      service.sendEvent(userId, 'RESTART_AFTER_REJECTION', { contractId: 2 });
      const currentState = service.getCurrentState(userId);
      
      // Assert
      expect(currentState.value).toBe('businessLicenseUpload');
      expect(currentState.context.previousContracts).toHaveLength(1);
      expect(currentState.context.previousContracts[0].id).toBe(1);
      expect(currentState.context.previousContracts[0].status).toBe(ContractStatus.REJECTED);
      expect(currentState.context.contractId).toBe(2);
      expect(currentState.context.rejectionReason).toBeUndefined();
    });
  });

  describe('State transitions for upgrading to business account', () => {
    it('should transition from approved to infoInput when upgrading to business account', async () => {
      // Arrange
      const userId = 1;
      const initialState = {
        value: 'approved',
        context: {
          userId,
          accountType: 'PERSONAL',
          contractId: 1,
        },
      };
      
      // Mock startRegistration to return a machine with the initial state
      jest.spyOn(service, 'startRegistration').mockImplementation(async () => {
        const machine = {
          currentState: initialState.value,
          context: initialState.context,
          listeners: [],
          
          getSnapshot: () => ({
            value: machine.currentState,
            context: machine.context,
          }),
          
          send: (event) => {
            if (event.type === 'UPGRADE_TO_BUSINESS' && machine.currentState === 'approved') {
              // Save previous contract
              machine.context.previousContracts = [
                ...(machine.context.previousContracts || []),
                {
                  id: machine.context.contractId,
                  type: machine.context.accountType,
                  status: ContractStatus.APPROVED,
                },
              ];
              
              // Update account type and clear contract info
              machine.context.accountType = 'BUSINESS';
              machine.context.contractId = event.contractId;
              
              // Transition to infoInput
              machine.currentState = 'infoInput';
              
              // Notify listeners
              machine.listeners.forEach(listener => listener({
                value: machine.currentState,
                context: machine.context,
              }));
            }
          },
          
          subscribe: (listener) => {
            machine.listeners.push(listener);
            return {
              unsubscribe: () => {
                const index = machine.listeners.indexOf(listener);
                if (index >= 0) {
                  machine.listeners.splice(index, 1);
                }
              },
            };
          },
          
          start: () => {
            machine.listeners.forEach(listener => listener({
              value: machine.currentState,
              context: machine.context,
            }));
          },
        };
        
        machine.start();
        return machine;
      });
      
      // Mock getCurrentState to return the current state of the machine
      jest.spyOn(service, 'getCurrentState').mockImplementation((id) => {
        const machine = service['machines'].get(id.toString());
        return machine ? machine.getSnapshot() : null;
      });
      
      // Mock sendEvent to forward events to the machine
      jest.spyOn(service, 'sendEvent').mockImplementation((id, eventType, payload) => {
        const machine = service['machines'].get(id.toString());
        if (machine) {
          machine.send({ type: eventType, ...payload });
          return true;
        }
        return false;
      });
      
      // Act
      await service.startRegistration(userId);
      service.sendEvent(userId, 'UPGRADE_TO_BUSINESS', { contractId: 2 });
      const currentState = service.getCurrentState(userId);
      
      // Assert
      expect(currentState.value).toBe('infoInput');
      expect(currentState.context.accountType).toBe('BUSINESS');
      expect(currentState.context.previousContracts).toHaveLength(1);
      expect(currentState.context.previousContracts[0].id).toBe(1);
      expect(currentState.context.previousContracts[0].status).toBe(ContractStatus.APPROVED);
      expect(currentState.context.contractId).toBe(2);
    });
  });
});
