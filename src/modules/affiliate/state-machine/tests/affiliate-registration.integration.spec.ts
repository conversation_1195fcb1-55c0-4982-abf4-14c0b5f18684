import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { getRepositoryToken } from '@nestjs/typeorm';
import { AffiliateRegistrationService } from '../affiliate-registration.service';
import { AffiliateRegistrationController } from '../affiliate-registration.controller';
import { AffiliateAccount } from '../../entities/affiliate-account.entity';
import { AffiliateContract } from '../../entities/affiliate-contract.entity';
import { BusinessInfo } from '@modules/user/entities/business-info.entity';
import { User } from '@modules/user/entities/user.entity';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { ContractStatus, ContractType } from '../../enums';

// Mock repositories
const mockAffiliateAccountRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockAffiliateContractRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockBusinessInfoRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

const mockUserRepository = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
});

// Mock JWT guard
class MockJwtUserGuard {
  canActivate() {
    return true;
  }
}

describe('AffiliateRegistration Integration Tests', () => {
  let app: INestApplication;
  let service: AffiliateRegistrationService;
  let affiliateContractRepository;
  let affiliateAccountRepository;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateRegistrationController],
      providers: [
        AffiliateRegistrationService,
        {
          provide: getRepositoryToken(AffiliateAccount),
          useFactory: mockAffiliateAccountRepository,
        },
        {
          provide: getRepositoryToken(AffiliateContract),
          useFactory: mockAffiliateContractRepository,
        },
        {
          provide: getRepositoryToken(BusinessInfo),
          useFactory: mockBusinessInfoRepository,
        },
        {
          provide: getRepositoryToken(User),
          useFactory: mockUserRepository,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useClass(MockJwtUserGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    service = moduleFixture.get<AffiliateRegistrationService>(AffiliateRegistrationService);
    affiliateContractRepository = moduleFixture.get(getRepositoryToken(AffiliateContract));
    affiliateAccountRepository = moduleFixture.get(getRepositoryToken(AffiliateAccount));
  });

  afterEach(async () => {
    await app.close();
  });

  describe('POST /user/affiliate/registration/restart', () => {
    it('should restart the registration process after rejection', async () => {
      // Arrange
      const userId = 1;
      const rejectedContract = {
        id: 1,
        userId,
        status: ContractStatus.REJECTED,
        contractType: ContractType.INDIVIDUAL,
      };
      
      const newContract = {
        id: 2,
        userId,
        status: ContractStatus.DRAFT,
        contractType: ContractType.INDIVIDUAL,
      };
      
      const currentState = {
        value: 'citizenIdUpload',
        context: {
          userId,
          accountType: 'PERSONAL',
          contractId: 2,
        },
      };
      
      // Mock service methods
      jest.spyOn(service, 'restartAfterRejection').mockResolvedValue(true);
      jest.spyOn(service, 'getCurrentState').mockReturnValue(currentState);
      
      // Mock repository methods
      affiliateContractRepository.findOne.mockResolvedValue(rejectedContract);
      affiliateContractRepository.create.mockReturnValue(newContract);
      affiliateContractRepository.save.mockResolvedValue(newContract);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/user/affiliate/registration/restart')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.state).toBe('citizenIdUpload');
          expect(res.body.data.context.accountType).toBe('PERSONAL');
          expect(res.body.data.context.contractId).toBe(2);
          expect(res.body.message).toBe('Đã bắt đầu lại quy trình ký hợp đồng');
        });
    });
  });

  describe('POST /user/affiliate/registration/upgrade-to-business', () => {
    it('should upgrade personal account to business account', async () => {
      // Arrange
      const userId = 1;
      const personalAccount = {
        id: 1,
        userId,
        accountType: 'PERSONAL',
      };
      
      const approvedContract = {
        id: 1,
        userId,
        status: ContractStatus.APPROVED,
        contractType: ContractType.INDIVIDUAL,
      };
      
      const newContract = {
        id: 2,
        userId,
        status: ContractStatus.DRAFT,
        contractType: ContractType.BUSINESS,
      };
      
      const currentState = {
        value: 'infoInput',
        context: {
          userId,
          accountType: 'BUSINESS',
          contractId: 2,
        },
      };
      
      // Mock service methods
      jest.spyOn(service, 'upgradeToBusinessAccount').mockResolvedValue(true);
      jest.spyOn(service, 'getCurrentState').mockReturnValue(currentState);
      
      // Mock repository methods
      affiliateAccountRepository.findOne.mockResolvedValue(personalAccount);
      affiliateContractRepository.findOne.mockResolvedValue(approvedContract);
      affiliateContractRepository.create.mockReturnValue(newContract);
      affiliateContractRepository.save.mockResolvedValue(newContract);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/user/affiliate/registration/upgrade-to-business')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.state).toBe('infoInput');
          expect(res.body.data.context.accountType).toBe('BUSINESS');
          expect(res.body.data.context.contractId).toBe(2);
          expect(res.body.message).toBe('Đã bắt đầu quy trình nâng cấp lên tài khoản doanh nghiệp');
        });
    });
  });
});
