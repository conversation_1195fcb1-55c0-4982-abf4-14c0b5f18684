# S<PERSON> đồ máy trạng thái (State Machine Diagrams)

<PERSON><PERSON><PERSON> mục này chứa các sơ đồ trực quan cho các máy trạng thái được sử dụng trong module Affiliate. <PERSON><PERSON><PERSON> sơ đồ này giúp hiểu rõ hơn về luồng trạng thái và các chuyển đổi có thể xảy ra.

## Sơ đồ đăng ký Affiliate cá nhân

```mermaid
stateDiagram-v2
    [*] --> termsAcceptance
    termsAcceptance --> personalInfo: ACCEPT_TERMS
    personalInfo --> bankInfo: SUBMIT_INFO
    bankInfo --> handwrittenSignature: SUBMIT_BANK_INFO
    handwrittenSignature --> otpVerification: SUBMIT_SIGNATURE
    otpVerification --> pendingApproval: VERIFY_OTP
    pendingApproval --> approved: ADMIN_APPROVE
    pendingApproval --> rejected: ADMIN_REJECT
    approved --> [*]
    rejected --> [*]
```

## <PERSON><PERSON> đồ quy trình rút tiền Affiliate

```mermaid
stateDiagram-v2
    [*] --> pending
    pending --> processing: PROCESS
    pending --> rejected: REJECT
    pending --> cancelled: CANCEL
    
    [*] --> invoiceNotUploaded
    invoiceNotUploaded --> invoiceUploaded: UPLOAD_INVOICE
    invoiceNotUploaded --> cancelled: CANCEL
    
    invoiceUploaded --> processing: APPROVE
    invoiceUploaded --> rejected: REJECT
    invoiceUploaded --> cancelled: CANCEL
    
    processing --> completed: COMPLETE
    processing --> rejected: REJECT
    
    completed --> [*]
    rejected --> [*]
    cancelled --> [*]
```

## Cách tạo sơ đồ

Các sơ đồ trên được tạo bằng cú pháp Mermaid, một công cụ tạo sơ đồ dựa trên JavaScript. Bạn có thể chỉnh sửa và tạo sơ đồ mới bằng cách sử dụng cú pháp Mermaid.

### Cú pháp cơ bản

```mermaid
stateDiagram-v2
    [*] --> StateA
    StateA --> StateB: Event1
    StateB --> StateC: Event2
    StateC --> [*]
```

### Trạng thái lồng nhau

```mermaid
stateDiagram-v2
    [*] --> First
    First --> Second
    
    state Second {
        [*] --> Second_A
        Second_A --> Second_B: Event1
        Second_B --> [*]
    }
    
    Second --> [*]
```

### Trạng thái song song

```mermaid
stateDiagram-v2
    [*] --> Fork
    
    state Fork <<fork>>
    Fork --> State1
    Fork --> State2
    
    state Join <<join>>
    State1 --> Join
    State2 --> Join
    
    Join --> [*]
```

## Công cụ tạo sơ đồ

Bạn có thể sử dụng các công cụ sau để tạo và chỉnh sửa sơ đồ Mermaid:

1. [Mermaid Live Editor](https://mermaid.live/) - Trình soạn thảo trực tuyến
2. Plugin Mermaid cho VS Code
3. [GitHub Markdown](https://github.blog/2022-02-14-include-diagrams-markdown-files-mermaid/) - GitHub hỗ trợ hiển thị sơ đồ Mermaid trong file Markdown

## Tích hợp với XState

XState cung cấp công cụ trực quan hóa máy trạng thái tại [XState Visualizer](https://stately.ai/viz). Bạn có thể dán mã máy trạng thái của mình vào đó để xem sơ đồ trực quan.

Ví dụ, để trực quan hóa máy trạng thái đăng ký Affiliate:

```javascript
const personalAffiliateMachine = Machine({
  id: 'personalAffiliate',
  initial: 'termsAcceptance',
  states: {
    termsAcceptance: {
      on: { ACCEPT_TERMS: 'personalInfo' }
    },
    personalInfo: {
      on: { SUBMIT_INFO: 'bankInfo' }
    },
    bankInfo: {
      on: { SUBMIT_BANK_INFO: 'handwrittenSignature' }
    },
    handwrittenSignature: {
      on: { SUBMIT_SIGNATURE: 'otpVerification' }
    },
    otpVerification: {
      on: { VERIFY_OTP: 'pendingApproval' }
    },
    pendingApproval: {
      on: {
        ADMIN_APPROVE: 'approved',
        ADMIN_REJECT: 'rejected'
      }
    },
    approved: { type: 'final' },
    rejected: { type: 'final' }
  }
});
```

Dán mã trên vào [XState Visualizer](https://stately.ai/viz) để xem sơ đồ trực quan.
