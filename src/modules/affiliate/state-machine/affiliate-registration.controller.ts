import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';
import { AffiliateRegistrationService } from './affiliate-registration.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc chọn loại tài khoản
 */
class SelectAccountTypeDto {
  @ApiProperty({
    description: 'Loại tài khoản affiliate',
    example: 'PERSONAL',
    enum: ['PERSONAL', 'BUSINESS'],
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(['PERSONAL', 'BUSINESS'])
  accountType: 'PERSONAL' | 'BUSINESS';
}

/**
 * DTO cho việc chấp nhận điều khoản
 */
class AcceptTermsDto {
  @ApiProperty({
    description: 'Xác nhận đã chấp nhận điều khoản',
    example: true,
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  termsAccepted: boolean;
}

/**
 * DTO cho việc nhập thông tin cá nhân
 */
class PersonalInfoDto {
  @ApiProperty({
    description: 'Họ tên đầy đủ',
    example: 'Nguyễn Văn A',
    required: true,
  })
  fullName: string;

  @ApiProperty({
    description: 'Email',
    example: '<EMAIL>',
    required: true,
  })
  email: string;

  @ApiProperty({
    description: 'Địa chỉ',
    example: 'Số 123, Đường ABC, Quận XYZ, TP. Hồ Chí Minh',
    required: true,
  })
  address: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '**********',
    required: true,
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Số CCCD/CMND',
    example: '079123456789',
    required: true,
  })
  citizenId: string;

  @ApiProperty({
    description: 'Ngày sinh (YYYY-MM-DD)',
    example: '1990-01-01',
    required: true,
  })
  dateOfBirth: string;

  @ApiProperty({
    description: 'Nơi cấp CCCD/CMND',
    example: 'Cục Cảnh sát quản lý hành chính về trật tự xã hội',
    required: true,
  })
  citizenIssuePlace: string;

  @ApiProperty({
    description: 'Ngày cấp CCCD/CMND (YYYY-MM-DD)',
    example: '2020-01-01',
    required: true,
  })
  citizenIssueDate: string;

  // Thông tin tài khoản ngân hàng
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB',
    required: true,
  })
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********',
    required: true,
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A',
    required: true,
  })
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Quận 1',
    required: false,
  })
  bankBranch?: string;
}

/**
 * DTO cho việc tải lên chữ ký
 */
class SignatureDto {
  @ApiProperty({
    description: 'Chữ ký dạng Base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  signature: string; // Base64 encoded signature image
}

/**
 * DTO cho việc xác thực OTP
 */
class VerifyOtpDto {
  @ApiProperty({
    description: 'Mã OTP',
    example: '123456',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  otp: string;
}

/**
 * DTO cho phản hồi trạng thái đăng ký
 */
class RegistrationStateDto {
  @ApiProperty({
    description: 'Trạng thái hiện tại của quá trình đăng ký',
    example: 'infoInput',
  })
  state: string;

  @ApiProperty({
    description: 'Dữ liệu ngữ cảnh của trạng thái',
    example: { accountType: 'PERSONAL', userData: { termsAccepted: true } },
  })
  context: any;

  @ApiProperty({
    description: 'URL của hợp đồng (nếu có)',
    example: 'contracts/123_1625097600000.pdf',
    required: false,
  })
  contractUrl?: string;
}

@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_REGISTRATION)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/affiliate/registration')
export class AffiliateRegistrationController {
  constructor(private readonly registrationService: AffiliateRegistrationService) {}

  /**
   * Lấy trạng thái đăng ký hiện tại
   */
  @Get('status')
  @ApiOperation({ summary: 'Lấy trạng thái đăng ký affiliate hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Trả về trạng thái đăng ký hiện tại',
  })
  async getRegistrationStatus(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Lấy trạng thái đăng ký thành công',
    );
  }

  /**
   * Chọn loại tài khoản (cá nhân hoặc doanh nghiệp)
   */
  @Post('select-account-type')
  @ApiOperation({ summary: 'Chọn loại tài khoản affiliate' })
  @ApiBody({ type: SelectAccountTypeDto })
  @ApiResponse({
    status: 200,
    description: 'Đã chọn loại tài khoản thành công',
  })
  async selectAccountType(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SelectAccountTypeDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái với loại tài khoản đã chọn
    await this.registrationService.startRegistration(user.id, {
      accountType: dto.accountType,
    });

    // Gửi sự kiện chọn loại tài khoản
    const eventType = dto.accountType === 'PERSONAL' ? 'SELECT_PERSONAL' : 'SELECT_BUSINESS';
    this.registrationService.sendEvent(user.id, eventType);

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      `Đã chọn loại tài khoản ${dto.accountType === 'PERSONAL' ? 'cá nhân' : 'doanh nghiệp'} thành công`,
    );
  }

  /**
   * Chấp nhận điều khoản và điều kiện
   */
  @Post('accept-terms')
  @ApiOperation({ summary: 'Chấp nhận điều khoản và điều kiện' })
  @ApiBody({ type: AcceptTermsDto })
  @ApiResponse({
    status: 200,
    description: 'Đã chấp nhận điều khoản thành công',
  })
  async acceptTerms(
    @CurrentUser() user: JwtPayload,
    @Body() dto: AcceptTermsDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    if (!dto.termsAccepted) {
      return ApiResponseDto.success(
        { state: 'termsAcceptance', context: null } as RegistrationStateDto,
        'Bạn phải chấp nhận điều khoản để tiếp tục',
      );
    }

    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    // Gửi sự kiện chấp nhận điều khoản
    this.registrationService.sendEvent(user.id, 'ACCEPT_TERMS', {
      userData: { termsAccepted: true },
    });

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã chấp nhận điều khoản thành công',
    );
  }

  /**
   * Nhập thông tin cá nhân
   */
  @Post('personal-info')
  @ApiOperation({ summary: 'Nhập thông tin cá nhân' })
  @ApiBody({ type: PersonalInfoDto })
  @ApiResponse({
    status: 200,
    description: 'Đã lưu thông tin cá nhân thành công',
  })
  async submitPersonalInfo(
    @CurrentUser() user: JwtPayload,
    @Body() dto: PersonalInfoDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    // Tạo hợp đồng từ thông tin người dùng (giả định)
    const contractId = Date.now();
    const contractPath = `contracts/${user.id}_${contractId}.pdf`;

    // Gửi sự kiện nhập thông tin cá nhân
    this.registrationService.sendEvent(user.id, 'SUBMIT_INFO', {
      userData: dto,
      contractId,
      contractPath,
    });

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
        contractUrl: contractPath, // Đường dẫn đến hợp đồng để hiển thị
      },
      'Đã lưu thông tin cá nhân thành công',
    );
  }

  /**
   * Tải lên chữ ký
   */
  @Post('signature')
  @ApiOperation({ summary: 'Tải lên chữ ký' })
  @ApiBody({ type: SignatureDto })
  @ApiResponse({
    status: 200,
    description: 'Đã tải lên chữ ký thành công',
  })
  async uploadSignature(
    @CurrentUser() user: JwtPayload,
    @Body() dto: SignatureDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    // Gửi sự kiện tải lên chữ ký
    this.registrationService.sendEvent(user.id, 'SUBMIT_SIGNATURE', {
      signatureUrl: dto.signature,
    });

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã tải lên chữ ký thành công',
    );
  }

  /**
   * Tiến hành xem xét hợp đồng
   */
  @Post('review-contract')
  @ApiOperation({ summary: 'Tiến hành xem xét hợp đồng' })
  @ApiResponse({
    status: 200,
    description: 'Đã chuyển sang bước xác thực OTP',
  })
  async reviewContract(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    // Gửi sự kiện tiến hành ký hợp đồng
    this.registrationService.sendEvent(user.id, 'PROCEED_TO_SIGN');

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã chuyển sang bước xác thực OTP',
    );
  }

  /**
   * Xác thực OTP để ký hợp đồng
   */
  @Post('verify-otp')
  @ApiOperation({ summary: 'Xác thực OTP để ký hợp đồng' })
  @ApiBody({ type: VerifyOtpDto })
  @ApiResponse({
    status: 200,
    description: 'Đã xác thực OTP thành công',
  })
  async verifyOtp(
    @CurrentUser() user: JwtPayload,
    @Body() dto: VerifyOtpDto,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    // Khởi tạo máy trạng thái nếu chưa tồn tại
    await this.registrationService.startRegistration(user.id);

    // Xác thực OTP (giả định)
    const verified = dto.otp === '123456'; // Giả sử OTP đúng là 123456

    // Gửi sự kiện xác thực OTP
    this.registrationService.sendEvent(user.id, 'VERIFY_OTP', {
      verified,
    });

    const currentState = this.registrationService.getCurrentState(user.id);

    const message = verified
      ? 'Đã xác thực OTP thành công, hợp đồng đang chờ phê duyệt'
      : 'Mã OTP không chính xác';

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      message
    );
  }

  /**
   * Ký lại hợp đồng sau khi bị từ chối
   */
  @Post('restart')
  @ApiOperation({ summary: 'Ký lại hợp đồng sau khi bị từ chối' })
  @ApiResponse({
    status: 200,
    description: 'Đã bắt đầu lại quy trình ký hợp đồng',
  })
  async restartAfterRejection(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    await this.registrationService.restartAfterRejection(user.id);

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã bắt đầu lại quy trình ký hợp đồng',
    );
  }

  /**
   * Nâng cấp lên tài khoản doanh nghiệp
   */
  @Post('upgrade-to-business')
  @ApiOperation({ summary: 'Nâng cấp lên tài khoản doanh nghiệp' })
  @ApiResponse({
    status: 200,
    description: 'Đã bắt đầu quy trình nâng cấp lên tài khoản doanh nghiệp',
  })
  async upgradeToBusinessAccount(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<RegistrationStateDto>> {
    await this.registrationService.upgradeToBusinessAccount(user.id);

    const currentState = this.registrationService.getCurrentState(user.id);

    return ApiResponseDto.success(
      {
        state: currentState ? currentState.value : null,
        context: currentState ? currentState.context : null,
      },
      'Đã bắt đầu quy trình nâng cấp lên tài khoản doanh nghiệp',
    );
  }
}
