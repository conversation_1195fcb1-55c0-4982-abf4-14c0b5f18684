import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho tham số truy vấn danh sách rank affiliate
 */
export class AffiliateRankQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái kích hoạt',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;
}
