import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';
import { ContractStatus } from '@modules/affiliate/enums';

/**
 * DTO cho tham số truy vấn hợp đồng affiliate
 */
export class AffiliateContractQueryDto extends QueryDto {
  /**
   * ID tài khoản affiliate
   */
  @ApiPropertyOptional({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  affiliateAccountId?: number;

  /**
   * Thời gian bắt đầu (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian bắt đầu (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  begin?: number;

  /**
   * Thời gian kết thúc (Unix timestamp)
   */
  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end?: number;

  /**
   * Trạng thái hợp đồng
   */
  @ApiPropertyOptional({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED,
  })
  @IsOptional()
  @IsEnum(ContractStatus)
  status?: ContractStatus;

  /**
   * Mã hợp đồng
   */
  @ApiPropertyOptional({
    description: 'Mã hợp đồng',
    example: 'HD-2023-001',
  })
  @IsOptional()
  @IsString()
  contractCode?: string;
}
