import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ContractStatus } from '@modules/affiliate/enums';

/**
 * DTO cho cập nhật trạng thái hợp đồng
 */
export class UpdateContractStatusDto {
  /**
   * Trạng thái hợp đồng
   */
  @ApiProperty({
    description: 'Trạng thái hợp đồng',
    enum: ContractStatus,
    example: ContractStatus.APPROVED,
  })
  @IsEnum(ContractStatus)
  status: ContractStatus;

  /**
   * <PERSON><PERSON> chú
   */
  @ApiProperty({
    description: '<PERSON>hi chú',
    example: 'Hợp đồng đã được ký và có hiệu lực',
    required: false,
  })
  @IsOptional()
  @IsString()
  note?: string;
}
