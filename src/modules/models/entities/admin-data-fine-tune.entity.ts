import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { DataFineTuneStatus } from '../constants/data-fine-tune-status.enum';

/**
 * Entity đại diện cho bảng admin_data_fine_tune trong cơ sở dữ liệu
 * Lưu bộ dữ liệu fine-tune do admin cung cấp, thường dùng cho hệ thống hoặc thử nghiệm
 */
@Entity('admin_data_fine_tune')
export class AdminDataFineTune {
  /**
   * ID duy nhất của bộ dữ liệu fine-tune do admin cung cấp
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên bộ dữ liệu
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * <PERSON><PERSON> tả bộ dữ liệu
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  /**
   * Key S3 của tập dữ liệu huấn luyện
   */
  @Column({ name: 'train_dataset', type: 'varchar', length: 255, nullable: false })
  trainDataset: string;

  /**
   * Key S3 của tập dữ liệu validation (nếu có)
   */
  @Column({ name: 'valid_dataset', type: 'varchar', length: 255, nullable: true })
  validDataset: string | null;

  /**
   * Thời điểm tạo bộ dữ liệu
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * ID nhân viên tạo bộ dữ liệu
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * Thời điểm cập nhật bộ dữ liệu
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * ID nhân viên cập nhật bộ dữ liệu
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời điểm xóa mềm
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * ID nhân viên xóa bộ dữ liệu
   */
  @Column({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * Nhà cung cấp AI
   */
  @Column({
    name: 'provider',
    type: 'enum',
    enum: ProviderFineTuneEnum,
    default: ProviderFineTuneEnum.OPENAI,
  })
  provider: ProviderFineTuneEnum;

  /**
   * Trạng thái của dataset
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: DataFineTuneStatus,
    default: DataFineTuneStatus.PENDING,
  })
  status: DataFineTuneStatus;
}
