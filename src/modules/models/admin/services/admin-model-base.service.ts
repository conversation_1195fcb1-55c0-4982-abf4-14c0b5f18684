import { Injectable, Logger } from '@nestjs/common';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ModelBaseRepository } from '../../repositories/model-base.repository';
import { SystemKeyLlmRepository } from '../../repositories/system-key-llm.repository';
import {
  CreateModelBaseDto,
  UpdateModelBaseDto,
  ModelBaseQueryDto,
  ModelBaseResponseDto
} from '../dto/model-base';
import { ModelBaseMapper } from '../mappers/model-base.mapper';
import { AppException } from '@common/exceptions';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';

/**
 * Service xử lý business logic cho Admin Model Base
 */
@Injectable()
export class AdminModelBaseService {
  private readonly logger = new Logger(AdminModelBaseService.name);

  constructor(
    private readonly modelBaseRepository: ModelBaseRepository,
    private readonly systemKeyLlmRepository: SystemKeyLlmRepository,
  ) {}

  /**
   * Tạo mới model base
   */
  // @Transactional()
  // async create(createDto: CreateModelBaseDto, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Creating model base by employee ${employeeId}`);

  //   // Validate model ID
  //   if (!ModelBaseMapper.validateModelId(createDto.modelId)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate model name
  //   if (!ModelBaseMapper.validateModelName(createDto.name)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate costs
  //   if (!ModelBaseMapper.validateCost(createDto.inputCostPer1kTokens) ||
  //       !ModelBaseMapper.validateCost(createDto.outputCostPer1kTokens)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate tokens
  //   if (!ModelBaseMapper.validateTokens(createDto.maxTokens) ||
  //       !ModelBaseMapper.validateTokens(createDto.contextWindow)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate metadata
  //   if (!ModelBaseMapper.validateMetadata(createDto.metadata)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Kiểm tra system key LLM tồn tại
  //   const systemKeyExists = await this.systemKeyLlmRepository.exists({
  //     where: { id: createDto.systemKeyLlmId }
  //   });
  //   if (!systemKeyExists) {
  //     throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
  //   }

  //   // Kiểm tra trùng model ID + provider
  //   const existsByModelId = await this.modelBaseRepository.existsByModelId(
  //     createDto.modelId,
  //     createDto.provider
  //   );
  //   if (existsByModelId) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_MODEL_ID_EXISTS);
  //   }

  //   // Kiểm tra trùng tên
  //   const existsByName = await this.modelBaseRepository.existsByName(createDto.name);
  //   if (existsByName) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NAME_EXISTS);
  //   }

  //   // Tạo entity mới
  //   const newModelBase = this.modelBaseRepository.create({
  //     systemKeyLlmId: createDto.systemKeyLlmId,
  //     modelId: createDto.modelId,
  //     name: createDto.name,
  //     description: createDto.description,
  //     provider: createDto.provider as any,
  //     maxTokens: createDto.maxTokens,
  //     contextWindow: createDto.contextWindow,
  //     inputCostPer1kTokens: createDto.inputCostPer1kTokens,
  //     outputCostPer1kTokens: createDto.outputCostPer1kTokens,
  //     status: (createDto.status || 'DRAFT') as any,
  //     isUserAccessible: createDto.isUserAccessible || false,
  //     isFineTunable: createDto.isFineTunable || false,
  //     metadata: createDto.metadata || {} as any,
  //     createdBy: employeeId,
  //     updatedBy: employeeId
  //   });

  //   // Lưu vào database
  //   await this.modelBaseRepository.save(newModelBase);

  //   this.logger.log(`Created model base ${newModelBase.id} successfully`);
  //   return ApiResponseDto.success({ message: 'Tạo model base thành công' });
  // }

  // /**
  //  * Lấy danh sách model base có phân trang
  //  */
  // async findAll(queryDto: ModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<ModelBaseResponseDto>>> {
  //   this.logger.log('Getting model base list');

  //   // Lấy dữ liệu từ repository
  //   const result = await this.modelBaseRepository.findWithPagination(queryDto);

  //   // Lấy thông tin employee names và system key names (nếu cần)
  //   const employeeIds = new Set<number>();
  //   const systemKeyLlmIds = new Set<string>();

  //   result.items.forEach(item => {
  //     if (item.createdBy) employeeIds.add(item.createdBy);
  //     if (item.updatedBy) employeeIds.add(item.updatedBy);
  //     systemKeyLlmIds.add(item.systemKeyLlmId);
  //   });

  //   // TODO: Implement employee service to get names
  //   // const employeeNames = await this.getEmployeeNames(Array.from(employeeIds));

  //   // TODO: Get system key LLM names
  //   // const systemKeyLlmNames = await this.getSystemKeyLlmNames(Array.from(systemKeyLlmIds));

  //   // Convert sang DTO
  //   const items = ModelBaseMapper.toResponseDtoArray(result.items);

  //   return ApiResponseDto.paginated({
  //     items,
  //     meta: result.meta
  //   });
  // }

  // /**
  //  * Lấy chi tiết model base
  //  */
  // async findOne(id: string): Promise<ApiResponseDto<ModelBaseResponseDto>> {
  //   this.logger.log(`Getting model base detail: ${id}`);

  //   // Tìm model base
  //   const modelBase = await this.modelBaseRepository.findById(id);
  //   if (!modelBase) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //   }

  //   // TODO: Lấy thông tin employee names và system key name
  //   // const createdByName = modelBase.createdBy ? await this.getEmployeeName(modelBase.createdBy) : null;
  //   // const updatedByName = modelBase.updatedBy ? await this.getEmployeeName(modelBase.updatedBy) : null;
  //   // const systemKeyLlmName = await this.getSystemKeyLlmName(modelBase.systemKeyLlmId);

  //   // Convert sang DTO
  //   const responseDto = ModelBaseMapper.toResponseDto(modelBase);

  //   return ApiResponseDto.success(responseDto);
  // }

  // /**
  //  * Cập nhật model base
  //  */
  // @Transactional()
  // async update(id: string, updateDto: UpdateModelBaseDto, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Updating model base ${id} by employee ${employeeId}`);

  //   // Tìm model base hiện tại
  //   const existingModelBase = await this.modelBaseRepository.findById(id);
  //   if (!existingModelBase) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //   }

  //   // Validate các fields nếu có thay đổi
  //   if (updateDto.modelId && !ModelBaseMapper.validateModelId(updateDto.modelId)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (updateDto.name && !ModelBaseMapper.validateModelName(updateDto.name)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (!ModelBaseMapper.validateCost(updateDto.inputCostPer1kTokens) ||
  //       !ModelBaseMapper.validateCost(updateDto.outputCostPer1kTokens)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (!ModelBaseMapper.validateTokens(updateDto.maxTokens) ||
  //       !ModelBaseMapper.validateTokens(updateDto.contextWindow)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (!ModelBaseMapper.validateMetadata(updateDto.metadata)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Kiểm tra system key LLM tồn tại (nếu có thay đổi)
  //   if (updateDto.systemKeyLlmId) {
  //     const systemKeyExists = await this.systemKeyLlmRepository.exists({
  //       where: { id: updateDto.systemKeyLlmId }
  //     });
  //     if (!systemKeyExists) {
  //       throw new AppException(MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND);
  //     }
  //   }

  //   // Kiểm tra trùng model ID + provider (nếu có thay đổi)
  //   if (updateDto.modelId && updateDto.provider &&
  //       (updateDto.modelId !== existingModelBase.modelId || updateDto.provider !== existingModelBase.provider)) {
  //     const existsByModelId = await this.modelBaseRepository.existsByModelId(
  //       updateDto.modelId,
  //       updateDto.provider,
  //       id
  //     );
  //     if (existsByModelId) {
  //       throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_MODEL_ID_EXISTS);
  //     }
  //   }

  //   // Kiểm tra trùng tên (nếu có thay đổi tên)
  //   if (updateDto.name && updateDto.name !== existingModelBase.name) {
  //     const existsByName = await this.modelBaseRepository.existsByName(updateDto.name, id);
  //     if (existsByName) {
  //       throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NAME_EXISTS);
  //     }
  //   }

  //   // Cập nhật các trường
  //   if (updateDto.systemKeyLlmId !== undefined) {
  //     existingModelBase.systemKeyLlmId = updateDto.systemKeyLlmId;
  //   }
  //   if (updateDto.modelId !== undefined) {
  //     existingModelBase.modelId = updateDto.modelId;
  //   }
  //   if (updateDto.name !== undefined) {
  //     existingModelBase.name = updateDto.name;
  //   }
  //   if (updateDto.description !== undefined) {
  //     existingModelBase.description = updateDto.description;
  //   }
  //   if (updateDto.provider !== undefined) {
  //     existingModelBase.provider = updateDto.provider as any;
  //   }
  //   if (updateDto.maxTokens !== undefined) {
  //     existingModelBase.maxTokens = updateDto.maxTokens;
  //   }
  //   if (updateDto.contextWindow !== undefined) {
  //     existingModelBase.contextWindow = updateDto.contextWindow;
  //   }
  //   if (updateDto.inputCostPer1kTokens !== undefined) {
  //     existingModelBase.inputCostPer1kTokens = updateDto.inputCostPer1kTokens;
  //   }
  //   if (updateDto.outputCostPer1kTokens !== undefined) {
  //     existingModelBase.outputCostPer1kTokens = updateDto.outputCostPer1kTokens;
  //   }
  //   if (updateDto.status !== undefined) {
  //     existingModelBase.status = updateDto.status as any;
  //   }
  //   if (updateDto.isUserAccessible !== undefined) {
  //     existingModelBase.isUserAccessible = updateDto.isUserAccessible;
  //   }
  //   if (updateDto.isFineTunable !== undefined) {
  //     existingModelBase.isFineTunable = updateDto.isFineTunable;
  //   }
  //   if (updateDto.metadata !== undefined) {
  //     existingModelBase.metadata = updateDto.metadata as any;
  //   }

  //   existingModelBase.updatedBy = employeeId;
  //   existingModelBase.updatedAt = Date.now();

  //   // Lưu thay đổi
  //   await this.modelBaseRepository.save(existingModelBase);

  //   this.logger.log(`Updated model base ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Cập nhật model base thành công' });
  // }

  // /**
  //  * Xóa model base (soft delete)
  //  */
  // @Transactional()
  // async remove(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Soft deleting model base ${id} by employee ${employeeId}`);

  //   // Kiểm tra model base tồn tại
  //   const existingModelBase = await this.modelBaseRepository.findById(id);
  //   if (!existingModelBase) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //   }

  //   // Thực hiện soft delete
  //   const deleted = await this.modelBaseRepository.softDeleteModelBase(id, employeeId);
  //   if (!deleted) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_DELETE_FAILED);
  //   }

  //   this.logger.log(`Soft deleted model base ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Xóa model base thành công' });
  // }

  // /**
  //  * Lấy danh sách model base đã xóa
  //  */
  // async findDeleted(queryDto: ModelBaseQueryDto): Promise<ApiResponseDto<PaginatedResult<ModelBaseResponseDto>>> {
  //   this.logger.log('Getting deleted model base list');

  //   // Lấy dữ liệu từ repository
  //   const result = await this.modelBaseRepository.findDeletedWithPagination(queryDto);

  //   // Lấy thông tin employee names và system key names (nếu cần)
  //   const employeeIds = new Set<number>();
  //   const systemKeyLlmIds = new Set<string>();

  //   result.items.forEach(item => {
  //     if (item.createdBy) employeeIds.add(item.createdBy);
  //     if (item.updatedBy) employeeIds.add(item.updatedBy);
  //     if (item.deletedBy) employeeIds.add(item.deletedBy);
  //     systemKeyLlmIds.add(item.systemKeyLlmId);
  //   });

  //   // TODO: Implement employee service to get names
  //   // const employeeNames = await this.getEmployeeNames(Array.from(employeeIds));

  //   // TODO: Get system key LLM names
  //   // const systemKeyLlmNames = await this.getSystemKeyLlmNames(Array.from(systemKeyLlmIds));

  //   // Convert sang DTO
  //   const items = ModelBaseMapper.toResponseDtoArray(result.items);

  //   return ApiResponseDto.paginated({
  //     items,
  //     meta: result.meta
  //   });
  // }

  // /**
  //  * Khôi phục model base đã xóa
  //  */
  // @Transactional()
  // async restore(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Restoring model base ${id} by employee ${employeeId}`);

  //   // Thực hiện khôi phục
  //   const restored = await this.modelBaseRepository.restoreModelBase(id);
  //   if (!restored) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_RESTORE_FAILED);
  //   }

  //   this.logger.log(`Restored model base ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Khôi phục model base thành công' });
  // }

  // /**
  //  * Thêm API key cho model base
  //  */
  // async addApiKey(id: string, apiKeyDto: any, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Adding API key to model base ${id} by employee ${employeeId}`);
  //   return ApiResponseDto.success({ message: 'Thêm API key thành công' });
  // }

  // /**
  //  * Xóa API key khỏi model base (deprecated - sử dụng system key LLM thay thế)
  //  */
  // async removeApiKey(id: string, keyId: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Removing API key ${keyId} from model base ${id} by employee ${employeeId}`);
  //   // TODO: Implement hoặc deprecate method này
  //   return ApiResponseDto.success({ message: 'Xóa API key thành công' });
  // }

  // /**
  //  * Lấy models theo provider
  //  * @param provider Provider
  //  * @returns Danh sách models
  //  */
  // async getModelsByProvider(provider: string): Promise<ApiResponseDto<ModelBaseResponseDto[]>> {
  //   this.logger.log(`Getting models by provider: ${provider}`);

  //   const models = await this.modelBaseRepository.findByProvider(provider);
  //   const items = ModelBaseMapper.toResponseDtoArray(models);

  //   return ApiResponseDto.success(items);
  // }

  // /**
  //  * Lấy models user có thể access
  //  * @returns Danh sách models
  //  */
  // async getUserAccessibleModels(): Promise<ApiResponseDto<ModelBaseResponseDto[]>> {
  //   this.logger.log('Getting user accessible models');

  //   const models = await this.modelBaseRepository.findUserAccessible();
  //   const items = ModelBaseMapper.toResponseDtoArray(models);

  //   return ApiResponseDto.success(items);
  // }

  // /**
  //  * Lấy models có thể fine-tune
  //  * @returns Danh sách models
  //  */
  // async getFineTunableModels(): Promise<ApiResponseDto<ModelBaseResponseDto[]>> {
  //   this.logger.log('Getting fine-tunable models');

  //   const models = await this.modelBaseRepository.findFineTunable();
  //   const items = ModelBaseMapper.toResponseDtoArray(models);

  //   return ApiResponseDto.success(items);
  // }

  // /**
  //  * Tính toán chi phí ước tính cho request
  //  * @param modelId Model ID
  //  * @param inputTokens Số input tokens
  //  * @param outputTokens Số output tokens
  //  * @returns Chi phí ước tính
  //  */
  // async calculateCost(
  //   modelId: string,
  //   inputTokens: number,
  //   outputTokens: number
  // ): Promise<ApiResponseDto<{ estimatedCost: number; breakdown: any }>> {
  //   this.logger.log(`Calculating cost for model ${modelId}: ${inputTokens} input, ${outputTokens} output tokens`);

  //   const model = await this.modelBaseRepository.findById(modelId);
  //   if (!model) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_BASE_NOT_FOUND);
  //   }

  //   const estimatedCost = ModelBaseMapper.calculateEstimatedCost(
  //     inputTokens,
  //     outputTokens,
  //     model.inputCostPer1kTokens,
  //     model.outputCostPer1kTokens
  //   );

  //   const breakdown = {
  //     inputCost: model.inputCostPer1kTokens ? (inputTokens / 1000) * model.inputCostPer1kTokens : 0,
  //     outputCost: model.outputCostPer1kTokens ? (outputTokens / 1000) * model.outputCostPer1kTokens : 0,
  //     inputTokens,
  //     outputTokens,
  //     inputCostPer1k: model.inputCostPer1kTokens,
  //     outputCostPer1k: model.outputCostPer1kTokens
  //   };

  //   return ApiResponseDto.success({
  //     estimatedCost,
  //     breakdown
  //   });
  // }
}
