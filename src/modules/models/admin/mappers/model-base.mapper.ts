import { ModelBase } from '../../entities/model-base.entity';
import { ModelBaseResponseDto } from '../dto/model-base';

/**
 * Mapper cho ModelBase
 * Chuyển đổi giữa entity và DTO
 */
export class ModelBaseMapper {
  /**
   * Chuyển đổi entity sang response DTO
   * @param entity ModelBase entity
   * @param createdByName Tên người tạo (optional)
   * @param updatedByName Tên người cập nhật (optional)
   * @param deletedByName Tên người xóa (optional)
   * @param systemKeyLlmName Tên system key LLM (optional)
   * @returns ModelBaseResponseDto
   */
  // static toResponseDto(
  //   entity: ModelBase,
  //   createdByName?: string | null,
  //   updatedByName?: string | null,
  //   deletedByName?: string | null,
  //   systemKeyLlmName?: string | null
  // ): ModelBaseResponseDto {
  //   return {
  //     id: entity.id,
  //     name: entity.name,
  //     description: entity.description,
  //     createdAt: entity.createdAt,
  //     createdByName,
  //     updatedAt: entity.updatedAt,
  //     updatedByName,
  //     deletedAt: entity.deletedAt,
  //     deletedBy: entity.deletedBy,
  //     deletedByName,
  //     systemKeyLlmName
  //   };
  // }

  /**
   * Chuyển đổi mảng entities sang mảng response DTOs
   * @param entities Mảng ModelBase entities
   * @param employeeNames Map ID employee -> tên (optional)
   * @param systemKeyLlmNames Map system key LLM ID -> tên (optional)
   * @returns Mảng ModelBaseResponseDto
   */
  // static toResponseDtoArray(
  //   entities: ModelBase[],
  //   employeeNames?: Map<number, string>,
  //   systemKeyLlmNames?: Map<string, string>
  // ): ModelBaseResponseDto[] {
  //   return entities.map(entity => {
  //     const createdByName = entity.createdBy && employeeNames 
  //       ? employeeNames.get(entity.createdBy) || null 
  //       : null;
  //     const updatedByName = entity.updatedBy && employeeNames 
  //       ? employeeNames.get(entity.updatedBy) || null 
  //       : null;
  //     const deletedByName = entity.deletedBy && employeeNames 
  //       ? employeeNames.get(entity.deletedBy) || null 
  //       : null;

  //     return this.toResponseDto(entity, createdByName, updatedByName, deletedByName);
  //   });
  // }

  /**
   * Validate model ID format
   * @param modelId Model ID cần validate
   * @returns true nếu hợp lệ
   */
  static validateModelId(modelId: string): boolean {
    if (!modelId || modelId.trim().length === 0) {
      return false;
    }

    // Model ID không được chứa ký tự đặc biệt nguy hiểm
    const dangerousChars = /[<>\"'&]/;
    if (dangerousChars.test(modelId)) {
      return false;
    }

    // Model ID phải có ít nhất 2 ký tự
    if (modelId.trim().length < 2) {
      return false;
    }

    // Model ID không được chứa khoảng trắng
    if (/\s/.test(modelId)) {
      return false;
    }

    return true;
  }

  /**
   * Validate model name
   * @param name Tên model cần validate
   * @returns true nếu hợp lệ
   */
  static validateModelName(name: string): boolean {
    if (!name || name.trim().length === 0) {
      return false;
    }

    // Tên model không được chứa ký tự đặc biệt nguy hiểm
    const dangerousChars = /[<>\"'&]/;
    if (dangerousChars.test(name)) {
      return false;
    }

    // Tên model phải có ít nhất 2 ký tự
    if (name.trim().length < 2) {
      return false;
    }

    return true;
  }

  /**
   * Validate cost values
   * @param cost Chi phí cần validate
   * @returns true nếu hợp lệ
   */
  static validateCost(cost?: number): boolean {
    if (cost === undefined || cost === null) {
      return true; // Optional field
    }

    if (typeof cost !== 'number') {
      return false;
    }

    // Chi phí phải >= 0
    if (cost < 0) {
      return false;
    }

    // Chi phí không được quá lớn (max $1000 per 1k tokens)
    if (cost > 1000) {
      return false;
    }

    return true;
  }

  /**
   * Validate token values
   * @param tokens Số tokens cần validate
   * @returns true nếu hợp lệ
   */
  static validateTokens(tokens?: number): boolean {
    if (tokens === undefined || tokens === null) {
      return true; // Optional field
    }

    if (typeof tokens !== 'number') {
      return false;
    }

    // Tokens phải > 0
    if (tokens <= 0) {
      return false;
    }

    // Tokens không được quá lớn (max 10M tokens)
    if (tokens > 10000000) {
      return false;
    }

    return true;
  }

  /**
   * Validate metadata object
   * @param metadata Metadata cần validate
   * @returns true nếu hợp lệ
   */
  static validateMetadata(metadata?: any): boolean {
    if (!metadata) {
      return true; // Optional field
    }

    if (typeof metadata !== 'object') {
      return false;
    }

    // Kiểm tra kích thước metadata (không quá 100 keys)
    if (Object.keys(metadata).length > 100) {
      return false;
    }

    // Kiểm tra từng key và value
    for (const [key, value] of Object.entries(metadata)) {
      // Key không được chứa ký tự đặc biệt
      if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
        return false;
      }

      // Value phải là primitive types hoặc simple objects
      if (typeof value === 'function' || typeof value === 'symbol') {
        return false;
      }

      // Nếu value là object, kiểm tra độ sâu (max 2 levels)
      if (typeof value === 'object' && value !== null) {
        try {
          JSON.stringify(value);
        } catch {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Calculate estimated cost per request
   * @param inputTokens Số input tokens
   * @param outputTokens Số output tokens
   * @param inputCostPer1k Chi phí input per 1k tokens
   * @param outputCostPer1k Chi phí output per 1k tokens
   * @returns Chi phí ước tính
   */
  static calculateEstimatedCost(
    inputTokens: number,
    outputTokens: number,
    inputCostPer1k?: number,
    outputCostPer1k?: number
  ): number {
    if (!inputCostPer1k || !outputCostPer1k) {
      return 0;
    }

    const inputCost = (inputTokens / 1000) * inputCostPer1k;
    const outputCost = (outputTokens / 1000) * outputCostPer1k;

    return inputCost + outputCost;
  }

  /**
   * Format cost for display
   * @param cost Chi phí
   * @returns Chuỗi hiển thị
   */
  static formatCost(cost?: number): string {
    if (!cost) {
      return 'Free';
    }

    if (cost < 0.001) {
      return `$${(cost * 1000000).toFixed(2)}/1M tokens`;
    }

    if (cost < 1) {
      return `$${cost.toFixed(4)}/1k tokens`;
    }

    return `$${cost.toFixed(2)}/1k tokens`;
  }

  /**
   * Format tokens for display
   * @param tokens Số tokens
   * @returns Chuỗi hiển thị
   */
  static formatTokens(tokens?: number): string {
    if (!tokens) {
      return 'N/A';
    }

    if (tokens >= 1000000) {
      return `${(tokens / 1000000).toFixed(1)}M`;
    }

    if (tokens >= 1000) {
      return `${(tokens / 1000).toFixed(1)}k`;
    }

    return tokens.toString();
  }

  /**
   * Check if model supports feature
   * @param metadata Model metadata
   * @param feature Feature to check
   * @returns true nếu support
   */
  static supportsFeature(metadata?: any, feature?: string): boolean {
    if (!metadata || !feature) {
      return false;
    }

    const features = metadata.features;
    if (!Array.isArray(features)) {
      return false;
    }

    return features.includes(feature.toLowerCase());
  }
}
