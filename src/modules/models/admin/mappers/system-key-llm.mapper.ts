import { SystemKeyLlm } from '../../entities/system-key-llm.entity';
import { SystemKeyLlmResponseDto, SystemKeyLlmDetailResponseDto, TestConnectionResponseDto } from '../dto/system-key-llm';
import { EmployeeInfoSimpleDto } from '@modules/employee/dto/employee-info-simple.dto';

/**
 * Mapper cho SystemKeyLlm
 * Chuyển đổi giữa entity và DTO
 */
export class SystemKeyLlmMapper {
  /**
   * Chuyển đổi entity sang response DTO đơn giản
   * @param entity SystemKeyLlm entity
   * @returns SystemKeyLlmResponseDto
   */
  static toResponseDto(entity: SystemKeyLlm): SystemKeyLlmResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      provider: entity.provider
    };
  }

  /**
   * Chuyển đổi entity sang response DTO chi tiết
   * @param entity SystemKeyLlm entity
   * @param createdByInfo Thông tin người tạo (optional)
   * @param updatedByInfo Thông tin người cập nhật (optional)
   * @returns SystemKeyLlmDetailResponseDto
   */
  static toDetailResponseDto(
    entity: SystemKeyLlm,
    createdByInfo?: EmployeeInfoSimpleDto | null,
    updatedByInfo?: EmployeeInfoSimpleDto | null
  ): SystemKeyLlmDetailResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      provider: entity.provider,
      createdAt: entity.createdAt,
      created: createdByInfo || null,
      updatedAt: entity.updatedAt,
      updated: updatedByInfo || null,
    };
  }

  /**
   * Chuyển đổi mảng entities sang mảng response DTOs đơn giản
   * @param entities Mảng SystemKeyLlm entities
   * @returns Mảng SystemKeyLlmResponseDto
   */
  static toResponseDtoArray(entities: SystemKeyLlm[]): SystemKeyLlmResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Tạo response DTO cho test connection thành công
   * @param details Chi tiết kết nối
   * @returns TestConnectionResponseDto
   */
  static toTestConnectionSuccessDto(): TestConnectionResponseDto {
    return {
      success: true,
      message: 'Kết nối API key thành công',
    };
  }

  /**
   * Tạo response DTO cho test connection thất bại
   * @param error Thông tin lỗi
   * @returns TestConnectionResponseDto
   */
  static toTestConnectionFailureDto(error: string): TestConnectionResponseDto {
    return {
      success: false,
      message: 'Kết nối API key thất bại',
      error
    };
  }

  /**
   * Mask API key để hiển thị an toàn
   * @param apiKey API key cần mask
   * @returns API key đã được mask
   */
  static maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length <= 8) {
      return '***';
    }

    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.max(4, apiKey.length - 8));
    
    return `${start}${middle}${end}`;
  }

  /**
   * Kiểm tra key có hết hạn không
   * @param expiresAt Timestamp hết hạn
   * @returns true nếu đã hết hạn
   */
  static isExpired(expiresAt?: number | null): boolean {
    if (!expiresAt) return false;
    return expiresAt < Date.now();
  }

  /**
   * Kiểm tra key sắp hết hạn (trong 7 ngày)
   * @param expiresAt Timestamp hết hạn
   * @returns true nếu sắp hết hạn
   */
  static isExpiringSoon(expiresAt?: number | null): boolean {
    if (!expiresAt) return false;
    const sevenDaysFromNow = Date.now() + (7 * 24 * 60 * 60 * 1000);
    return expiresAt <= sevenDaysFromNow && expiresAt > Date.now();
  }
}
