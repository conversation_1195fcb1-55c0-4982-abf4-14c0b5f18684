import { ProviderEnum } from '@/modules/models/constants';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  Min
} from 'class-validator';

/**
 * DTO cho việc tạo mới model base
 */
export class CreateModelBaseDto {
  /**
   * ID của system key LLM đượ<PERSON> sử dụng
   */
  @ApiProperty({
    description: 'ID của system key LLM được sử dụng',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  systemKeyLlmId: string;

  /**
   * ID model từ nhà cung cấp
   */
  @ApiProperty({
    description: 'ID model từ nhà cung cấp',
    example: 'gpt-4-turbo-preview',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  modelId: string;

  /**
   * Tên hiển thị của model
   */
  @ApiProperty({
    description: 'Tên hiển thị của model',
    example: 'GPT-4 Turbo Preview',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả về model
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model',
    example: 'Model GPT-4 Turbo với khả năng xử lý văn bản và hình ảnh',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsEnum(ProviderEnum)
  @IsNotEmpty()
  provider: ProviderEnum;

  /**
   * Số token tối đa
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa',
    example: 4096,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  /**
   * Kích thước context window
   */
  @ApiPropertyOptional({
    description: 'Kích thước context window',
    example: 128000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  contextWindow?: number;

  /**
   * Chi phí input per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí input per 1k tokens (USD)',
    example: 0.01,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  inputCostPer1kTokens?: number;

  /**
   * Chi phí output per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí output per 1k tokens (USD)',
    example: 0.03,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  outputCostPer1kTokens?: number;

  /**
   * Có cho phép user sử dụng không
   */
  @ApiPropertyOptional({
    description: 'Có cho phép user sử dụng không',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isUserAccessible?: boolean;

  /**
   * Có cho phép fine-tuning không
   */
  @ApiPropertyOptional({
    description: 'Có cho phép fine-tuning không',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isFineTunable?: boolean;

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung (JSON)',
    example: { version: '1.0', features: ['chat', 'completion'] },
  })
  @IsOptional()
  metadata?: any;
}
