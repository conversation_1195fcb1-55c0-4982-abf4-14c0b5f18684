import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsUUID } from 'class-validator';

/**
 * DTO cho bulk delete admin data fine tune
 */
export class BulkDeleteAdminDataFineTuneDto {
  /**
   * Danh sách ID của datasets cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID của datasets cần xóa',
    example: ['uuid1', 'uuid2', 'uuid3'],
    type: [String]
  })
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsNotEmpty({ message: 'Danh sách IDs không được rỗng' })
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  ids: string[];
}
