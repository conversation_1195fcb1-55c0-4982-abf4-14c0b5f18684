import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { DataFineTuneStatus } from '../../../constants/data-fine-tune-status.enum';


/**
 * DTO cho việc cập nhật trạng thái bulk
 */
export class BulkUpdateStatusAdminDataFineTuneDto {
  /**
   * Danh sách ID của datasets cần cập nhật trạng thái
   */
  @ApiProperty({
    description: 'Danh sách ID của datasets cần cập nhật trạng thái',
    example: ['uuid1', 'uuid2', 'uuid3'],
    type: [String]
  })
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  ids: string[];

  /**
   * Trạng thái mới
   */
  @ApiProperty({
    description: 'Trạng thái mới của datasets',
    enum: DataFineTuneStatus,
    example: DataFineTuneStatus.APPROVED
  })
  @IsEnum(DataFineTuneStatus, { message: 'Trạng thái không hợp lệ' })
  status: DataFineTuneStatus;

  /**
   * Ghi chú (optional)
   */
  @ApiProperty({
    description: 'Ghi chú về việc thay đổi trạng thái',
    example: 'Batch approval cho datasets đã kiểm tra',
    required: false
  })
  @IsOptional()
  @IsString({ message: 'Ghi chú phải là chuỗi' })
  note?: string;
}
