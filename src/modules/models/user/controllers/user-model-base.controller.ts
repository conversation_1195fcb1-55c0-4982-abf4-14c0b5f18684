import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  UserModelBaseQueryDto
} from '../dto/user-model-base';
import { UserModelBaseService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho User Model Base
 */
@ApiTags(SWAGGER_API_TAGS.USER_BASE_MODEL)
@Controller('user/model-base')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserModelBaseController {
  constructor(private readonly userModelBaseService: UserModelBaseService) { }

  // /**
  //  * Lấy danh sách model base do admin cung cấp
  //  */
  // @Get()
  // @ApiOperation({
  //   summary: 'Lấy danh sách model base do admin cung cấp',
  //   description: 'API này trả về danh sách model base mà admin đã cấu hình và kích hoạt'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Danh sách model base do admin cung cấp',
  //   type: ApiResponseDto
  // })
  // findAdminProvided(@Query() queryDto: UserModelBaseQueryDto) {
  //   return this.userModelBaseService.findAdminProvided(queryDto);
  // }

  // /**
  //  * Lấy danh sách model từ một user key cụ thể
  //  */
  // @Get('from-user-key/:keyId')
  // @ApiOperation({
  //   summary: 'Lấy danh sách model từ một user key cụ thể',
  //   description: 'API này lấy model từ một user key LLM cụ thể và filter theo model registry'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Danh sách model từ user key cụ thể',
  //   type: ApiResponseDto
  // })
  // findFromSpecificUserKey(
  //   @CurrentUser('id') userId: number,
  //   @Param('keyId', ParseUUIDPipe) keyId: string,
  //   @Query() queryDto: UserModelBaseQueryDto
  // ) {
  //   return this.userModelBaseService.findFromSpecificUserKey(userId, keyId, queryDto);
  // }

  // /**
  //  * Lấy thông tin chi tiết model
  //  */
  // @Get('model-info/:modelId')
  // @ApiOperation({
  //   summary: 'Lấy thông tin chi tiết model',
  //   description: 'API này lấy thông tin chi tiết của một model cụ thể'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thông tin chi tiết model',
  //   type: ApiResponseDto
  // })
  // getModelInfo(
  //   @CurrentUser('id') userId: number,
  //   @Param('modelId') modelId: string,
  //   @Query('keyId') keyId?: string
  // ) {
  //   return this.userModelBaseService.getModelInfo(userId, modelId, keyId);
  // }

  // /**
  //  * Lấy models có thể fine-tune
  //  */
  // @Get('fine-tunable')
  // @ApiOperation({
  //   summary: 'Lấy models có thể fine-tune',
  //   description: 'API này lấy models có khả năng fine-tuning'
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Models có thể fine-tune',
  //   type: ApiResponseDto
  // })
  // getFineTunableModels(
  //   @CurrentUser('id') userId: number,
  //   @Query() queryDto: UserModelBaseQueryDto
  // ) {
  //   return this.userModelBaseService.getFineTunableModels(userId, queryDto);
  // }
}
