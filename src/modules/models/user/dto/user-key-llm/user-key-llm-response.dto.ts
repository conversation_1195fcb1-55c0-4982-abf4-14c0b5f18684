import { ProviderEnum } from '@/modules/models/constants';
import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho response của user key LLM
 */
@Exclude()
export class UserKeyLlmResponseDto {
  /**
   * UUID của user key LLM
   */
  @ApiProperty({
    description: 'UUID của user key LLM',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * Tên định danh cho key
   */
  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'My OpenAI Key',
  })
  @Expose()
  name: string;

  /**
   * Nhà cung cấp LLM
   */
  @ApiProperty({
    description: 'Nhà cung cấp LLM',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @Expose()
  provider: ProviderEnum;

  /**
   * Thờ<PERSON> gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thờ<PERSON> gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thời gian cập nhật (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}
