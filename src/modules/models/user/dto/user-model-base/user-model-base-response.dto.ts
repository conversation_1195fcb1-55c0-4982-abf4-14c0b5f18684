import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * DTO cho response của user model base
 */
@Exclude()
export class UserModelBaseResponseDto {
  /**
   * UUID của model base
   */
  @ApiProperty({
    description: 'UUID của model base',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * Tên hiển thị của model
   */
  @ApiProperty({
    description: 'Tên hiển thị của model',
    example: 'GPT-4 Turbo',
  })
  @Expose()
  name: string;

  /**
   * Model ID từ nhà cung cấp
   */
  @ApiProperty({
    description: 'Model ID từ nhà cung cấp',
    example: 'gpt-4-turbo-preview',
  })
  @Expose()
  modelId: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @Expose()
  provider: ProviderEnumq;

  /**
   * Mô tả về model
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model',
    example: 'GPT-4 Turbo with improved instruction following',
  })
  @Expose()
  description?: string;

  /**
   * User có thể access model này không
   */
  @ApiProperty({
    description: 'User có thể access model này không',
    example: true,
  })
  @Expose()
  isUserAccessible: boolean;

  /**
   * Model có thể fine-tune không
   */
  @ApiProperty({
    description: 'Model có thể fine-tune không',
    example: false,
  })
  @Expose()
  isFineTunable: boolean;

  /**
   * Chi phí input per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí input per 1k tokens (USD)',
    example: 0.01,
  })
  @Expose()
  inputCostPer1kTokens?: number;

  /**
   * Chi phí output per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí output per 1k tokens (USD)',
    example: 0.03,
  })
  @Expose()
  outputCostPer1kTokens?: number;

  /**
   * Độ dài context tối đa
   */
  @ApiPropertyOptional({
    description: 'Độ dài context tối đa',
    example: 128000,
  })
  @Expose()
  contextLength?: number;

  /**
   * Capabilities của model
   */
  @ApiPropertyOptional({
    description: 'Capabilities của model',
    example: ['text-generation', 'function-calling'],
  })
  @Expose()
  capabilities?: string[];

  /**
   * Source của model (admin hoặc user-key)
   */
  @ApiProperty({
    description: 'Source của model (admin hoặc user-key)',
    enum: ['admin', 'user-key'],
    example: 'admin',
  })
  @Expose()
  source: 'admin' | 'user-key';

  /**
   * Thông tin user key (nếu source là user-key)
   */
  @ApiPropertyOptional({
    description: 'Thông tin user key (nếu source là user-key)',
    example: {
      keyId: '123e4567-e89b-12d3-a456-************',
      keyName: 'My OpenAI Key',
      isDefault: true
    },
  })
  @Expose()
  userKeyInfo?: {
    keyId: string;
    keyName: string;
    isDefault: boolean;
  };

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: {
      version: '2024-01',
      trainingData: 'Up to April 2023',
      multimodal: false
    },
  })
  @Expose()
  metadata?: any;

  /**
   * Thời gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thời gian cập nhật (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;

  /**
   * Availability status
   */
  @ApiPropertyOptional({
    description: 'Availability status',
    example: {
      isAvailable: true,
      lastChecked: 1640995200000,
      responseTime: 250
    },
  })
  @Expose()
  availability?: {
    isAvailable: boolean;
    lastChecked: number;
    responseTime?: number;
  };

  /**
   * Usage statistics (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Usage statistics (nếu có)',
    example: {
      totalRequests: 1500,
      totalTokens: 2500000,
      avgResponseTime: 1200
    },
  })
  @Expose()
  usageStats?: {
    totalRequests: number;
    totalTokens: number;
    avgResponseTime: number;
  };
}
