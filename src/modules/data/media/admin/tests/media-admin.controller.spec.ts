import { Test, TestingModule } from '@nestjs/testing';
import { MediaAdminController } from '../controllers/media-admin.controller';
import { MediaAdminService } from '../services/media-admin.service';
import { MediaQueryDto } from '../../dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Media } from '../../entities/media.entity';
import { JwtUserGuard } from '@modules/auth/guards';
import { JwtPayload, TokenType } from '@modules/auth/guards/jwt.util';
import { AdminMediaResponseDto } from '../../dto/media-user.dto';
import { SortDirection } from '@common/dto';
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';

describe('MediaAdminController', () => {
  let controller: MediaAdminController;
  let mediaAdminService: jest.Mocked<MediaAdminService>;

  const mockMedia: Media = {
    id: 'media-id-1',
    name: 'Test Media',
    description: 'Test Description',
    status: MediaStatusEnum.APPROVED,
    ownedBy: 1,
    createdAt: new Date().getTime(),
    updatedAt: new Date().getTime(),
    storageKey: 'test-storage-key',
    size: 1024,
    tags: ['test', 'image'],
    nameEmbedding: [],
    descriptionEmbedding: [],
  };

  const mockUser: JwtPayload = {
    id: 1,
    sub: 1,
    username: '<EMAIL>',
    isAdmin: true,
    typeToken: TokenType.ACCESS,
  };

  const mockPaginatedResponse = {
    items: [
      {
        ...mockMedia,
        author: 'Test User',
        avatar: 'https://example.com/avatar.jpg',
      },
    ],
    meta: {
      totalItems: 1,
      itemCount: 1,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    // Create mocks
    const mediaAdminServiceMock = {
      findAllForAdmin: jest.fn(),
      findByIdForAdmin: jest.fn(),
      deleteAgentMediaByAdmin: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaAdminController],
      providers: [
        {
          provide: MediaAdminService,
          useValue: mediaAdminServiceMock,
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<MediaAdminController>(MediaAdminController);
    mediaAdminService = module.get(MediaAdminService) as jest.Mocked<MediaAdminService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllMediaForAdmin', () => {
    it('should return paginated media list', async () => {
      // Arrange
      const query: MediaQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };

      const apiResponse = ApiResponseDto.paginated(
        mockPaginatedResponse as PaginatedResult<AdminMediaResponseDto>
      );

      mediaAdminService.findAllForAdmin.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.getAllMediaForAdmin(query, mockUser);

      // Assert
      expect(mediaAdminService.findAllForAdmin).toHaveBeenCalledWith(query, mockUser.isAdmin);
      expect(result).toBe(apiResponse);
    });
  });

  describe('findOne', () => {
    it('should return media by id', async () => {
      // Arrange
      const mediaId = 'media-id-1';
      const apiResponse = ApiResponseDto.success(mockMedia);

      mediaAdminService.findByIdForAdmin.mockResolvedValue(apiResponse);

      // Act
      const result = await controller.findOne(mockUser, mediaId);

      // Assert
      expect(mediaAdminService.findByIdForAdmin).toHaveBeenCalledWith(mediaId, mockUser.isAdmin);
      expect(result).toBe(apiResponse);
    });
  });

  describe('deleteAgentMediaByAdmin', () => {
    it('should delete agent media records', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'media-id-2'];
      const deleteResult = {
        deletedIds: ['media-id-1', 'media-id-2'],
        skippedIds: [],
        failedIds: [],
      };

      mediaAdminService.deleteAgentMediaByAdmin.mockResolvedValue(deleteResult);

      // Act
      const result = await controller.deleteAgentMediaByAdmin(mediaIds, mockUser);

      // Assert
      expect(mediaAdminService.deleteAgentMediaByAdmin).toHaveBeenCalledWith(
        mediaIds,
        mockUser.isAdmin
      );
      expect(result).toEqual(deleteResult);
    });

    it('should handle partial deletion', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'non-existent-id', 'error-id'];
      const deleteResult = {
        deletedIds: ['media-id-1'],
        skippedIds: ['non-existent-id'],
        failedIds: ['error-id'],
      };

      mediaAdminService.deleteAgentMediaByAdmin.mockResolvedValue(deleteResult);

      // Act
      const result = await controller.deleteAgentMediaByAdmin(mediaIds, mockUser);

      // Assert
      expect(mediaAdminService.deleteAgentMediaByAdmin).toHaveBeenCalledWith(
        mediaIds,
        mockUser.isAdmin
      );
      expect(result).toEqual(deleteResult);
    });
  });
});
