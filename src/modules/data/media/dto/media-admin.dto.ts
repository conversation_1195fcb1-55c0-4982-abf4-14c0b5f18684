import { ApiProperty } from "@nestjs/swagger";
import { Media } from "../entities";
import { IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";

export class AdminMediaResponseDto extends Media {
    @ApiProperty({
        description: 'Tên người đăng tải',
        example: '<PERSON>uyễn Văn A'
    })
    @IsString()
    @IsNotEmpty()
    author?: string;

    @ApiProperty({
        description: 'ID của người đăng tải',
        example: 1
    })
    @IsNumber()
    @IsOptional()
    authorId?: number;

    @ApiProperty({
        description: 'Avatar của người đăng tải',
        example: 'https://example.com/avatar/123e4567-e89b-12d3-a456-426614174000.jpg'
    })
    @IsString()
    @IsNotEmpty()
    avatar?: string;
}