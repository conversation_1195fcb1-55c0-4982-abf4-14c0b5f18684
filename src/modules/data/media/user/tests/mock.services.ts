// mock.services.ts
export const MockMediaRepository = () => ({
    findOne: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
});

export const MockS3Service = () => ({
    createPresignedWithID: jest.fn(),
    getDownloadUrl: jest.fn(),
    createPresignedDownloadUrl: jest.fn(),
});

export const MockAgentMediaRepository = () => ({
    delete: jest.fn(),
});

export const MockUserRepository = () => ({
    createQueryBuilder: jest.fn(),
});
