import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { UserRepository } from "@/modules/user/repositories/user.repository";
import { AdminMediaResponseDto } from '../dto/media-admin.dto';
import { plainToInstance } from "class-transformer";
import { TimeIntervalEnum } from '@/shared/utils';
import axios from 'axios';

export class MediaMapper {
  static async toAdminList(
    mediaList: AdminMediaResponseDto[],
    cdnService: CdnService,
  ): Promise<AdminMediaResponseDto[]> {
    // Sử dụng Promise.all để chạy đồng thời các yêu cầu lấy URL
    const mediaListWithUrl = await Promise.all(
      mediaList.map(async (media) => {
        const dto = media;

        // Kiểm tra và lấy presigned URL từ S3 nếu có storageKey
        if (media.storageKey) {
          try {
            // Gọi S3 để lấy download URL
            const downloadUrl = await cdnService.generateUrlView(media.storageKey,TimeIntervalEnum.ONE_HOUR); // Thay đổi thời gian hết hạn nếu cần
            if(downloadUrl!=null) {
              dto.storageKey = downloadUrl; // Gán URL vào storageKey
            }

          } catch (e) {
            console.warn(`Không thể tạo URL cho ${media.storageKey}: ${e.message}`);
            dto.storageKey = ''; // Nếu không thể lấy URL thì gán là null
          }
        }
        if(media.avatar){
          try {
            // Gọi S3 để lấy download URL
            const downloadUrl = await cdnService.generateUrlView(media.avatar,TimeIntervalEnum.ONE_HOUR); // Thay đổi thời gian hết hạn nếu cần
            if(downloadUrl!=null) {
              dto.avatar = downloadUrl; // Gán URL vào storageKey
            }

          } catch (e) {
            console.warn(`Không thể tạo URL cho ${media.avatar}: ${e.message}`);
            dto.avatar = ''; // Nếu không thể lấy URL thì gán là null
          }
        }

        return dto;
      }),
    );

    return mediaListWithUrl;
  }



static async toAdminDetail(
  media: AdminMediaResponseDto,
  s3Service: S3Service,
): Promise<AdminMediaResponseDto> {
  const dto = media;

  if (media.storageKey) {
    try {
      const presignedUrl = await s3Service.getDownloadUrl(
        media.storageKey,
        TimeIntervalEnum.ONE_HOUR,
      );

      if (presignedUrl) {
        // Gửi request GET để trigger prefetch/token hoá URL thực
        const response = await axios.get(presignedUrl, {
          maxRedirects: 0,
          validateStatus: (status) => status >= 200 && status < 400, // chấp nhận redirect
        });

        const finalUrl =
          response.request?.res?.responseUrl || presignedUrl; // fallback nếu không có redirect

        dto.storageKey = finalUrl;
      }
    } catch (e) {
      console.warn(`Không thể tạo URL cho ${media.storageKey}: ${e.message}`);
      dto.storageKey = '';
    }
  }

  // Đảm bảo giữ nguyên authorId khi xử lý
  dto.authorId = media.authorId;

  return dto;
}

}
