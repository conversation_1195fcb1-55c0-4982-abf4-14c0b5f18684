// import { Media } from '../../entities/media.entity';
// import { MediaDto } from '../../dto/media-user.dto';
// import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
// import { PaginatedResult } from '@/common/response';

// /**
//  * Tạo mock Media entity
//  */
// export const createMockMedia = (id: string = '123e4567-e89b-12d3-a456-426614174000'): Media => {
//   return {
//     id,
//     name: `Test Media ${id}`,
//     description: `Test Description for media ${id}`,
//     size: 1024,
//     tags: ['test', 'media'],
//     storageKey: `media/test/${id}.jpg`,
//     ownedBy: 1,
//     createdAt: 1625097600000,
//     updatedAt: 1625097600000,
//     nameEmbedding: [],
//     descriptionEmbedding: [],
//     status: MediaStatusEnum.DRAFT,
//   };
// };

// /**
//  * Tạo danh sách mock Media entities
//  */
// export const createMockMediaList = (count: number = 5): Media[] => {
//   return Array.from({ length: count }, (_, i) => createMockMedia(`${i + 1}23e4567-e89b-12d3-a456-426614174000`));
// };

// /**
//  * Tạo mock PaginatedResult cho Media
//  */
// export const createMockPaginatedMediaResult = (count: number = 5, totalItems: number = count): PaginatedResult<Media> => {
//   return {
//     items: createMockMediaList(count),
//     meta: {
//       totalItems,
//       itemCount: count,
//       itemsPerPage: count,
//       totalPages: Math.ceil(totalItems / count),
//       currentPage: 1
//     }
//   };
// };

// /**
//  * Tạo mock MediaDto
//  */
// export const createMockMediaDto = (type: string = 'image/jpeg'): MediaDto => {
//   return {
//     name: 'test-image.jpg',
//     description: 'Test image description',
//     size: 1024,
//     tags: ['test', 'image'],
//     type,
//     ownedBy: 1,
//   };
// };

// /**
//  * Tạo danh sách mock MediaDto
//  */
// export const createMockMediaDtoList = (count: number = 5, type: string = 'image/jpeg'): MediaDto[] => {
//   return Array.from({ length: count }, () => createMockMediaDto(type));
// };

// /**
//  * Mock JWT payload
//  */
// export const mockJwtPayload = {
//   sub: 1, // userId
//   email: '<EMAIL>',
//   roles: ['user'],
// };

// /**
//  * Mock JWT token
//  */
// export const mockJwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
