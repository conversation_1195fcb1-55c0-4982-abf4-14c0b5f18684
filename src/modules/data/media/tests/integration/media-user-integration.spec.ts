import { Test, TestingModule } from '@nestjs/testing';
import { MediaUserService } from '../../../user/services/media-user.service';
import { MediaRepository } from '../../../repositories';
import { S3Service } from '@/shared/services/s3.service';
import { AgentMediaRepository } from '@modules/agent/repositories';
import { Media } from '../../../entities/media.entity';
import { QueryDto, SortDirection } from '@common/dto';
import { MediaDto } from '../../../dto/media.dto';
import { AppException } from '@/common';
import { MEDIA_ERROR_CODES } from '@modules/data/media/exception';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { DataSource } from 'typeorm';
import { JwtUserGuard } from '@/modules/auth/guards';
import { MediaUserController } from '../../../user/controllers/media-user.controller';

describe('Media User Integration Tests', () => {
  let controller: MediaUserController;
  let service: MediaUserService;
  let repository: MediaRepository;
  let s3Service: jest.Mocked<S3Service>;
  let agentMediaRepository: jest.Mocked<AgentMediaRepository>;

  // Mock data
  const mockMedia: Media = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: 'Test Media',
    description: 'Test Description',
    size: 1024,
    tags: ['test', 'media'],
    storageKey: 'media/test/123e4567-e89b-12d3-a456-426614174000.jpg',
    ownedBy: 1,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
    nameEmbedding: [],
    descriptionEmbedding: [],
    status: 'DRAFT' as any,
  };

  const mockMediaList: Media[] = [mockMedia];

  const mockPaginatedResult: PaginatedResult<Media> = {
    items: mockMediaList,
    meta: {
      totalItems: mockMediaList.length,
      itemCount: mockMediaList.length,
      itemsPerPage: mockMediaList.length,
      totalPages: 1,
      currentPage: 1
    }
  };

  const mockUser = {
    sub: 1, // userId
    email: '<EMAIL>',
    roles: ['user'],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaUserController],
      providers: [
        MediaUserService,
        {
          provide: MediaRepository,
          useValue: {
            findOneBy: jest.fn(),
            findOne: jest.fn(),
            findAllUserMedia: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
            deleteFiles: jest.fn(),
          },
        },
        {
          provide: AgentMediaRepository,
          useValue: {
            delete: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtUserGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<MediaUserController>(MediaUserController);
    service = module.get<MediaUserService>(MediaUserService);
    repository = module.get<MediaRepository>(MediaRepository);
    s3Service = module.get(S3Service) as jest.Mocked<S3Service>;
    agentMediaRepository = module.get(AgentMediaRepository) as jest.Mocked<AgentMediaRepository>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Integration: Controller -> Service -> Repository', () => {
    it('should retrieve media by id through the entire chain', async () => {
      // Arrange
      const mediaId = '123e4567-e89b-12d3-a456-426614174000';
      jest.spyOn(repository, 'findOneBy').mockResolvedValue(mockMedia);

      // Act
      const result = await controller.findOne(mockUser, mediaId);

      // Assert
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: mediaId });
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockMedia);
    });

    it('should retrieve paginated media list through the entire chain', async () => {
      // Arrange
      const query: QueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };
      jest.spyOn(repository, 'findAllUserMedia').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.findMyMedia(mockUser, query);

      // Assert
      expect(repository.findAllUserMedia).toHaveBeenCalledWith(mockUser.sub, expect.objectContaining({
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      }));
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should delete media through the entire chain', async () => {
      // Arrange
      const keys = ['media/key1.jpg'];
      const s3DeleteResult = {
        deleted: keys,
        errors: [],
      };
      jest.spyOn(s3Service, 'deleteFiles').mockResolvedValue(s3DeleteResult);
      jest.spyOn(repository, 'findOne').mockResolvedValue(mockMedia);
      jest.spyOn(agentMediaRepository, 'delete').mockResolvedValue(undefined);
      jest.spyOn(repository, 'delete').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteManyMyMedia(mockUser, keys);

      // Assert
      expect(s3Service.deleteFiles).toHaveBeenCalledWith(keys);
      expect(repository.findOne).toHaveBeenCalledTimes(1);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(1);
      expect(repository.delete).toHaveBeenCalledTimes(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result.deleted).toEqual(keys);
    });

    it('should create presigned URLs through the entire chain', async () => {
      // Arrange
      const mediaList: MediaDto[] = [
        {
          name: 'test-image.jpg',
          description: 'Test image',
          size: 1024,
          tags: ['test', 'image'],
          type: 'image/jpeg',
          storageKey: '',
          ownedBy: mockUser.sub,
        },
      ];
      const presignedUrl = 'https://presigned-url.example.com';
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue(presignedUrl);
      jest.spyOn(repository, 'save').mockResolvedValue([]);

      // Act
      const result = await controller.createPresignedUrlsFromMediaList(mockUser, mediaList);

      // Assert
      expect(s3Service.createPresignedWithID).toHaveBeenCalledTimes(1);
      expect(repository.save).toHaveBeenCalledTimes(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual([presignedUrl]);
    });

    it('should handle exceptions properly through the entire chain', async () => {
      // Arrange
      const mediaId = 'non-existent-id';
      jest.spyOn(repository, 'findOneBy').mockResolvedValue(null);

      // Act & Assert
      await expect(controller.findOne(mockUser, mediaId)).rejects.toThrow(AppException);
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: mediaId });
    });
  });
});
