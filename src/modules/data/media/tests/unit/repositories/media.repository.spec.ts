import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { MediaRepository } from '../../../repositories/media.repository';
import { Media } from '../../../entities/media.entity';
import { QueryDto, SortDirection } from '@common/dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

describe('MediaRepository', () => {
  let repository: MediaRepository;
  let dataSource: DataSource;
  let queryBuilder: jest.Mocked<SelectQueryBuilder<Media>>;

  // Mock data
  const mockMedia: Media[] = [
    {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Test Media 1',
      description: 'Test Description 1',
      size: 1024,
      tags: ['test', 'media'],
      storageKey: 'media/test/123e4567-e89b-12d3-a456-426614174000.jpg',
      ownedBy: 1,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      nameEmbedding: [],
      descriptionEmbedding: [],
      status: 'DRAFT' as any,
    },
    {
      id: '223e4567-e89b-12d3-a456-426614174001',
      name: 'Test Media 2',
      description: 'Test Description 2',
      size: 2048,
      tags: ['test', 'image'],
      storageKey: 'media/test/223e4567-e89b-12d3-a456-426614174001.jpg',
      ownedBy: 1,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
      nameEmbedding: [],
      descriptionEmbedding: [],
      status: 'DRAFT' as any,
    },
  ];

  // Mock query builder
  const createMockQueryBuilder = () => {
    const qb = {
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([mockMedia, mockMedia.length]),
    } as unknown as jest.Mocked<SelectQueryBuilder<Media>>;
    return qb;
  };

  beforeEach(async () => {
    queryBuilder = createMockQueryBuilder();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaRepository,
        {
          provide: DataSource,
          useValue: {
            createEntityManager: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue(queryBuilder),
          },
        },
      ],
    }).compile();

    repository = module.get<MediaRepository>(MediaRepository);
    dataSource = module.get<DataSource>(DataSource);

    // Mock createQueryBuilder method
    jest.spyOn(repository, 'createQueryBuilder').mockReturnValue(queryBuilder);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('findAllUserMedia', () => {
    it('should return paginated media for a user', async () => {
      // Arrange
      const userId = 1;
      const query: QueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };

      // Act
      const result = await repository.findAllUserMedia(userId, query);

      // Assert
      expect(repository.createQueryBuilder).toHaveBeenCalledWith('media');
      expect(queryBuilder.where).toHaveBeenCalledWith('media.ownedBy = :userId', { userId });
      expect(queryBuilder.orderBy).toHaveBeenCalledWith('media.createdAt', 'DESC');
      expect(queryBuilder.skip).toHaveBeenCalledWith(0); // (page - 1) * limit = 0
      expect(queryBuilder.take).toHaveBeenCalledWith(10);
      expect(queryBuilder.getManyAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        items: mockMedia,
        total: mockMedia.length,
      } as PaginatedResult<Media>);
    });

    it('should apply search filter when search query is provided', async () => {
      // Arrange
      const userId = 1;
      const query: QueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        search: 'test',
      };

      // Act
      const result = await repository.findAllUserMedia(userId, query);

      // Assert
      expect(queryBuilder.where).toHaveBeenCalledWith('media.ownedBy = :userId', { userId });
      expect(queryBuilder.andWhere).toHaveBeenCalledWith(
        '(media.name ILIKE :search OR media.description ILIKE :search)',
        { search: '%test%' }
      );
      expect(result).toEqual({
        items: mockMedia,
        total: mockMedia.length,
      } as PaginatedResult<Media>);
    });

    it('should use default pagination values when not provided', async () => {
      // Arrange
      const userId = 1;
      const query: QueryDto = {}; // Empty query

      // Act
      const result = await repository.findAllUserMedia(userId, query);

      // Assert
      expect(queryBuilder.skip).toHaveBeenCalledWith(0); // Default (page - 1) * limit = 0
      expect(queryBuilder.take).toHaveBeenCalledWith(10); // Default limit = 10
      expect(queryBuilder.orderBy).toHaveBeenCalledWith('media.createdAt', 'DESC'); // Default sort
      expect(result).toEqual({
        items: mockMedia,
        total: mockMedia.length,
      } as PaginatedResult<Media>);
    });
  });
});
