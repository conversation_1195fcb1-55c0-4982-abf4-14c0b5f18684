import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { MediaTypeUtil } from '@/shared/utils';

/**
 * Custom validator để kiểm tra MIME type có được phép trong Media module không
 * Chỉ cho phép image, video và audio - từ chối document files
 */
export function IsAllowedMediaType(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isAllowedMediaType',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (typeof value !== 'string') {
            return false;
          }
          return MediaTypeUtil.isAllowedInMediaModule(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} phải là MIME type hợp lệ cho media (image/, video/, audio/)`;
        },
      },
    });
  };
}

/**
 * Custom validator để kiểm tra file extension c<PERSON> được phép không
 */
export function IsAllowedMediaExtension(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isAllowedMediaExtension',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value || typeof value !== 'string') {
            return true; // Optional field
          }
          
          if (!value.includes('.')) {
            return false;
          }

          const extension = value.split('.').pop()?.toLowerCase();
          if (!extension) {
            return false;
          }

          const allowedExtensions = [
            // Image extensions
            'jpg', 'jpeg', 'png', 'gif', 'webp',
            // Video extensions  
            'mp4', 'webm', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'mpg', 'mpeg', '3gp',
            // Audio extensions
            'mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'
          ];

          return allowedExtensions.includes(extension);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} phải có phần mở rộng hợp lệ cho media files`;
        },
      },
    });
  };
}
