import { ApiProperty } from '@nestjs/swagger';
import { KnowledgeFile } from '../entities/knowledge-file.entity';

export class KnowledgeFileSchema {
  @ApiProperty({
    description: 'ID của file tri thức',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của file tri thức',
    example: 'Tài liệu hướng dẫn sử dụng NestJS',
  })
  name: string;

  @ApiProperty({
    description: 'Khóa định danh file trên hệ thống lưu trữ',
    example: 'knowledge/123e4567-e89b-12d3-a456-426614174000.pdf',
  })
  storageKey: string;

  @ApiProperty({
    description: 'Loại người sở hữu file',
    example: 'user',
    enum: ['user', 'employee'],
  })
  ownerType: string;

  @ApiProperty({
    description: 'ID của người sở hữu file',
    example: 1,
  })
  ownedBy: number;

  @ApiProperty({
    description: 'Trạng thái sở hữu file',
    example: true,
  })
  isOwner: boolean;

  @ApiProperty({
    description: 'Trạng thái đăng bán file',
    example: false,
  })
  isForSale: boolean;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi file (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Dung lượng của file tri thức (byte)',
    example: 1024000,
  })
  storage: number;

  constructor(partial: Partial<KnowledgeFile>) {
    Object.assign(this, partial);
  }
}

export class KnowledgeFileListResponseSchema {
  @ApiProperty({
    description: 'Danh sách file tri thức',
    type: [KnowledgeFileSchema],
  })
  items: KnowledgeFileSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số file tri thức',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số file tri thức trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số file tri thức trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
