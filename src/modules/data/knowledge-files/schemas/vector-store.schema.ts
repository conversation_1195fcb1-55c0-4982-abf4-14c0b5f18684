import { ApiProperty } from '@nestjs/swagger';
import { VectorStore } from '../entities/vector-store.entity';

export class VectorStoreSchema {
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên vector store',
    example: 'Kho vector tài liệu NestJS',
  })
  name: string;

  @ApiProperty({
    description: 'Dung lượng đã dùng (bytes/tokens)',
    example: 1024000,
  })
  storage: number;

  @ApiProperty({
    description: 'Loại chủ sở hữu',
    example: 'user',
    enum: ['user', 'employee'],
  })
  ownerType: string;

  @ApiProperty({
    description: 'ID của chủ sở hữu',
    example: 1,
  })
  ownerId: number;

  @ApiProperty({
    description: 'Thời điểm tạo (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (unix timestamp)',
    example: 1625097600000,
  })
  updateAt: number;

  constructor(partial: Partial<VectorStore>) {
    Object.assign(this, partial);
  }
}

export class VectorStoreListResponseSchema {
  @ApiProperty({
    description: 'Danh sách vector store',
    type: [VectorStoreSchema],
  })
  items: VectorStoreSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số vector store',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số vector store trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số vector store trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
