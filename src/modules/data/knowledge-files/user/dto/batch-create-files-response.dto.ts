import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin file đã tạo và URL tải lên
 */
export class FileCreationInfoDto {
  @ApiProperty({
    description: 'ID của file tri thức đã tạo',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'URL ký sẵn để tải file lên',
    example: 'https://storage.example.com/presigned-url?token=abc123',
  })
  @Expose()
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa lưu trữ của file trên hệ thống',
    example: '123/knowledge_files/document-123456-abcdef.pdf',
  })
  @Expose()
  storageKey: string;
}

/**
 * DTO cho kết quả tạo nhiều file tri thức
 */
export class BatchCreateFilesResponseDto {
  @ApiProperty({
    description: 'Danh sách thông tin các file đã tạo',
    type: [FileCreationInfoDto],
  })
  @Expose()
  @Type(() => FileCreationInfoDto)
  files: FileCreationInfoDto[];
}
