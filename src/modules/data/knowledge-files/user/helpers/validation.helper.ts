import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions';
import { KnowledgeFile, VectorStore } from '../../entities';
import { KnowledgeFileStatus } from '../../enums/knowledge-file-status.enum';

/**
 * Helper class để xác thực dữ liệu liên quan đến file tri thức của người dùng
 */
@Injectable()
export class KnowledgeFileUserValidationHelper {
  /**
   * Map lưu trữ thông báo lỗi cho từng trạng thái file khi thực hiện gửi duyệt
   */
  private readonly errorMessageForSubmit = {
    [KnowledgeFileStatus.PENDING]: 'File đã được gửi duyệt và đang chờ xét duyệt',
    [KnowledgeFileStatus.APPROVED]: 'File đã được duyệt và không thể gửi duyệt lại',
    [KnowledgeFileStatus.REJECTED]: 'File đã bị từ chối, vui lòng tạo file mới',
    [KnowledgeFileStatus.DELETED]: 'File đã bị xóa và không thể gửi duyệt',
  };

  /**
   * Xác thực file tồn tại
   * @param file File cần xác thực
   * @param fileId ID của file (dùng cho thông báo lỗi)
   * @throws AppException nếu file không tồn tại
   */
  validateFileExists(
    file: KnowledgeFile | null,
    fileId: string,
  ): asserts file is KnowledgeFile {
    if (!file) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
        `File với ID ${fileId} không tồn tại hoặc bạn không có quyền truy cập`,
      );
    }
  }

  /**
   * Xác thực file không ở trạng thái đã xóa
   * @param file File cần xác thực
   * @throws AppException nếu file đã bị xóa
   */
  validateFileNotDeleted(file: KnowledgeFile): void {
    if (file.status === KnowledgeFileStatus.DELETED) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
        'File đã bị xóa và không thể truy cập',
      );
    }
  }

  /**
   * Xác thực file đang ở trạng thái DRAFT để có thể gửi duyệt
   * @param file File cần xác thực
   * @throws AppException nếu file không ở trạng thái DRAFT
   */
  validateFileIsDraft(file: KnowledgeFile): void {
    if (file.status !== KnowledgeFileStatus.DRAFT) {
      const errorMessage = this.errorMessageForSubmit[file.status] ||
        'File không ở trạng thái nháp và không thể gửi duyệt';

      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_INVALID_STATUS,
        errorMessage,
      );
    }
  }

  /**
   * Xác thực vector store tồn tại
   * @param vectorStore Vector store cần xác thực
   * @param vectorStoreId ID của vector store (dùng cho thông báo lỗi)
   * @throws AppException nếu vector store không tồn tại
   */
  validateVectorStoreExists(
    vectorStore: VectorStore | null,
    vectorStoreId: string,
  ): asserts vectorStore is VectorStore {
    if (!vectorStore) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        `Vector store với ID ${vectorStoreId} không tồn tại hoặc bạn không có quyền truy cập`,
      );
    }
  }

  /**
   * Xác thực danh sách file tồn tại
   * @param files Danh sách file cần xác thực
   * @param fileIds Danh sách ID của các file (dùng cho thông báo lỗi)
   * @throws AppException nếu không có file nào tồn tại hoặc có file không tồn tại
   */
  validateFilesExist(files: KnowledgeFile[], fileIds: string[]): void {
    if (files.length === 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
        'Không có file nào tồn tại hoặc bạn không có quyền truy cập',
      );
    }

    // Xác định các file không tồn tại
    const foundFileIds = files.map((file) => file.id);
    const missingFileIds = fileIds.filter((id) => !foundFileIds.includes(id));

    if (missingFileIds.length > 0) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
        `Có ${missingFileIds.length} file không tồn tại hoặc bạn không có quyền truy cập: ${missingFileIds.join(', ')}`,
      );
    }
  }

  /**
   * Xác thực file không đang được bán
   * @param file File cần xác thực
   * @throws AppException nếu file đang được bán
   */
  validateFileNotForSale(file: KnowledgeFile): void {
    if (file.isForSale) {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
        'File đang được bán và không thể thực hiện thao tác này',
      );
    }
  }

  /**
   * Xác thực S3 key hợp lệ
   * @param s3Key S3 key cần xác thực
   * @throws AppException nếu S3 key không hợp lệ
   */
  validateS3Key(s3Key: string): void {
    if (!s3Key || typeof s3Key !== 'string' || s3Key.trim() === '') {
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_UPLOAD_ERROR,
        'S3 key không hợp lệ',
      );
    }
  }
}
