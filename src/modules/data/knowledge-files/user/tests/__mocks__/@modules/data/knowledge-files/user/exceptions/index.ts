import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '../../../../../@shared/exceptions/app.exception';

/**
 * Mã lỗi cho các thao tác với file tri thức
 */
export const KNOWLEDGE_FILE_ERROR_CODES = {
  KNOWLEDGE_FILE_CREATE_ERROR: new ErrorCode(
    20101,
    'Lỗi khi tạo file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_LIST_ERROR: new ErrorCode(
    20102,
    'Lỗi khi lấy danh sách file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_DELETE_ERROR: new ErrorCode(
    20103,
    'Lỗi khi xóa file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_NOT_FOUND: new ErrorCode(
    20104,
    'Không tìm thấy file tri thức',
    HttpStatus.NOT_FOUND,
  ),

  KNOWLEDGE_FILE_UPLOAD_ERROR: new ErrorCode(
    20105,
    'Lỗi khi tải lên file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_DOWNLOAD_ERROR: new ErrorCode(
    20106,
    'Lỗi khi tải xuống file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_PERMISSION_ERROR: new ErrorCode(
    20107,
    'Không có quyền truy cập file tri thức',
    HttpStatus.FORBIDDEN,
  ),
};
