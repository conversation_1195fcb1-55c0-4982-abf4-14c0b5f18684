/**
 * Tạo khóa S3 cho file
 * @param options C<PERSON><PERSON> tùy chọn để tạo khóa S3
 * @returns Khóa S3 được tạo
 */
export function generateS3Key(options: {
  baseFolder: string;
  fileName: string;
  useTimeFolder?: boolean;
  categoryFolder?: string;
}): string {
  const { baseFolder, fileName, useTimeFolder = false, categoryFolder } = options;
  
  let key = baseFolder;
  
  if (useTimeFolder) {
    key += '/time_folder';
  }
  
  if (categoryFolder) {
    key += `/${categoryFolder}`;
  }
  
  key += `/${fileName}`;
  
  return key;
}
