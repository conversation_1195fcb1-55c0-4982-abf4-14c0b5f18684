import { Injectable } from '@nestjs/common';

/**
 * Interface cho VectorStoreFile response
 */
interface VectorStoreFile {
  id: string;
  object: string;
  created_at: number;
  vector_store_id: string;
  file_id: string;
}

@Injectable()
export class OpenAiService {
  /**
   * Mock cho hàm tạo vector store
   */
  createVectorStore = jest.fn().mockResolvedValue({
    id: 'mock-vector-store-id',
    object: 'vector_store',
    created_at: Date.now(),
    name: 'Mock Vector Store'
  });

  /**
   * Mock cho hàm tạo vector store và gán file
   * @param config Cấu hình cho vector store
   * @param fileId ID của file trên OpenAI
   * @returns Thông tin về vector store đã tạo và file đã gán
   */
  createVectorStoreWithFile = jest.fn().mockImplementation(
    (config: any, fileId: string): Promise<{
      vectorStoreId: string;
    }> => {
      return Promise.resolve({
        vectorStoreId: 'mock-vector-store-id'
      });
    }
  );

  /**
   * <PERSON>ck cho hàm tạo vector store và gán nhiều file
   * @param config Cấu hình cho vector store
   * @param fileIds Danh sách các ID của file trên OpenAI
   * @returns Thông tin về vector store đã tạo và số lượng file đã gán thành công
   */
  createVectorStoreWithMultipleFiles = jest.fn().mockImplementation(
    (config: any, fileIds: string[]): Promise<{
      vectorStoreId: string;
      successCount: number;
      errorCount: number;
      errors?: { fileId: string; message: string }[];
    }> => {
      // Giả lập thành công cho hầu hết các file, trừ một số file có ID bắt đầu bằng 'error'
      const errors: { fileId: string; message: string }[] = [];
      let successCount = 0;

      fileIds.forEach(fileId => {
        if (fileId.startsWith('error')) {
          errors.push({
            fileId,
            message: 'Mock error for testing'
          });
        } else {
          successCount++;
        }
      });

      return Promise.resolve({
        vectorStoreId: 'mock-vector-store-id',
        successCount,
        errorCount: errors.length,
        errors: errors.length > 0 ? errors : undefined
      });
    }
  );

  /**
   * Mock cho hàm gán file vào vector store bằng file_id
   * @param vectorStoreId ID của vector store
   * @param fileId ID của file trên OpenAI
   * @returns Thông tin về file đã gán vào vector store
   */
  attachFileToVectorStore = jest.fn().mockImplementation(
    (vectorStoreId: string, fileId: string): Promise<VectorStoreFile> => {
      return Promise.resolve({
        id: `mock-vector-file-${Date.now()}`,
        object: 'vector_store_file',
        created_at: Date.now(),
        vector_store_id: vectorStoreId,
        file_id: fileId
      });
    }
  );

  /**
   * Mock cho hàm upload file lên vector store
   * @param vectorStoreId ID của vector store
   * @param fileKey S3 key của file cần upload
   * @returns Thông tin về file đã upload
   */
  uploadFileToVectorStore = jest.fn().mockImplementation(
    (vectorStoreId: string, fileKey: string): Promise<VectorStoreFile> => {
      return Promise.resolve({
        id: `mock-vector-file-${Date.now()}`,
        object: 'vector_store_file',
        created_at: Date.now(),
        vector_store_id: vectorStoreId,
        file_id: `mock-file-${Date.now()}`
      });
    }
  );

  /**
   * Mock cho hàm xóa file khỏi vector store
   */
  deleteVectorStoreFile = jest.fn().mockResolvedValue({
    deleted: true,
    vectorStoreId: 'mock-vector-store-id',
    fileId: 'mock-file-id'
  });

  /**
   * Mock cho hàm xóa vector store
   */
  deleteVectorStore = jest.fn().mockResolvedValue({
    deleted: true,
    id: 'mock-vector-store-id'
  });

  /**
   * Mock cho hàm xóa file trên OpenAI
   */
  deleteOpenAIFile = jest.fn().mockResolvedValue({
    deleted: true,
    id: 'mock-file-id'
  });
}