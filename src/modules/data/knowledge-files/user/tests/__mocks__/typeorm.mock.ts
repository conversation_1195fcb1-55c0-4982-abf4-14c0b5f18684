import { Repository, DataSource, SelectQueryBuilder } from 'typeorm';

export class MockRepository<T> {
  create = jest.fn().mockImplementation(entity => entity);
  save = jest.fn().mockImplementation(entity => Promise.resolve(entity));
  findOne = jest.fn().mockResolvedValue(null);
  find = jest.fn().mockResolvedValue([]);
  delete = jest.fn().mockResolvedValue({ affected: 1 });
  remove = jest.fn().mockResolvedValue(undefined);
  count = jest.fn().mockResolvedValue(0);
  createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder());
}

export class MockDataSource {
  createQueryBuilder = jest.fn().mockReturnValue(mockQueryBuilder());
  getRepository = jest.fn().mockReturnValue(new MockRepository());
  transaction = jest.fn().mockImplementation(async callback => {
    return callback(mockEntityManager());
  });
}

export class MockKnowledgeFileRepository extends MockRepository<any> {
  // Add any custom methods used in the service
}

export class MockVectorStoreRepository extends MockRepository<any> {
  // Add any custom methods used in the service
}

export function mockQueryBuilder() {
  const qb: any = {
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    getRawMany: jest.fn().mockResolvedValue([]),
    getRawOne: jest.fn().mockResolvedValue({}),
    getMany: jest.fn().mockResolvedValue([]),
    getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
    getOne: jest.fn().mockResolvedValue(null),
    from: jest.fn().mockReturnThis(),
  };
  return qb;
}

export function mockEntityManager() {
  return {
    save: jest.fn().mockImplementation(entity => Promise.resolve(entity)),
    // Add other methods as needed
  };
}