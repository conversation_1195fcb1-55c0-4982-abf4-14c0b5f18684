import { plainToInstance } from 'class-transformer';
import { BatchCreateFilesResponseDto, FileCreationInfoDto } from '../../dto';

describe('BatchCreateFilesResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      files: [
        {
          id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
          name: '<PERSON><PERSON><PERSON> liệu hướng dẫn.pdf',
          uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
          storageKey: '123/knowledge_files/document-123456-abcdef.pdf',
        },
        {
          id: 'b2c3d4e5-f6a7-8901-bcde-f01234567890',
          name: '<PERSON><PERSON><PERSON> li<PERSON>u kỹ thuật.docx',
          uploadUrl: 'https://storage.example.com/presigned-url?token=def456',
          storageKey: '123/knowledge_files/document-654321-fedcba.docx',
        },
      ],
    };

    // Act
    const dto = plainToInstance(BatchCreateFilesResponseDto, plainObject, { excludeExtraneousValues: true });
    
    // Chuyển đổi thủ công các đối tượng con
    if (dto.files && dto.files.length > 0) {
      dto.files = dto.files.map(file => 
        plainToInstance(FileCreationInfoDto, file, { excludeExtraneousValues: true })
      );
    }

    // Assert
    expect(dto).toBeDefined();
    expect(dto.files).toHaveLength(2);
    expect(dto.files[0]).toBeInstanceOf(FileCreationInfoDto);
    expect(dto.files[0].id).toBe('a1b2c3d4-e5f6-7890-abcd-ef1234567890');
    expect(dto.files[0].name).toBe('Tài liệu hướng dẫn.pdf');
    expect(dto.files[0].uploadUrl).toBe('https://storage.example.com/presigned-url?token=abc123');
    expect(dto.files[0].storageKey).toBe('123/knowledge_files/document-123456-abcdef.pdf');
    expect(dto.files[1].id).toBe('b2c3d4e5-f6a7-8901-bcde-f01234567890');
    expect(dto.files[1].name).toBe('Tài liệu kỹ thuật.docx');
    expect(dto.files[1].uploadUrl).toBe('https://storage.example.com/presigned-url?token=def456');
    expect(dto.files[1].storageKey).toBe('123/knowledge_files/document-654321-fedcba.docx');
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với mảng files rỗng', () => {
    // Arrange
    const plainObject = {
      files: [],
    };

    // Act
    const dto = plainToInstance(BatchCreateFilesResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.files).toHaveLength(0);
  });

  it('nên loại bỏ các trường không được khai báo trong DTO', () => {
    // Arrange
    const plainObject = {
      files: [
        {
          id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
          name: 'Tài liệu hướng dẫn.pdf',
          uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
          storageKey: '123/knowledge_files/document-123456-abcdef.pdf',
          extraField: 'Trường không được khai báo',
        },
      ],
      extraProperty: 'Thuộc tính không được khai báo',
    };

    // Act
    const dto = plainToInstance(BatchCreateFilesResponseDto, plainObject, { excludeExtraneousValues: true });
    
    // Chuyển đổi thủ công các đối tượng con
    if (dto.files && dto.files.length > 0) {
      dto.files = dto.files.map(file => 
        plainToInstance(FileCreationInfoDto, file, { excludeExtraneousValues: true })
      );
    }
    
    const dtoJson = JSON.stringify(dto);

    // Assert
    expect(dto).toBeDefined();
    expect(dtoJson).not.toContain('extraField');
    expect(dtoJson).not.toContain('extraProperty');
  });
});

describe('FileCreationInfoDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      name: 'Tài liệu hướng dẫn.pdf',
      uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
      storageKey: '123/knowledge_files/document-123456-abcdef.pdf',
    };

    // Act
    const dto = plainToInstance(FileCreationInfoDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.id).toBe('a1b2c3d4-e5f6-7890-abcd-ef1234567890');
    expect(dto.name).toBe('Tài liệu hướng dẫn.pdf');
    expect(dto.uploadUrl).toBe('https://storage.example.com/presigned-url?token=abc123');
    expect(dto.storageKey).toBe('123/knowledge_files/document-123456-abcdef.pdf');
  });

  it('nên loại bỏ các trường không được khai báo trong DTO', () => {
    // Arrange
    const plainObject = {
      id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      name: 'Tài liệu hướng dẫn.pdf',
      uploadUrl: 'https://storage.example.com/presigned-url?token=abc123',
      storageKey: '123/knowledge_files/document-123456-abcdef.pdf',
      extraField1: 'Trường không được khai báo',
      extraField2: 123,
    };

    // Act
    const dto = plainToInstance(FileCreationInfoDto, plainObject, { excludeExtraneousValues: true });
    const dtoJson = JSON.stringify(dto);

    // Assert
    expect(dto).toBeDefined();
    expect(dtoJson).not.toContain('extraField1');
    expect(dtoJson).not.toContain('extraField2');
  });
});
