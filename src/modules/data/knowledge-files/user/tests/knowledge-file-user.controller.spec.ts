import { Test, TestingModule } from '@nestjs/testing';
import { KnowledgeFileUserController } from '../controllers';
import { KnowledgeFileUserService } from '../services';
import { BatchCreateFilesDto, BatchCreateFilesResponseDto, FileResponseDto, QueryFileDto } from '../dto';
import { FileTypeEnum } from '@shared/utils/file/file-media-type.util';
import { HttpStatus } from '@nestjs/common';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { JwtUserGuard } from './__mocks__/@modules/auth/guards/jwt-user.guard';
import { JwtUtilService } from './__mocks__/@modules/auth/services/jwt.util';
import { RedisService } from './__mocks__/@shared/services/redis.service';
import { SortDirection } from '@common/dto/query.dto';

describe('KnowledgeFileUserController', () => {
  let controller: KnowledgeFileUserController;
  let service: KnowledgeFileUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [KnowledgeFileUserController],
      providers: [
        {
          provide: KnowledgeFileUserService,
          useValue: {
            batchCreateFiles: jest.fn(),
            getFiles: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
        {
          provide: JwtUserGuard,
          useValue: {
            canActivate: jest.fn().mockReturnValue(true),
          },
        },
        {
          provide: JwtUtilService,
          useValue: {
            verifyToken: jest.fn().mockResolvedValue({ id: 1, email: '<EMAIL>' }),
            generateToken: jest.fn().mockResolvedValue('mock-token'),
          },
        },
        {
          provide: RedisService,
          useValue: {
            get: jest.fn().mockResolvedValue(null),
            set: jest.fn().mockResolvedValue(undefined),
            del: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    })
    .overrideGuard(JwtUserGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<KnowledgeFileUserController>(KnowledgeFileUserController);
    service = module.get<KnowledgeFileUserService>(KnowledgeFileUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('batchCreateFiles', () => {
    it('should handle empty files array', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: [],
      };
      const userId = 1;

      jest.spyOn(service, 'batchCreateFiles').mockResolvedValue({ files: [] });

      // Act
      const result = await controller.batchCreateFiles(dto, userId);

      // Assert
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Đã tạo files tri thức thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual({ files: [] });
    });

    it('should handle large file batch', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: Array(10).fill(0).map((_, i) => ({
          name: `test${i}.pdf`,
          mime: FileTypeEnum.PDF,
          storage: 1024 * (i + 1),
        })),
      };
      const userId = 1;

      const mockResponse: BatchCreateFilesResponseDto = {
        files: Array(10).fill(0).map((_, i) => ({
          id: `file${i}`,
          name: `test${i}.pdf`,
          uploadUrl: `https://example.com/upload/test${i}.pdf`,
          storageKey: `knowledge/DOCUMENT/2023/05/test${i}-pdf-123456-uuid.pdf`,
        })),
      };

      jest.spyOn(service, 'batchCreateFiles').mockResolvedValue(mockResponse);

      // Act
      const result = await controller.batchCreateFiles(dto, userId);

      // Assert
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Đã tạo files tri thức thành công.');
      expect(result.result).toEqual(mockResponse);
      expect(result.result?.files.length).toBe(10);
    });
    it('should create batch files successfully', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: [
          { name: 'test.pdf', mime: FileTypeEnum.PDF, storage: 1024 },
          { name: 'test.docx', mime: FileTypeEnum.DOCX, storage: 2048 },
        ],
      };
      const userId = 1;

      const mockResponse: BatchCreateFilesResponseDto = {
        files: [
          {
            id: 'file1',
            name: 'test.pdf',
            uploadUrl: 'https://example.com/upload/test.pdf',
            storageKey: 'knowledge/DOCUMENT/2023/05/test-pdf-123456-uuid.pdf',
          },
          {
            id: 'file2',
            name: 'test.docx',
            uploadUrl: 'https://example.com/upload/test.docx',
            storageKey: 'knowledge/DOCUMENT/2023/05/test-docx-123456-uuid.docx',
          },
        ],
      };

      jest.spyOn(service, 'batchCreateFiles').mockResolvedValue(mockResponse);

      // Act
      const result = await controller.batchCreateFiles(dto, userId);

      // Assert
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Đã tạo files tri thức thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockResponse);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: [
          { name: 'test.pdf', mime: FileTypeEnum.PDF, storage: 1024 },
        ],
      };
      const userId = 1;
      const errorMessage = 'Lỗi khi tạo file';

      jest.spyOn(service, 'batchCreateFiles').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.batchCreateFiles(dto, userId)).rejects.toThrow(Error);
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
    });

    it('should handle BadRequestException from service', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: [
          { name: 'test.pdf', mime: 'invalid/mime' as any, storage: 1024 },
        ],
      };
      const userId = 1;
      const errorMessage = 'Loại file không được hỗ trợ';

      jest.spyOn(service, 'batchCreateFiles').mockRejectedValue(new BadRequestException(errorMessage));

      // Act & Assert
      await expect(controller.batchCreateFiles(dto, userId)).rejects.toThrow(BadRequestException);
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const dto: BatchCreateFilesDto = {
        files: [
          { name: 'test.pdf', mime: FileTypeEnum.PDF, storage: 1024 },
        ],
      };
      const userId = 1;

      jest.spyOn(service, 'batchCreateFiles').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR, 'Lỗi ứng dụng'));

      // Act & Assert
      await expect(controller.batchCreateFiles(dto, userId)).rejects.toThrow(AppException);
      expect(service.batchCreateFiles).toHaveBeenCalledWith(dto, userId);
    });
  });

  describe('getFiles', () => {
    it('should handle sorting parameters', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        sortBy: 'name',
        sortDirection: SortDirection.ASC,
      } as QueryFileDto;
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
        items: [
          {
            id: 'file1',
            name: 'a_test.pdf',
            extension: 'pdf',
            storage: 1024,
            viewUrl: 'https://cdn.example.com/knowledge/a_test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
          {
            id: 'file2',
            name: 'b_test.pdf',
            extension: 'pdf',
            storage: 2048,
            viewUrl: 'https://cdn.example.com/knowledge/b_test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockPaginatedResult);
    });
    it('should return paginated files', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as QueryFileDto;
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
        items: [
          {
            id: 'file1',
            name: 'test.pdf',
            extension: 'pdf',
            storage: 1024,
            viewUrl: 'https://example.com/test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
          {
            id: 'file2',
            name: 'test.docx',
            extension: 'docx',
            storage: 2048,
            viewUrl: 'https://example.com/test.docx',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle empty result', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as QueryFileDto;
      const userId = 1;

      const mockEmptyResult: PaginatedResult<FileResponseDto> = {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockEmptyResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockEmptyResult);
    });

    it('should handle search parameter', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        search: 'test',
      } as QueryFileDto;
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
        items: [
          {
            id: 'file1',
            name: 'test.pdf',
            extension: 'pdf',
            storage: 1024,
            viewUrl: 'https://cdn.example.com/knowledge/test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle extensions parameter', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        extensions: 'pdf',
      } as QueryFileDto;
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
        items: [
          {
            id: 'file1',
            name: 'test.pdf',
            extension: 'pdf',
            storage: 1024,
            viewUrl: 'https://cdn.example.com/knowledge/test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle vectorStoreId parameter', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
        vectorStoreId: 'vs_123',
      } as QueryFileDto;
      const userId = 1;

      const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
        items: [
          {
            id: 'file1',
            name: 'test.pdf',
            extension: 'pdf',
            storage: 1024,
            vectorStoreId: 'vs_123',
            vectorStoreName: 'Test Vector Store',
            viewUrl: 'https://cdn.example.com/knowledge/test.pdf',
            createdAt: Date.now(),
            updatedAt: Date.now(),
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto, userId);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách file thành công.');
      expect(result.result).toBeDefined();
      expect(result.result).toEqual(mockPaginatedResult);
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as QueryFileDto;
      const userId = 1;
      const errorMessage = 'Lỗi khi lấy danh sách file';

      jest.spyOn(service, 'getFiles').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.getFiles(queryDto, userId)).rejects.toThrow(Error);
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as QueryFileDto;
      const userId = 1;

      jest.spyOn(service, 'getFiles').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR, 'Lỗi khi lấy danh sách file'));

      // Act & Assert
      await expect(controller.getFiles(queryDto, userId)).rejects.toThrow(AppException);
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
    });

    it('should handle invalid query parameters', async () => {
      // Arrange
      const queryDto = {
        page: -1, // Invalid page number
        limit: 10,
      } as QueryFileDto;
      const userId = 1;

      jest.spyOn(service, 'getFiles').mockRejectedValue(new BadRequestException('Số trang không hợp lệ'));

      // Act & Assert
      await expect(controller.getFiles(queryDto, userId)).rejects.toThrow(BadRequestException);
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
    });

    it('should handle unauthorized access', async () => {
      // Arrange
      const queryDto = {
        page: 1,
        limit: 10,
      } as QueryFileDto;
      const userId = 999; // Unauthorized user

      jest.spyOn(service, 'getFiles').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR, 'Không có quyền truy cập'));

      // Act & Assert
      await expect(controller.getFiles(queryDto, userId)).rejects.toThrow(AppException);
      expect(service.getFiles).toHaveBeenCalledWith(queryDto, userId);
    });
  });

  describe('deleteFile', () => {
    it('should handle file with vector store links', async () => {
      // Arrange
      const fileId = 'file_with_vector_store';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockResolvedValue({ success: true });

      // Act
      const result = await controller.deleteFile(fileId, userId);

      // Assert
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa file thành công.');
      expect(result.result).toBeNull();
    });

    it('should handle file with OpenAI links', async () => {
      // Arrange
      const fileId = 'file_with_openai';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockResolvedValue({ success: true });

      // Act
      const result = await controller.deleteFile(fileId, userId);

      // Assert
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa file thành công.');
      expect(result.result).toBeNull();
    });
    it('should delete file successfully', async () => {
      // Arrange
      const fileId = 'file1';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockResolvedValue({ success: true });

      // Act
      const result = await controller.deleteFile(fileId, userId);

      // Assert
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa file thành công.');
      expect(result.result).toBeNull();
    });

    it('should handle service exceptions and rethrow them', async () => {
      // Arrange
      const fileId = 'file1';
      const userId = 1;
      const errorMessage = 'Lỗi khi xóa file';

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(Error);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });

    it('should handle NotFoundException from service', async () => {
      // Arrange
      const fileId = 'non_existent_file';
      const userId = 1;
      const errorMessage = 'Không tìm thấy file';

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new NotFoundException(errorMessage));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(NotFoundException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });

    it('should handle AppException from service', async () => {
      // Arrange
      const fileId = 'file1';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR, 'Lỗi khi xóa file'));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(AppException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });

    it('should handle file in use by vector store', async () => {
      // Arrange
      const fileId = 'file_in_use';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR, 'File đang được sử dụng bởi vector store'));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(AppException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });

    it('should handle file for sale', async () => {
      // Arrange
      const fileId = 'file_for_sale';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR, 'File đang được bán và không thể xóa'));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(AppException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });

    it('should handle file already deleted', async () => {
      // Arrange
      const fileId = 'file_already_deleted';
      const userId = 1;

      jest.spyOn(service, 'deleteFile').mockRejectedValue(new AppException(KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND, 'File đã bị xóa trước đó'));

      // Act & Assert
      await expect(controller.deleteFile(fileId, userId)).rejects.toThrow(AppException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId, userId);
    });
  });
});
