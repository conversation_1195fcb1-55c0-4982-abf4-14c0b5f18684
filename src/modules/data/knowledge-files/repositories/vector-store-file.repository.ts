import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, In } from 'typeorm';
import { VectorStoreFile } from '../entities/vector-store-file.entity';

/**
 * Interface đại diện cho thông tin vector store của file
 */
export interface FileVectorStoreInfo {
  vectorStoreId: string;
  vectorStoreName: string;
}

@Injectable()
export class VectorStoreFileRepository extends Repository<VectorStoreFile> {
  private readonly logger = new Logger(VectorStoreFileRepository.name);

  constructor(private dataSource: DataSource) {
    super(VectorStoreFile, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho vector store file
   * @returns SelectQueryBuilder<VectorStoreFile>
   */
  private createBaseQuery(): SelectQueryBuilder<VectorStoreFile> {
    return this.createQueryBuilder('vsf');
  }

  /**
   * Tìm các file trong vector store
   * @param vectorStoreId ID của vector store
   * @returns Danh sách các file trong vector store
   */
  async findByVectorStoreId(vectorStoreId: string): Promise<VectorStoreFile[]> {
    try {
      return this.createBaseQuery()
        .where('vsf.vector_store_id = :vectorStoreId', { vectorStoreId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm các file trong vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm các file trong vector store theo danh sách file ID
   * @param vectorStoreId ID của vector store
   * @param fileIds Danh sách ID của các file
   * @returns Danh sách các file trong vector store
   */
  async findByVectorStoreIdAndFileIds(vectorStoreId: string, fileIds: string[]): Promise<VectorStoreFile[]> {
    try {
      return this.createBaseQuery()
        .where('vsf.vector_store_id = :vectorStoreId', { vectorStoreId })
        .andWhere('vsf.file_id IN (:...fileIds)', { fileIds })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm các file trong vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thông tin vector store của một file cụ thể
   * @param fileId ID của file
   * @returns Thông tin vector store của file hoặc null nếu không tìm thấy
   */
  async findVectorStoreByFileId(fileId: string): Promise<FileVectorStoreInfo | null> {
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('vsf.vector_store_id', 'vectorStoreId')
        .addSelect('vs.name', 'vectorStoreName')
        .from('vector_store_files', 'vsf')
        .leftJoin('vector_stores', 'vs', 'vs.id = vsf.vector_store_id')
        .where('vsf.file_id = :fileId', { fileId })
        .getRawOne();

      return result || null;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin vector store của file: ${error.message}`, error.stack);
      return null;
    }
  }
  
  /**
   * Đếm số lượng file trong vector store
   * @param vectorStoreId ID của vector store
   * @returns Số lượng file trong vector store
   */
  async countByVectorStoreId(vectorStoreId: string): Promise<number> {
    try {
      return this.count({
        where: { vectorStoreId }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng file trong vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy số lượng file trong mỗi vector store
   * @param vectorStoreIds Danh sách ID của các vector store
   * @returns Danh sách kết quả với storeId và fileCount
   */
  async countFilesByVectorStoreIds(vectorStoreIds: string[]): Promise<{ storeId: string; fileCount: number }[]> {
    try {
      if (!vectorStoreIds.length) return [];

      const result = await this.createBaseQuery()
        .select('vsf.vector_store_id', 'storeId')
        .addSelect('COUNT(vsf.file_id)', 'fileCount')
        .where('vsf.vector_store_id IN (:...vectorStoreIds)', { vectorStoreIds })
        .groupBy('vsf.vector_store_id')
        .getRawMany();

      return result.map(item => ({
        storeId: item.storeId,
        fileCount: parseInt(item.fileCount)
      }));
    } catch (error) {
      this.logger.error(`Lỗi khi đếm số lượng file trong các vector store: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm vector store file theo file ID
   * @param fileId ID của file
   * @returns Vector store file nếu tìm thấy, null nếu không tìm thấy
   */
  async findByFileId(fileId: string): Promise<VectorStoreFile | null> {
    try {
      return this.findOne({
        where: { fileId }
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tìm vector store file theo file ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Xóa các liên kết vector store file theo file ID
   * @param fileId ID của file
   * @returns Kết quả xóa với các vector store ID đã bị ảnh hưởng
   */
  async deleteByFileId(fileId: string): Promise<{ affected: number; vectorStoreIds: string[] }> {
    try {
      // Tìm các vector store ID trước khi xóa
      const vectorStoreFiles = await this.find({ where: { fileId } });
      const vectorStoreIds = vectorStoreFiles.map(vsf => vsf.vectorStoreId);

      // Xóa các liên kết
      const result = await this.delete({ fileId });

      return {
        affected: result.affected || 0,
        vectorStoreIds
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa vector store file theo file ID: ${error.message}`, error.stack);
      return { affected: 0, vectorStoreIds: [] };
    }
  }
}
