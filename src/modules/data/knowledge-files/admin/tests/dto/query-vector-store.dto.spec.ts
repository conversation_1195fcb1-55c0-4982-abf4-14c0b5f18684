import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryVectorStoreDto } from '../../dto/query-vector-store.dto';
import { SortDirection } from '@common/dto/query.dto';
import { OwnerType } from '@shared/enums';

describe('QueryVectorStoreDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các tham số', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      page: 1,
      limit: 10,
      search: 'test',
      sortBy: 'name',
      sortDirection: SortDirection.ASC,
      ownerType: OwnerType.ADMIN
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi page là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      page: -1
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('page');
  });

  it('nên thất bại khi limit là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      limit: -1
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('limit');
  });

  it('nên thất bại khi sortDirection không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      sortDirection: 'INVALID'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('sortDirection');
    expect(errorJson).toContain('isEnum');
  });

  it('nên thất bại khi ownerType không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryVectorStoreDto, {
      ownerType: 'INVALID'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('ownerType');
    expect(errorJson).toContain('isEnum');
  });

  it('nên xác thực với sortBy hợp lệ', async () => {
    // Arrange
    const validSortByValues = ['name', 'createdAt', 'storage'];
    
    for (const sortBy of validSortByValues) {
      const dto = plainToInstance(QueryVectorStoreDto, {
        sortBy
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    }
  });

  it('nên xác thực với ownerType hợp lệ', async () => {
    // Arrange
    const validOwnerTypes = [OwnerType.ADMIN, OwnerType.USER];
    
    for (const ownerType of validOwnerTypes) {
      const dto = plainToInstance(QueryVectorStoreDto, {
        ownerType
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    }
  });
});
