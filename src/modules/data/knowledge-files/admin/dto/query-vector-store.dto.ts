import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { Type } from 'class-transformer';
import { OwnerType } from '@shared/enums';

export class QueryVectorStoreDto extends QueryDto {
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['name', 'createdAt', 'storage'],
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;

  @ApiProperty({
    description: 'Lọc theo loại chủ sở hữu (USER hoặc ADMIN)',
    enum: OwnerType,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnerType)
  ownerType?: OwnerType;
}
