import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString, Max, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class AssignFilesDto {
  @ApiProperty({
    description: 'Danh sách ID của các file cần gán vào vector store',
    example: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    type: [String],
  })
  @IsArray({ message: 'fileIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một file ID' })
  @IsString({ each: true, message: 'Mỗi file ID phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'File ID không được để trống' })
  fileIds: string[];

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thước của mỗi chunk khi xử lý file (số từ trong mỗi đoạn)',
    example: 2000,
    required: false,
    default: 2000,
    minimum: 100,
    maximum: 8000,
  })
  @IsOptional()
  @IsInt({ message: 'chunkSize phải là số nguyên' })
  @Min(100, { message: 'chunkSize tối thiểu là 100' })
  @Max(8000, { message: 'chunkSize tối đa là 8000' })
  @Type(() => Number)
  chunkSize?: number = 2000;

  @ApiProperty({
    description: 'Số từ chồng lấp giữa các chunk liên tiếp',
    example: 100,
    required: false,
    default: 100,
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsInt({ message: 'chunkOverlap phải là số nguyên' })
  @Min(0, { message: 'chunkOverlap tối thiểu là 0' })
  @Max(1000, { message: 'chunkOverlap tối đa là 1000' })
  @Type(() => Number)
  chunkOverlap?: number = 100;
}
