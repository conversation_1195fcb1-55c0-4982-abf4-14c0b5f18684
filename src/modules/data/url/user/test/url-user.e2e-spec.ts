import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { UrlUserModule } from '../url-user.module';
import { UrlUserService } from '../services/url-user.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Url } from '../../entities/url.entity';
import { UrlRepository } from '../../repositories';
import { SortDirection } from '@common/dto/query.dto';

describe('UrlUserController (e2e)', () => {
  let app: INestApplication;
  let urlUserService: UrlUserService;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  // Mock JwtAuthGuard để bỏ qua xác thực
  const mockJwtAuthGuard = {
    canActivate: (context) => {
      const req = context.switchToHttp().getRequest();
      req.user = { id: 1, email: '<EMAIL>' };
      return true;
    },
  };

  beforeEach(async () => {
    // Tạo mock repository và service
    const mockUrlRepository = {
      create: jest.fn().mockImplementation((dto) => dto),
      save: jest.fn().mockImplementation((url) => Promise.resolve({ id: 'test-id', ...url })),
      findOne: jest.fn().mockResolvedValue(null),
      remove: jest.fn().mockResolvedValue(mockUrl),
    };

    const mockUrlCustomRepository = {
      findUrlById: jest.fn().mockResolvedValue(mockUrl),
      findUrlsByOwner: jest.fn().mockResolvedValue(mockPaginatedResult),
      searchUrls: jest.fn().mockResolvedValue(mockUrls),
    };

    const module: TestingModule = await Test.createTestingModule({
      imports: [UrlUserModule],
    })
      .overrideProvider(getRepositoryToken(Url))
      .useValue(mockUrlRepository)
      .overrideProvider(UrlRepository)
      .useValue(mockUrlCustomRepository)
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = module.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();

    urlUserService = module.get<UrlUserService>(UrlUserService);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/data/url (GET)', () => {
    it('should return paginated URLs', () => {
      return request(app.getHttpServer())
        .get('/data/url')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toEqual(mockPaginatedResult);
          expect(res.body.success).toBe(true);
        });
    });

    it('should handle search parameters', () => {
      jest.spyOn(urlUserService, 'findUrlsByOwner').mockResolvedValueOnce(mockPaginatedResult);

      return request(app.getHttpServer())
        .get('/data/url?keyword=test&type=web&tags=tag1,tag2')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toEqual(mockPaginatedResult);
          expect(res.body.success).toBe(true);
          expect(urlUserService.findUrlsByOwner).toHaveBeenCalledWith(
            1,
            1,
            10,
            'createdAt',
            SortDirection.DESC,
            'test',
            'web',
            ['tag1', 'tag2']
          );
        });
    });
  });

  describe('/data/url/:id (GET)', () => {
    it('should return a URL by ID', () => {
      return request(app.getHttpServer())
        .get('/data/url/test-id')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toEqual(mockUrl);
          expect(res.body.success).toBe(true);
        });
    });

    it('should return 404 if URL does not exist', () => {
      jest.spyOn(urlUserService, 'findUrlById').mockRejectedValueOnce(new Error('URL not found'));

      return request(app.getHttpServer())
        .get('/data/url/non-existent-id')
        .expect(500); // Trong môi trường test, lỗi sẽ trả về 500 thay vì 404
    });
  });

  describe('/data/url (POST)', () => {
    it('should create a new URL', () => {
      const createUrlDto = {
        url: 'https://example.com',
        title: 'Test URL',
        content: 'Test content',
        type: 'web',
        tags: ['test'],
      };

      return request(app.getHttpServer())
        .post('/data/url')
        .send(createUrlDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.data).toEqual(mockUrl);
          expect(res.body.success).toBe(true);
          expect(res.body.message).toBe('URL đã được tạo thành công');
        });
    });

    it('should return 400 if URL format is invalid', () => {
      const invalidUrlDto = {
        url: 'invalid-url',
        title: 'Invalid URL',
        content: 'Invalid content',
      };

      jest.spyOn(urlUserService, 'createUrl').mockRejectedValueOnce(new Error('Invalid URL format'));

      return request(app.getHttpServer())
        .post('/data/url')
        .send(invalidUrlDto)
        .expect(500); // Trong môi trường test, lỗi sẽ trả về 500 thay vì 400
    });

    it('should return 400 if required fields are missing', () => {
      const incompleteUrlDto: Partial<CreateUrlDto> = {
        url: 'https://example.com',
        // Missing title and content
      };

      return request(app.getHttpServer())
        .post('/data/url')
        .send(incompleteUrlDto)
        .expect(400);
    });
  });

  describe('/data/url/:id (PUT)', () => {
    it('should update an existing URL', () => {
      const updateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      return request(app.getHttpServer())
        .put('/data/url/test-id')
        .send(updateUrlDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toEqual(mockUrl);
          expect(res.body.success).toBe(true);
          expect(res.body.message).toBe('URL đã được cập nhật thành công');
        });
    });

    it('should return 404 if URL does not exist', () => {
      const updateUrlDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      jest.spyOn(urlUserService, 'updateUrl').mockRejectedValueOnce(new Error('URL not found'));

      return request(app.getHttpServer())
        .put('/data/url/non-existent-id')
        .send(updateUrlDto)
        .expect(500); // Trong môi trường test, lỗi sẽ trả về 500 thay vì 404
    });
  });

  describe('/data/url/:id (DELETE)', () => {
    it('should delete an existing URL', () => {
      return request(app.getHttpServer())
        .delete('/data/url/test-id')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toBeNull();
          expect(res.body.success).toBe(true);
          expect(res.body.message).toBe('URL đã được xóa thành công');
        });
    });

    it('should return 404 if URL does not exist', () => {
      jest.spyOn(urlUserService, 'deleteUrl').mockRejectedValueOnce(new Error('URL not found'));

      return request(app.getHttpServer())
        .delete('/data/url/non-existent-id')
        .expect(500); // Trong môi trường test, lỗi sẽ trả về 500 thay vì 404
    });
  });
});
