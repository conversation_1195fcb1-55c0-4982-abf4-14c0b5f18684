import { Injectable, Logger } from '@nestjs/common';
import { Processor, InjectQueue, WorkerHost, OnQueueEvent } from '@nestjs/bullmq';
import { CrawlUrlJobName, QueueName } from '@shared/queue/queue.constants';
import { Job, Queue } from 'bullmq';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { UrlRepository } from '../../repositories/url.repository';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { CrawlDto } from '../dto/crawl.dto';
import { ExtractedMetadata } from '../interfaces/extracted-metadata.interface';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { backOff } from 'exponential-backoff';
import { URL_ERROR_CODES } from 'src/modules/data/url/exceptions';
import { AppException } from '@/common';
import { ProxyRotationService } from '../../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../../shared/services/user-agent-rotation.service';
import { AdvancedCrawlerService } from '../../shared/services/advanced-crawler.service';

// Enum cho các loại lỗi crawl
enum CrawlErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  ROBOTS_BLOCKED = 'ROBOTS_BLOCKED',
  INVALID_URL = 'INVALID_URL',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Interface cho thông tin lỗi chi tiết
interface CrawlError {
  type: CrawlErrorType;
  message: string;
  url: string;
  statusCode?: number;
  retryable: boolean;
  retryAfter?: number; // seconds
}

// Interface cho progress tracking
interface CrawlProgress {
  totalUrls: number;
  processedUrls: number;
  successfulUrls: number;
  failedUrls: number;
  currentDepth: number;
  startTime: number;
  estimatedTimeRemaining?: number;
  currentUrl?: string;
  errors: CrawlError[];
}

// Type cho progress callback
type ProgressCallback = (progress: CrawlProgress) => void;

@Injectable()
@Processor(QueueName.CRAWL_URL, {
  concurrency: 100, // ✅ Số lượng job xử lí song song tối đa
})
export class CrawlWorker extends WorkerHost {
  private readonly logger = new Logger(CrawlWorker.name);
  private progressCallbacks = new Map<string, ProgressCallback>(); // sessionId -> callback

  // Cache cho robots.txt
  private robotsCache = new Map<string, { allowed: boolean; expiry: number }>(); // domain -> cache
  private readonly ROBOTS_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 giờ

  // Cache cho DNS lookups
  private dnsCache = new Map<string, { ip: string; expiry: number }>(); // hostname -> cache
  private readonly DNS_CACHE_TTL = 60 * 60 * 1000; // 1 giờ

  // Cache cho metadata
  private metadataCache = new Map<
    string,
    { metadata: ExtractedMetadata; expiry: number }
  >(); // url -> cache
  private readonly METADATA_CACHE_TTL = 30 * 60 * 1000; // 30 phút

  // Rate limiting per domain - Tối ưu cho crawling nhanh hơn
  private domainRateLimits = new Map<
    string,
    { lastRequest: number; requestCount: number; resetTime: number }
  >(); // domain -> rate limit info
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 phút
  private readonly MAX_REQUESTS_PER_MINUTE = 30; // ✅ Tối đa 30 requests/phút
  private readonly MIN_DELAY_BETWEEN_REQUESTS = 100; // ✅ OPTIMIZED: 100ms delay giữa các requests

  // 🎯 OPTIMIZATION: Domain-level website analysis cache
  private websiteAnalysisCache = new Map<string, {
    type: string;
    framework: string;
    needsBrowser: boolean;
    confidence: number;
    cachedAt: number;
  }>();

  // Cache TTL: 1 hour
  private readonly WEBSITE_ANALYSIS_CACHE_TTL = 60 * 60 * 1000;

  // 🎯 OPTIMIZATION: Adaptive rate limiting
  private domainSuccessRates = new Map<string, {
    successCount: number;
    totalCount: number;
    lastUpdated: number;
  }>();

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(CrawlSession)
    private readonly crawlSessionRepository: Repository<CrawlSession>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly crawlSessionCustomRepository: CrawlSessionRepository,
    private readonly httpService: HttpService,
    private readonly proxyRotationService: ProxyRotationService,
    private readonly userAgentRotationService: UserAgentRotationService,
    private readonly advancedCrawlerService: AdvancedCrawlerService,
    @InjectQueue(QueueName.CRAWL_URL)
    private readonly crawlQueue: Queue,
  ) {
    super();
    this.logger.log('🚀 CrawlWorker initialized successfully');
    this.logger.log(`🔧 Worker listening for jobs on queue: ${QueueName.CRAWL_URL}`);
    this.logger.log(`🔧 Worker will process job names: ${CrawlUrlJobName.CRAWL_URL}`);

    // ✅ Setup worker event listeners trước khi khởi tạo connection
    this.setupWorkerEventListeners();

    // ✅ Kiểm tra và đợi Redis connection sẵn sàng
    this.initializeWorkerConnection();

    // Cleanup cache mỗi 30 phút
    setInterval(
      () => {
        this.cleanupExpiredCache();
      },
      30 * 60 * 1000,
    );
  }

  /**
   * ✅ Setup worker event listeners để debug và monitor worker lifecycle
   */
  private setupWorkerEventListeners(): void {
    // Sử dụng setTimeout để setup events sau khi worker được khởi tạo hoàn toàn
    setTimeout(() => {
      try {
        // Perform worker warm-up ngay lập tức
        this.performWorkerWarmup();
      } catch (error) {
        this.logger.error(`❌ Error setting up worker events: ${error.message}`);
      }
    }, 1000);
  }

  /**
   * ✅ Perform worker warm-up để đảm bảo worker thực sự sẵn sàng
   */
  private async performWorkerWarmup(): Promise<void> {
    try {
      // Đợi một chút để đảm bảo tất cả connections đã ổn định
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Kiểm tra queue operations
      const waitingJobs = await this.crawlQueue.getWaiting();
      const activeJobs = await this.crawlQueue.getActive();

      // Chỉ log nếu có jobs đang chờ (cần debug)
      if (waitingJobs.length > 0) {
        this.logger.log(`🔍 Found ${waitingJobs.length} waiting jobs after warm-up`);
        waitingJobs.slice(0, 3).forEach(job => {
          this.logger.log(`📋 Waiting job: ${job.id} (${job.name}) - added at ${new Date(job.timestamp)}`);
        });

        // Trigger manual check cho waiting jobs nếu cần
        this.logger.log('🔄 Triggering manual check for waiting jobs...');
        setTimeout(async () => {
          const stillWaiting = await this.crawlQueue.getWaiting();
          if (stillWaiting.length > 0) {
            this.logger.warn(`⚠️ Still have ${stillWaiting.length} waiting jobs after warm-up`);
          }
        }, 5000);
      }

    } catch (error) {
      this.logger.error(`❌ Worker warm-up failed: ${error.message}`);
    }
  }

  /**
   * ✅ Khởi tạo và kiểm tra Redis connection cho worker
   * Đây là giải pháp cho vấn đề worker không xử lý job đầu tiên sau restart
   */
  private async initializeWorkerConnection(): Promise<void> {
    try {
      this.logger.log('🔄 Đang kiểm tra Redis connection cho worker...');

      // ✅ Sử dụng cách tiếp cận khác thay vì truy cập trực tiếp connection
      // Kiểm tra queue có sẵn sàng bằng cách thử thực hiện một operation đơn giản
      const waitingJobs = await this.crawlQueue.getWaiting();

      // Chỉ log nếu có waiting jobs (cần debug)
      if (waitingJobs.length > 0) {
        this.logger.log(`🔧 Worker ready - waiting jobs: ${waitingJobs.length}`);
      }

      // ✅ Thêm periodic health check
      this.startPeriodicHealthCheck();

    } catch (error) {
      this.logger.error(`❌ Lỗi khi khởi tạo Redis connection: ${error.message}`);

      // Thử đợi một chút và retry
      try {
        this.logger.log('🔄 Retry kiểm tra connection cho worker...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        await this.crawlQueue.getWaiting();
        this.logger.log('✅ Redis connection đã sẵn sàng cho worker sau retry');

        // ✅ Thêm periodic health check sau retry thành công
        this.startPeriodicHealthCheck();
      } catch (retryError) {
        this.logger.error(`❌ Retry thất bại cho worker: ${retryError.message}`);
        this.logger.error('⚠️ Worker có thể không xử lý job đầu tiên đúng cách');

        // Không throw error để không crash worker, chỉ log warning
        // Worker vẫn có thể hoạt động sau khi connection ổn định
      }
    }
  }

  /**
   * ✅ Start periodic health check để monitor worker state
   */
  private startPeriodicHealthCheck(): void {
    setInterval(async () => {
      try {
        const waitingJobs = await this.crawlQueue.getWaiting();
        const activeJobs = await this.crawlQueue.getActive();
        const completedJobs = await this.crawlQueue.getCompleted();
        const failedJobs = await this.crawlQueue.getFailed();

        // Chỉ log khi có jobs hoặc mỗi 5 phút
        const now = Date.now();
        if (waitingJobs.length > 0 || activeJobs.length > 0 || now % (5 * 60 * 1000) < 30000) {
          this.logger.log(`🏥 Health check - waiting: ${waitingJobs.length}, active: ${activeJobs.length}, completed: ${completedJobs.length}, failed: ${failedJobs.length}`);
        }

        // Cảnh báo nếu có jobs chờ quá lâu
        if (waitingJobs.length > 0) {
          const oldestJob = waitingJobs[0];
          const waitTime = now - oldestJob.timestamp;
          if (waitTime > 60000) { // 1 phút
            this.logger.warn(`⚠️ Job ${oldestJob.id} đã chờ ${Math.round(waitTime/1000)}s`);
          }
        }

      } catch (error) {
        this.logger.error(`❌ Health check failed: ${error.message}`);
      }
    }, 30000); // Check mỗi 30 giây
  }

  @OnQueueEvent('failed')
  onFailed(job: Job, error: Error) {
    this.logger.error(`❌ Job ${job.id} failed: ${error.message}`);
  }

  @OnQueueEvent('active')
  onActive(job: Job) {
    this.logger.log(`🚀 Job ${job.id} started processing (concurrent jobs active)`);
  }

  @OnQueueEvent('completed')
  onCompleted(job: Job) {
    this.logger.log(`✅ Job ${job.id} completed successfully`);
  }

  @OnQueueEvent('stalled')
  onStalled(job: Job) {
    this.logger.warn(`⚠️ Job ${job.id} stalled and will be retried`);
  }

  async process(job: Job<{userId: number, crawlDto: CrawlDto, sessionId?: string, cancelled?: boolean}>) {
    console.log(`🚀 CrawlWorker: Bắt đầu xử lý job ${job.id}`);
    this.logger.log(`🚀 CrawlWorker: Bắt đầu xử lý job ${job.id}`);
    this.logger.log(`📊 Job details: name=${job.name}, id=${job.id}, data=${JSON.stringify(job.data)}`);

    try {
      switch (job.name) {
        case CrawlUrlJobName.CRAWL_URL:
          console.log(`✅ Processing CRAWL_URL job ${job.id}`);
          this.logger.log(`✅ Processing CRAWL_URL job ${job.id}`);
          return await this.handleCrawlUrl(job);
        default:
          throw new Error(`Không hỗ trợ job name: ${job.name}`);
      }
    } catch (error) {
      console.log(`❌ Lỗi khi xử lý job ${job.name}: ${error.message}`);
      this.logger.error(`❌ Lỗi khi xử lý job ${job.name}: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async handleCrawlUrl(job: Job<{userId: number, crawlDto: CrawlDto, sessionId?: string, cancelled?: boolean}>) {
    const { userId, crawlDto, sessionId, cancelled } = job.data;
    this.logger.log(`📋 Job data: userId=${userId}, url=${crawlDto.url}, sessionId=${sessionId}, cancelled=${cancelled}`);

    // Kiểm tra job có bị hủy không ngay từ đầu
    if (cancelled) {
      this.logger.log(`🛑 Job ${job.id} đã bị hủy, không thực hiện crawl`);
      return {
        status: 'cancelled',
        message: 'Job đã bị hủy bởi người dùng',
        urlsProcessed: 0,
        urlsSaved: 0,
      };
    }

    try {
      // Nếu có sessionId, cập nhật session trước khi bắt đầu
      if (sessionId) {
        const maxUrls = crawlDto.maxUrls || 20;
        await this.updateSessionProgress(sessionId, {
          totalUrls: maxUrls, // ✅ Sử dụng maxUrls từ user input
          processedUrls: 0,
          successfulUrls: 0,
          failedUrls: 0,
          currentDepth: 0,
          percentage: 0,
        });
      }

      // Thực hiện crawl
      const result = await this.crawlUrl(userId, crawlDto, sessionId, job);

      // ✅ Kiểm tra xem session có bị hủy không trước khi cập nhật kết quả
      if (sessionId && result.status !== 'cancelled') {
        // Kiểm tra trạng thái session hiện tại
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (currentSession && currentSession.status === 'cancelled') {
          this.logger.log(`🛑 Session ${sessionId} đã bị hủy, không cập nhật kết quả thành completed`);
          return {
            status: 'cancelled',
            message: 'Session đã bị hủy bởi người dùng',
            urlsProcessed: result.urlsProcessed || 0,
            urlsSaved: result.urlsSaved || 0,
          };
        }

        // Chỉ cập nhật nếu session chưa bị hủy
        const status = result.status === 'success' ? 'completed' : 'error';
        await this.updateSessionResult(sessionId, status, {
          urlsProcessed: result.urlsProcessed || 0,
          urlsSaved: result.urlsSaved || 0,
          message: result.message,
          errors: result.errors,
        });
      }

      return result;
    } catch (error) {
      // Cập nhật session với lỗi nếu có (chỉ khi chưa bị hủy)
      if (sessionId) {
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (!currentSession || currentSession.status !== 'cancelled') {
          await this.updateSessionResult(sessionId, 'error', {
            urlsProcessed: 0,
            urlsSaved: 0,
            message: error.message || 'Lỗi không xác định',
            errors: [error.message || 'Lỗi không xác định'],
          });
        }
      }
      throw error;
    }
  }

  /**
   * Đăng ký progress callback cho một session crawl
   * @param sessionId ID của session crawl
   * @param callback Callback function để nhận progress updates
   */
  registerProgressCallback(
    sessionId: string,
    callback: ProgressCallback,
  ): void {
    this.progressCallbacks.set(sessionId, callback);
  }

  /**
   * Hủy đăng ký progress callback
   * @param sessionId ID của session crawl
   */
  unregisterProgressCallback(sessionId: string): void {
    this.progressCallbacks.delete(sessionId);
  }

  /**
   * Crawl URL và các URL con để lấy metadata từ thẻ head với xử lý song song và progress tracking
   * @param userId ID của người dùng
   * @param crawlDto Thông tin URL cần crawl
   * @param sessionId ID session để track progress (optional)
   * @param job Bull job instance để kiểm tra cancellation
   * @returns Danh sách các URL với metadata
   */
  async crawlUrl(
    userId: number,
    crawlDto: CrawlDto,
    sessionId?: string,
    job?: Job,
  ): Promise<{
    status: string;
    message: string;
    urlsProcessed?: number;
    urlsSaved?: number;
    // results?: ExtractedMetadata[],
    errors?: string[];
  }> {
    // Mảng lưu trữ các lỗi gặp phải
    const errors: string[] = [];

    try {
      this.logger.log(`===== BẮT ĐẦU CRAWL URL =====`);
      this.logger.log(
        `User: ${userId}, URL: ${crawlDto.url}, Độ sâu: ${crawlDto.depth}`,
      );

      // Kiểm tra URL có hợp lệ không
      try {
        const urlObj = new URL(crawlDto.url);
        this.logger.log(`URL hợp lệ: ${urlObj.href}`);

        // Chuẩn hóa URL
        const normalizedUrl = this.normalizeUrl(crawlDto.url);
        if (normalizedUrl !== crawlDto.url) {
          this.logger.log(`URL đã được chuẩn hóa: ${normalizedUrl}`);
          crawlDto.url = normalizedUrl;
        }

        // Kiểm tra robots.txt nếu không bỏ qua
        if (!crawlDto.ignoreRobotsTxt) {
          const isAllowed = await this.checkRobotsPermission(crawlDto.url);
          if (!isAllowed) {
            const robotsMsg = `URL không được phép crawl theo robots.txt: ${crawlDto.url}`;
            this.logger.error(robotsMsg);
            errors.push(robotsMsg);
            throw new AppException(URL_ERROR_CODES.URL_CRAWL_FAILED, robotsMsg);
          }
        } else {
          this.logger.log(`Bỏ qua kiểm tra robots.txt theo yêu cầu người dùng`);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }

        const errorMsg = `URL không đúng định dạng: ${error.message}`;
        this.logger.error(errorMsg);
        errors.push(errorMsg);
        throw new AppException(URL_ERROR_CODES.URL_INVALID_FORMAT, errorMsg);
      }

      // Giới hạn số lượng URL tối đa và concurrency - Tối ưu cho tốc độ
      const MAX_URLS = crawlDto.maxUrls || 20;
      const CONCURRENCY_LIMIT = 10; // ✅ OPTIMIZED: Xử lý 10 URLs song song trong 1 job
      this.logger.log(
        `Giới hạn tối đa: ${MAX_URLS} URLs, độ sâu tối đa: ${crawlDto.depth}, concurrency: ${CONCURRENCY_LIMIT}`,
      );

      // Khởi tạo các biến cần thiết
      const visitedUrls = new Set<string>();
      const urlsToVisit: Array<{ url: string; depth: number }> = [
        { url: crawlDto.url, depth: 0 },
      ];
      const processedUrls: ExtractedMetadata[] = [];
      const crawlErrors: CrawlError[] = [];
      let totalCrawledUrls = 0; // Tổng số URLs đã crawl thành công (có hoặc không có metadata)

      // Khởi tạo progress tracking với totalUrls = maxUrls từ user input
      const startTime = Date.now();
      const progress: CrawlProgress = {
        totalUrls: MAX_URLS, // ✅ Sử dụng maxUrls từ user input làm totalUrls
        processedUrls: 0,
        successfulUrls: 0,
        failedUrls: 0,
        currentDepth: 0,
        startTime,
        errors: crawlErrors,
      };

      // Tạo session ID nếu chưa có
      const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;

      this.logger.log(
        `===== BẮT ĐẦU VÒNG LẶP CRAWL URL VỚI XỬ LÝ SONG SONG [${trackingSessionId}] =====`,
      );

      // Cập nhật progress ban đầu
      if (sessionId) {
        this.logger.log(`🔄 Cập nhật progress ban đầu cho session ${sessionId}`);
        await this.updateProgress(sessionId, progress);
      }

      // ✅ Biến theo dõi trạng thái hủy
      let isCancelled = false;

      // Crawl URLs với xử lý song song
      while (urlsToVisit.length > 0 && visitedUrls.size < MAX_URLS && !isCancelled) {
        // ✅ Sử dụng helper function để kiểm tra hủy
        isCancelled = await this.checkCancellation(sessionId, job);
        if (isCancelled) {
          this.logger.log(`🛑 Crawl đã bị hủy, dừng vòng lặp chính`);
          break;
        }
        // ✅ Không cập nhật totalUrls nữa, giữ nguyên MAX_URLS từ user input
        // progress.totalUrls luôn = MAX_URLS để tính % chính xác

        // Lấy batch URLs để xử lý song song
        const currentBatch: Array<{ url: string; depth: number }> = [];

        // Lấy tối đa CONCURRENCY_LIMIT URLs chưa được xử lý
        while (
          currentBatch.length < CONCURRENCY_LIMIT &&
          urlsToVisit.length > 0
        ) {
          const currentItem = urlsToVisit.shift();
          if (!currentItem) continue;

          const { url } = currentItem;

          // Bỏ qua nếu URL đã được xử lý
          if (visitedUrls.has(url)) continue;

          // Đánh dấu URL đã được xử lý
          visitedUrls.add(url);
          currentBatch.push(currentItem);
        }

        if (currentBatch.length === 0) break;

        // Cập nhật current depth và URL
        progress.currentDepth = Math.max(
          ...currentBatch.map((item) => item.depth),
        );
        progress.currentUrl = currentBatch[0].url;

        // 🎯 OPTIMIZATION: Reduce logging overhead
        this.logger.log(
          `Xử lý batch ${currentBatch.length} URLs song song (depth ${progress.currentDepth})`,
        );

        // Xử lý batch URLs song song với smart crawling
        const batchResults = await this.processConcurrentUrlsWithSmartCrawling(
          currentBatch,
          userId,
          crawlDto,
          visitedUrls,
          urlsToVisit,
          errors,
          sessionId, // ✅ Thêm sessionId để kiểm tra hủy
          job, // ✅ Thêm job để kiểm tra hủy
        );

        // Cập nhật progress và tổng số URLs đã crawl
        progress.processedUrls += currentBatch.length;
        progress.successfulUrls += batchResults.length;
        progress.failedUrls += currentBatch.length - batchResults.length;
        totalCrawledUrls += batchResults.length; // Tất cả URLs trong batchResults đều được coi là thành công

        // Lưu batch metadata vào database
        if (batchResults.length > 0) {
          try {
            const savedCount = await this.saveBatchMetadata(
              userId,
              batchResults,
            );
            this.logger.log(
              `💾 Batch save: ${savedCount}/${batchResults.length} metadata đã lưu vào database thành công`,
            );

            if (savedCount < batchResults.length) {
              this.logger.warn(
                `⚠️ Một số metadata không được lưu thành công: ${batchResults.length - savedCount}`,
              );
            }
          } catch (saveError) {
            this.logger.error(
              `❌ Lỗi khi lưu batch metadata: ${saveError.message}`,
            );
            errors.push(`Lỗi lưu database: ${saveError.message}`);
          }
        }

        // Thêm kết quả thành công vào danh sách
        processedUrls.push(...batchResults);

        this.logger.log(
          `Hoàn thành batch: ${batchResults.length}/${currentBatch.length} URLs thành công`,
        );

        // ✅ Kiểm tra hủy sau khi xử lý batch
        isCancelled = await this.checkCancellation(sessionId, job);
        if (isCancelled) {
          this.logger.log(`🛑 Crawl đã bị hủy sau khi xử lý batch, dừng ngay lập tức`);
          break;
        }

        // 🎯 OPTIMIZATION: Batch progress updates (mỗi 5 URLs hoặc 10% thay vì mỗi batch)
        if (sessionId) {
          const percentage = Math.min(
            Math.round((progress.processedUrls / progress.totalUrls) * 100),
            100
          );

          // Chỉ update progress khi:
          // 1. Mỗi 5 URLs được xử lý, HOẶC
          // 2. Mỗi 10% progress, HOẶC
          // 3. Batch cuối cùng
          const shouldUpdate =
            progress.processedUrls % 5 === 0 ||
            percentage % 10 === 0 ||
            visitedUrls.size >= MAX_URLS ||
            urlsToVisit.length === 0;

          if (shouldUpdate) {
            this.logger.log(`🔄 Cập nhật progress batch cho session ${sessionId}: ${progress.processedUrls}/${progress.totalUrls} (${percentage}%)`);
            await this.updateProgress(sessionId, progress);
          } else {
            this.logger.debug(`⏭️ Skip progress update: ${progress.processedUrls}/${progress.totalUrls} (${percentage}%)`);
          }
        }
      }

      // ✅ Kiểm tra nếu bị hủy, return với status cancelled
      if (isCancelled) {
        const cancelledMessage = `Crawl đã bị hủy bởi người dùng. Đã xử lý ${progress.processedUrls} URL, lưu được ${processedUrls.length} URL có metadata.`;

        this.logger.log(`===== CRAWL BỊ HỦY [${trackingSessionId}] =====`);
        this.logger.log(cancelledMessage);

        // Final progress update cho cancelled
        if (sessionId) {
          progress.currentUrl = undefined;
          this.logger.log(`🔄 Cập nhật progress cuối cùng cho session bị hủy ${sessionId}`);
          await this.updateProgress(sessionId, progress);
          // Cleanup callback sau 30 giây
          setTimeout(() => {
            this.unregisterProgressCallback(sessionId);
          }, 30000);
        }

        // Browser cleanup
        try {
          this.logger.log(`🧹 Performing browser cleanup after cancellation`);
          await this.advancedCrawlerService.closeBrowser();
        } catch (cleanupError) {
          this.logger.warn(`Browser cleanup warning: ${cleanupError.message}`);
        }

        // ✅ Clear cancellation cache khi job bị hủy
        this.clearCancellationCache(sessionId);

        return {
          status: 'cancelled',
          message: cancelledMessage,
          urlsProcessed: progress.processedUrls,
          urlsSaved: processedUrls.length,
          errors: errors.length > 0 ? errors : undefined,
        };
      }

      // Tạo thông báo kết quả cho trường hợp hoàn thành bình thường
      let resultMessage = '';
      if (totalCrawledUrls > 0) {
        const savedCount = processedUrls.length;
        if (savedCount > 0) {
          resultMessage = `Đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Đã lưu ${savedCount} URL có metadata vào database.`;
        } else {
          resultMessage = `Đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Không có metadata để lưu vào database (có thể do anti-bot protection).`;
        }
      } else {
        resultMessage = `Không crawl được URL nào từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      }

      this.logger.log(`===== KẾT THÚC CRAWL URL [${trackingSessionId}] =====`);
      this.logger.log(resultMessage);

      // Final progress update
      if (sessionId) {
        progress.currentUrl = undefined;
        this.logger.log(`🔄 Cập nhật progress cuối cùng cho session ${sessionId}`);
        await this.updateProgress(sessionId, progress);
        // Cleanup callback sau 30 giây
        setTimeout(() => {
          this.unregisterProgressCallback(sessionId);
        }, 30000);
      }

      // ✅ KHÔNG đóng browser sau mỗi job để tránh conflict với jobs khác
      // Browser sẽ được cleanup tự động khi ứng dụng shutdown
      this.logger.log(`🧹 Crawl completed - keeping browser alive for other jobs`);

      // Optional: Cleanup expired cache để giải phóng memory
      try {
        this.cleanupExpiredCache();
      } catch (cleanupError) {
        this.logger.warn(`Cache cleanup warning: ${cleanupError.message}`);
      }

      // ✅ Clear cancellation cache khi job hoàn thành thành công
      this.clearCancellationCache(sessionId);

      return {
        status: 'success',
        message: resultMessage,
        urlsProcessed: totalCrawledUrls, // Số URLs đã crawl thành công
        urlsSaved: processedUrls.length, // Số URLs đã lưu vào database
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      // Xử lý lỗi
      const errorMsg =
        error instanceof AppException
          ? error.message
          : `Lỗi không xác định: ${error.message}`;
      this.logger.error(`Crawl URL thất bại: ${errorMsg}`);

      // Cleanup progress tracking on error
      if (sessionId) {
        const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;
        this.unregisterProgressCallback(trackingSessionId);
      }

      // ✅ KHÔNG đóng browser khi có lỗi để tránh conflict với jobs khác
      // Browser sẽ được cleanup tự động khi ứng dụng shutdown
      this.logger.log(`🚨 Error occurred - keeping browser alive for other jobs`);

      // Optional: Cleanup expired cache để giải phóng memory
      try {
        this.cleanupExpiredCache();
      } catch (cleanupError) {
        this.logger.warn(`Cache cleanup warning: ${cleanupError.message}`);
      }

      // ✅ Clear cancellation cache khi job có lỗi
      this.clearCancellationCache(sessionId);

      return {
        status: 'error',
        message: errorMsg,
        urlsProcessed: 0,
        errors: errors.length > 0 ? [...errors, errorMsg] : [errorMsg],
      };
    }
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();

    // Cleanup robots cache
    for (const [domain, cache] of this.robotsCache.entries()) {
      if (cache.expiry < now) {
        this.robotsCache.delete(domain);
      }
    }

    // Cleanup DNS cache
    for (const [hostname, cache] of this.dnsCache.entries()) {
      if (cache.expiry < now) {
        this.dnsCache.delete(hostname);
      }
    }

    // Cleanup metadata cache
    for (const [url, cache] of this.metadataCache.entries()) {
      if (cache.expiry < now) {
        this.metadataCache.delete(url);
      }
    }

    // Cleanup rate limit data
    for (const [domain, rateLimit] of this.domainRateLimits.entries()) {
      if (rateLimit.resetTime < now) {
        this.domainRateLimits.delete(domain);
      }
    }

    // 🎯 OPTIMIZATION: Cleanup website analysis cache
    for (const [domain, cache] of this.websiteAnalysisCache.entries()) {
      if (cache.cachedAt + this.WEBSITE_ANALYSIS_CACHE_TTL < now) {
        this.websiteAnalysisCache.delete(domain);
      }
    }

    // 🎯 OPTIMIZATION: Cleanup success rate cache
    for (const [domain, successRate] of this.domainSuccessRates.entries()) {
      if (now - successRate.lastUpdated > 60 * 60 * 1000) {
        this.domainSuccessRates.delete(domain);
      }
    }

    this.logger.debug(
      `Cache cleanup completed. Robots: ${this.robotsCache.size}, DNS: ${this.dnsCache.size}, Metadata: ${this.metadataCache.size}, RateLimit: ${this.domainRateLimits.size}, WebsiteAnalysis: ${this.websiteAnalysisCache.size}, SuccessRates: ${this.domainSuccessRates.size}`,
    );
  }

  /**
   * Kiểm tra và áp dụng rate limiting cho domain
   * @param url URL cần kiểm tra
   * @returns Số milliseconds cần chờ trước khi có thể request, 0 nếu có thể request ngay
   */
  private checkRateLimit(url: string): number {
    try {
      const domain = new URL(url).hostname;
      const now = Date.now();

      let rateLimit = this.domainRateLimits.get(domain);

      if (!rateLimit) {
        // Lần đầu tiên request domain này
        rateLimit = {
          lastRequest: now,
          requestCount: 1,
          resetTime: now + this.RATE_LIMIT_WINDOW,
        };
        this.domainRateLimits.set(domain, rateLimit);
        return 0;
      }

      // Reset counter nếu đã hết window
      if (now >= rateLimit.resetTime) {
        rateLimit.requestCount = 1;
        rateLimit.lastRequest = now;
        rateLimit.resetTime = now + this.RATE_LIMIT_WINDOW;
        return 0;
      }

      // Kiểm tra số lượng requests trong window
      if (rateLimit.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
        const waitTime = rateLimit.resetTime - now;
        this.logger.warn(
          `Rate limit exceeded for domain ${domain}, wait ${waitTime}ms`,
        );
        return waitTime;
      }

      // 🎯 OPTIMIZATION: Adaptive delay based on success rate
      const adaptiveDelay = this.getAdaptiveDelay(domain);
      const timeSinceLastRequest = now - rateLimit.lastRequest;
      if (timeSinceLastRequest < adaptiveDelay) {
        const waitTime = adaptiveDelay - timeSinceLastRequest;
        this.logger.debug(
          `Adaptive delay for domain ${domain}, wait ${waitTime}ms (success rate based)`,
        );
        return waitTime;
      }

      // Cập nhật rate limit
      rateLimit.requestCount++;
      rateLimit.lastRequest = now;

      return 0;
    } catch (error) {
      this.logger.warn(
        `Error checking rate limit for ${url}: ${error.message}`,
      );
      return 0; // Cho phép request nếu có lỗi
    }
  }

  /**
   * 🎯 OPTIMIZATION: Get adaptive delay based on domain success rate
   * @param domain Domain to check
   * @returns Delay in milliseconds
   */
  private getAdaptiveDelay(domain: string): number {
    const successRate = this.domainSuccessRates.get(domain);

    if (!successRate || successRate.totalCount < 3) {
      // Chưa có đủ data, sử dụng default delay
      return this.MIN_DELAY_BETWEEN_REQUESTS;
    }

    const rate = successRate.successCount / successRate.totalCount;

    if (rate >= 0.9) {
      // Success rate cao (>=90%), giảm delay xuống 50ms
      return 50;
    } else if (rate >= 0.7) {
      // Success rate trung bình (70-89%), sử dụng default
      return this.MIN_DELAY_BETWEEN_REQUESTS;
    } else {
      // Success rate thấp (<70%), tăng delay lên 200ms
      return 200;
    }
  }

  /**
   * 🎯 OPTIMIZATION: Update domain success rate
   * @param domain Domain to update
   * @param success Whether the request was successful
   */
  private updateDomainSuccessRate(domain: string, success: boolean): void {
    const now = Date.now();
    let successRate = this.domainSuccessRates.get(domain);

    if (!successRate) {
      successRate = {
        successCount: 0,
        totalCount: 0,
        lastUpdated: now,
      };
      this.domainSuccessRates.set(domain, successRate);
    }

    // Reset stats nếu đã quá 1 giờ
    if (now - successRate.lastUpdated > 60 * 60 * 1000) {
      successRate.successCount = 0;
      successRate.totalCount = 0;
    }

    successRate.totalCount++;
    if (success) {
      successRate.successCount++;
    }
    successRate.lastUpdated = now;

    // Giới hạn history để tránh memory leak
    if (successRate.totalCount > 100) {
      successRate.successCount = Math.round(successRate.successCount * 0.8);
      successRate.totalCount = 80;
    }
  }

  /**
   * Chờ theo rate limit nếu cần thiết
   * @param url URL cần request
   */
  private async waitForRateLimit(url: string): Promise<void> {
    const waitTime = this.checkRateLimit(url);
    if (waitTime > 0) {
      this.logger.log(
        `Waiting ${waitTime}ms for rate limit on domain: ${new URL(url).hostname}`,
      );
      await new Promise((resolve) => setTimeout(resolve, waitTime));

      // Kiểm tra lại sau khi chờ
      const remainingWait = this.checkRateLimit(url);
      if (remainingWait > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingWait));
      }
    }
  }

  /**
   * Cập nhật progress và gọi callback nếu có
   * @param sessionId ID của session crawl
   * @param progress Thông tin progress hiện tại
   */
  private async updateProgress(sessionId: string, progress: CrawlProgress): Promise<void> {
    const callback = this.progressCallbacks.get(sessionId);
    if (callback) {
      // Tính toán estimated time remaining
      const elapsedTime = Date.now() - progress.startTime;
      if (progress.processedUrls > 0) {
        const avgTimePerUrl = elapsedTime / progress.processedUrls;
        const remainingUrls = progress.totalUrls - progress.processedUrls;
        progress.estimatedTimeRemaining = Math.round(
          (avgTimePerUrl * remainingUrls) / 1000,
        ); // seconds
      }

      try {
        callback(progress);
      } catch (error) {
        this.logger.warn(
          `Error in progress callback for session ${sessionId}: ${error.message}`,
        );
      }
    }

    // Cập nhật progress vào database
    // ✅ Tính percentage linh hoạt và chính xác
    const percentage = progress.totalUrls > 0
      ? Math.min(Math.round((progress.processedUrls / progress.totalUrls) * 100), 100)
      : 0;

    await this.updateSessionProgress(sessionId, {
      totalUrls: progress.totalUrls,
      processedUrls: progress.processedUrls,
      successfulUrls: progress.successfulUrls,
      failedUrls: progress.failedUrls,
      currentDepth: progress.currentDepth,
      currentUrl: progress.currentUrl,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      percentage,
    });

    // Log progress
    this.logger.log(
      `Progress [${sessionId}]: ${percentage}% (${progress.processedUrls}/${progress.totalUrls}) - Success: ${progress.successfulUrls}, Failed: ${progress.failedUrls}`,
    );
  }

  /**
   * Phân loại lỗi crawl để xử lý thông minh
   * @param error Lỗi gốc
   * @param url URL gây lỗi
   * @returns Thông tin lỗi đã phân loại
   */
  private categorizeError(error: any, url: string): CrawlError {
    // Lỗi timeout
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return {
        type: CrawlErrorType.TIMEOUT_ERROR,
        message: `Timeout khi truy cập ${url}`,
        url,
        retryable: true,
        retryAfter: 5,
      };
    }

    // Lỗi network
    if (
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ECONNRESET'
    ) {
      return {
        type: CrawlErrorType.NETWORK_ERROR,
        message: `Lỗi kết nối mạng: ${error.code}`,
        url,
        retryable: true,
        retryAfter: 10,
      };
    }

    // Lỗi HTTP response
    if (error.response?.status) {
      const status = error.response.status;

      if (status === 429) {
        const retryAfter =
          parseInt(error.response.headers['retry-after']) || 60;
        return {
          type: CrawlErrorType.RATE_LIMIT_ERROR,
          message: `Rate limit exceeded cho ${url}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter,
        };
      }

      if (status >= 400 && status < 500) {
        return {
          type: CrawlErrorType.CLIENT_ERROR,
          message: `Lỗi client ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable:
            status === 408 ||
            status === 409 ||
            status === 423 ||
            status === 424, // Chỉ retry một số lỗi 4xx
        };
      }

      if (status >= 500) {
        return {
          type: CrawlErrorType.SERVER_ERROR,
          message: `Lỗi server ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter: 30,
        };
      }
    }

    // Lỗi parsing
    if (
      error.message?.includes('parse') ||
      error.message?.includes('invalid')
    ) {
      return {
        type: CrawlErrorType.PARSING_ERROR,
        message: `Lỗi phân tích dữ liệu: ${error.message}`,
        url,
        retryable: false,
      };
    }

    // Lỗi không xác định
    return {
      type: CrawlErrorType.UNKNOWN_ERROR,
      message: `Lỗi không xác định: ${error.message}`,
      url,
      retryable: true,
      retryAfter: 15,
    };
  }

  /**
   * Tạo retry strategy thông minh dựa trên loại lỗi
   * @param crawlError Thông tin lỗi đã phân loại
   * @returns Retry configuration
   */
  private createRetryStrategy(crawlError: CrawlError) {
    if (!crawlError.retryable) {
      return {
        numOfAttempts: 1,
        retry: () => false,
      };
    }

    const baseConfig = {
      startingDelay: (crawlError.retryAfter || 1) * 1000,
      timeMultiple: 2,
      maxDelay: 60000,
      delayFirstAttempt: false,
      jitter: 'full' as const,
    };

    switch (crawlError.type) {
      case CrawlErrorType.RATE_LIMIT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 2, // Ít retry hơn cho rate limit
          startingDelay: (crawlError.retryAfter || 60) * 1000,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Rate limit retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 2;
          },
        };

      case CrawlErrorType.TIMEOUT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Timeout retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 3;
          },
        };

      case CrawlErrorType.SERVER_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 4,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Server error retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 4;
          },
        };

      case CrawlErrorType.NETWORK_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          startingDelay: 5000, // Chờ lâu hơn cho network error
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Network error retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 3;
          },
        };

      default:
        return {
          ...baseConfig,
          numOfAttempts: 2,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Generic retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 2;
          },
        };
    }
  }

  /**
   * Trích xuất domain chính từ hostname
   * Ví dụ: www.example.com -> example.com
   * @param hostname Hostname cần trích xuất
   * @returns Domain chính
   */
  private extractMainDomain(hostname: string): string {
    // Loại bỏ www. nếu có
    let domain = hostname.replace(/^www\./, '');

    // Trả về domain chính
    return domain;
  }

  /**
   * Kiểm tra quyền crawl theo robots.txt với cache
   * @param url URL cần kiểm tra
   * @returns true nếu được phép crawl, false nếu không
   */
  private async checkRobotsPermission(url: string): Promise<boolean> {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;
      const robotsUrl = `${parsedUrl.protocol}//${domain}/robots.txt`;

      // Kiểm tra cache trước
      const cached = this.robotsCache.get(domain);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Robots.txt cache hit for domain: ${domain}`);
        return cached.allowed;
      }

      this.logger.log(`Kiểm tra robots.txt: ${robotsUrl}`);

      // Sử dụng thư viện exponential-backoff để retry
      const robotsTxt = await backOff(
        async () => {
          const response = await firstValueFrom(
            this.httpService.get(robotsUrl, {
              timeout: 5000,
              headers: {
                'User-Agent': 'Mozilla/5.0',
              },
            }),
          );
          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: 3,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 5000, // Tối đa 5 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for robots.txt ${robotsUrl}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              return false;
            }

            return true;
          },
        },
      );

      let allowed = true;

      // Phân tích robots.txt đơn giản
      // Kiểm tra xem có bị disallow toàn bộ không
      if (robotsTxt.includes('Disallow: /')) {
        this.logger.warn(`Robots.txt không cho phép crawl: ${url}`);
        allowed = false;
      } else {
        // Kiểm tra xem URL cụ thể có bị disallow không
        const urlPath = parsedUrl.pathname;
        const disallowLines = robotsTxt
          .split('\n')
          .filter((line: string) => line.trim().startsWith('Disallow:'))
          .map((line: string) => line.split('Disallow:')[1].trim());

        for (const disallowPath of disallowLines) {
          if (disallowPath && urlPath.startsWith(disallowPath)) {
            this.logger.warn(
              `Robots.txt không cho phép crawl path: ${urlPath}`,
            );
            allowed = false;
            break;
          }
        }
      }

      // Cache kết quả
      this.robotsCache.set(domain, {
        allowed,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL,
      });

      this.logger.debug(
        `Robots.txt cached for domain: ${domain}, allowed: ${allowed}`,
      );
      return allowed;
    } catch (error) {
      // Nếu không tìm thấy robots.txt, cho phép crawl và cache kết quả
      this.logger.warn(`Không thể kiểm tra robots.txt: ${error.message}`);

      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;

      // Cache cho phép crawl khi không có robots.txt
      this.robotsCache.set(domain, {
        allowed: true,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL,
      });

      return true;
    }
  }

  /**
   * Chuẩn hóa URL
   * @param url URL cần chuẩn hóa
   * @returns URL đã chuẩn hóa
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // Loại bỏ fragment
      urlObj.hash = '';

      // Loại bỏ các query params không cần thiết
      const paramsToRemove = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'fbclid',
        'gclid',
      ];
      const params = new URLSearchParams(urlObj.search);

      paramsToRemove.forEach((param) => {
        if (params.has(param)) {
          params.delete(param);
        }
      });

      urlObj.search = params.toString();

      // Đảm bảo URL kết thúc bằng / nếu không có path
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.toString();
    } catch (error) {
      // Nếu không thể chuẩn hóa, trả về URL gốc
      return url;
    }
  }

  /**
   * Thực hiện HTTP request với retry
   * @param url URL cần request
   * @param maxRetries Số lần retry tối đa
   * @returns Response data
   */
  private async fetchWithRetry(url: string, maxRetries = 3): Promise<any> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching data from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'chrome',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 60000, // 60 giây timeout - Tăng để tránh timeout
            maxRedirects: 5, // Cho phép tối đa 5 lần chuyển hướng
          });

          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Fetch HTML từ URL với retry, chỉ lấy thẻ head
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML của thẻ head
   */
  private async fetchHeadWithRetry(
    url: string,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching head from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'firefox',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 30000, // 30 giây timeout - Tăng để tránh timeout
            maxRedirects: 5,
            responseType: 'text',
          });

          // Sử dụng axios với responseType: 'text' để lấy HTML
          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          const html = response.data;

          // Chỉ trích xuất phần head từ HTML
          const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
          if (headMatch && headMatch[1]) {
            return `<head>${headMatch[1]}</head>`;
          }

          // Nếu không tìm thấy thẻ head, trả về toàn bộ HTML
          return html;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Fetch HTML có giới hạn kích thước với streaming
   * @param url URL cần fetch
   * @param maxSize Kích thước tối đa (bytes)
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã giới hạn kích thước
   */
  private async fetchLimitedHtml(
    url: string,
    maxSize = 50 * 1024,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(
            `Fetching limited HTML (${maxSize} bytes) from URL: ${url}`,
          );

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'safari',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 15000, // 15 giây timeout
            maxRedirects: 5,
            responseType: 'stream',
          });

          // Sử dụng axios với responseType: 'stream' để streaming
          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          return new Promise<string>((resolve, reject) => {
            let htmlBuffer = '';
            let bytesReceived = 0;
            let isComplete = false;

            response.data.on('data', (chunk: Buffer) => {
              if (isComplete) return;

              const chunkStr = chunk.toString('utf8');
              bytesReceived += chunk.length;

              // Kiểm tra nếu đã đạt giới hạn
              if (bytesReceived >= maxSize) {
                const remainingBytes = maxSize - (bytesReceived - chunk.length);
                if (remainingBytes > 0) {
                  htmlBuffer += chunkStr.substring(0, remainingBytes);
                }
                isComplete = true;
                response.data.destroy(); // Dừng stream
                this.logger.log(
                  `Stream stopped at ${maxSize} bytes for memory optimization`,
                );
                resolve(htmlBuffer);
                return;
              }

              htmlBuffer += chunkStr;

              // Kiểm tra xem đã có đủ thông tin metadata chưa (head tag)
              if (
                htmlBuffer.includes('</head>') &&
                htmlBuffer.includes('<title>')
              ) {
                isComplete = true;
                response.data.destroy(); // Dừng stream sớm
                this.logger.log(
                  `Stream stopped early after finding head tag (${bytesReceived} bytes)`,
                );
                resolve(htmlBuffer);
                return;
              }
            });

            response.data.on('end', () => {
              if (!isComplete) {
                resolve(htmlBuffer);
              }
            });

            response.data.on('error', (error: Error) => {
              reject(error);
            });

            // Timeout cho stream
            setTimeout(() => {
              if (!isComplete) {
                isComplete = true;
                response.data.destroy();
                this.logger.warn(
                  `Stream timeout for ${url}, using partial data (${bytesReceived} bytes)`,
                );
                resolve(htmlBuffer);
              }
            }, 10000); // 10 giây timeout cho stream
          });
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lưu batch metadata vào cơ sở dữ liệu với hiệu suất cao và logging chi tiết
   * @param userId ID của người dùng
   * @param metadataList Danh sách metadata cần lưu
   * @returns Số lượng metadata đã lưu thành công
   */
  private async saveBatchMetadata(
    userId: number,
    metadataList: ExtractedMetadata[],
  ): Promise<number> {
    if (!metadataList || metadataList.length === 0) {
      this.logger.warn(`saveBatchMetadata: Không có metadata để lưu`);
      return 0;
    }

    try {
      this.logger.log(
        `🔄 Bắt đầu lưu batch ${metadataList.length} metadata cho user ${userId}`,
      );

      // Log URLs sẽ được xử lý
      const urlsToProcess = metadataList.map((m) => m.url);
      this.logger.debug(
        `URLs cần xử lý: ${urlsToProcess.slice(0, 5).join(', ')}${urlsToProcess.length > 5 ? '...' : ''}`,
      );

      // Lọc metadata hợp lệ
      const validMetadata = metadataList.filter((metadata) => {
        const isValid = this.isValidMetadata(metadata);
        if (!isValid) {
          this.logger.warn(
            `❌ Metadata không hợp lệ cho URL: ${metadata.url} - title: "${metadata.title}", content: "${metadata.content?.substring(0, 50)}..."`,
          );
        }
        return isValid;
      });

      this.logger.log(
        `✅ Metadata hợp lệ: ${validMetadata.length}/${metadataList.length}`,
      );

      if (validMetadata.length === 0) {
        this.logger.warn(`❌ Không có metadata hợp lệ nào để lưu`);
        return 0;
      }

      // Lấy danh sách URL đã tồn tại
      const urls = validMetadata.map((m) => m.url);
      this.logger.debug(
        `🔍 Kiểm tra ${urls.length} URLs đã tồn tại trong DB...`,
      );

      const existingUrls = await this.urlRepository.find({
        where: { url: In(urls), ownedBy: userId },
      });

      this.logger.log(
        `📊 Tìm thấy ${existingUrls.length} URLs đã tồn tại trong DB`,
      );

      if (existingUrls.length > 0) {
        this.logger.debug(
          `URLs đã tồn tại: ${existingUrls
            .map((u) => u.url)
            .slice(0, 3)
            .join(', ')}${existingUrls.length > 3 ? '...' : ''}`,
        );
      }

      // Tạo map để tra cứu nhanh
      const existingUrlMap = new Map(existingUrls.map((url) => [url.url, url]));

      // Phân loại URLs cần cập nhật và tạo mới
      const urlsToUpdate: Url[] = [];
      const urlsToCreate: Url[] = [];
      const currentTime = Date.now();

      for (const metadata of validMetadata) {
        const existingUrl = existingUrlMap.get(metadata.url);

        if (existingUrl) {
          // Cập nhật URL đã tồn tại
          existingUrl.title = metadata.title;
          existingUrl.content = metadata.content;
          existingUrl.tags = metadata.tags; // Lưu tags vào cột tags riêng biệt
          existingUrl.updatedAt = currentTime;
          urlsToUpdate.push(existingUrl);
          this.logger.debug(`🔄 Sẽ cập nhật URL: ${metadata.url}`);
        } else {
          // Tạo URL mới
          const newUrl = new Url();
          newUrl.url = metadata.url;
          newUrl.title = metadata.title;
          newUrl.content = metadata.content;
          newUrl.tags = metadata.tags; // Lưu tags vào cột tags riêng biệt
          newUrl.ownedBy = userId;
          newUrl.createdAt = currentTime;
          newUrl.updatedAt = currentTime;
          urlsToCreate.push(newUrl);
          this.logger.debug(`➕ Sẽ tạo mới URL: ${metadata.url}`);
        }
      }

      this.logger.log(
        `📝 Phân loại hoàn thành: ${urlsToUpdate.length} URLs cập nhật, ${urlsToCreate.length} URLs tạo mới`,
      );

      // Thực hiện batch operations
      let savedCount = 0;

      if (urlsToUpdate.length > 0) {
        try {
          await this.urlRepository.save(urlsToUpdate);
          savedCount += urlsToUpdate.length;
          this.logger.log(`✅ Đã cập nhật ${urlsToUpdate.length} URLs`);
        } catch (updateError) {
          this.logger.error(`❌ Lỗi khi cập nhật URLs: ${updateError.message}`);
        }
      }

      if (urlsToCreate.length > 0) {
        try {
          await this.urlRepository.save(urlsToCreate);
          savedCount += urlsToCreate.length;
          this.logger.log(`✅ Đã tạo mới ${urlsToCreate.length} URLs`);
        } catch (createError) {
          this.logger.error(`❌ Lỗi khi tạo mới URLs: ${createError.message}`);
        }
      }

      this.logger.log(
        `🎉 Hoàn thành lưu batch: ${savedCount}/${metadataList.length} metadata thành công (${validMetadata.length} hợp lệ)`,
      );

      // Log chi tiết nếu có sự khác biệt
      if (savedCount !== metadataList.length) {
        const invalidCount = metadataList.length - validMetadata.length;
        const failedCount = validMetadata.length - savedCount;
        this.logger.warn(
          `⚠️  Phân tích: ${invalidCount} metadata không hợp lệ, ${failedCount} metadata lưu thất bại`,
        );
      }

      return savedCount;
    } catch (error) {
      this.logger.error(`❌ Lỗi khi lưu batch metadata: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      return 0;
    }
  }

  /**
   * Clean up metadata để đảm bảo tags không bị lưu vào content
   * @param metadata Metadata cần clean up
   * @returns Metadata đã được clean up
   */
  private cleanupMetadata(metadata: ExtractedMetadata): ExtractedMetadata {
    if (!metadata.content) {
      return metadata;
    }

    // Kiểm tra và loại bỏ tags từ cuối content nếu có
    const tagsPattern = /\n\nTags:\s*(.+)$/;
    const match = metadata.content.match(tagsPattern);

    if (match) {
      // Nếu tìm thấy tags trong content, loại bỏ chúng
      metadata.content = metadata.content.replace(tagsPattern, '').trim();

      // Nếu chưa có tags riêng biệt, sử dụng tags từ content
      if (!metadata.tags && match[1]) {
        metadata.tags = match[1].trim();
      }

      this.logger.debug(`Cleaned up metadata: removed tags from content and moved to tags field`);
    }

    return metadata;
  }

  /**
   * Kiểm tra metadata có hợp lệ để lưu vào database không
   * @param metadata Metadata cần kiểm tra
   * @returns true nếu có đủ thông tin để lưu, false nếu không
   */
  private isValidMetadata(metadata: ExtractedMetadata): boolean {
    // Kiểm tra URL có tồn tại
    const hasUrl = Boolean(
      metadata &&
      metadata.url &&
      typeof metadata.url === 'string' &&
      metadata.url.trim() !== ''
    );

    // Kiểm tra title có tồn tại và không rỗng
    const hasTitle = Boolean(
      metadata.title &&
      typeof metadata.title === 'string' &&
      metadata.title.trim() !== ''
    );

    // Kiểm tra content có tồn tại và không rỗng
    const hasContent = Boolean(
      metadata.content &&
      typeof metadata.content === 'string' &&
      metadata.content.trim() !== ''
    );

    // Chỉ lưu vào DB nếu có URL và ít nhất title HOẶC content
    const isValid = hasUrl && (hasTitle || hasContent);

    // Log kết quả kiểm tra chi tiết
    if (!isValid) {
      if (!hasUrl) {
        this.logger.warn(`❌ Invalid metadata: missing URL`);
      } else if (!hasTitle && !hasContent) {
        this.logger.warn(`⚠️ URL ${metadata.url} không có title và content - không lưu vào DB nhưng vẫn báo thành công`);
      }
    } else {
      this.logger.debug(`✅ Valid metadata for URL ${metadata.url}: hasTitle=${hasTitle}, hasContent=${hasContent}`);
    }

    return isValid;
  }

  /**
   * Xử lý batch URLs song song với smart crawling
   * @param batch Batch URLs cần xử lý
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @param sessionId ID session để kiểm tra hủy
   * @param job Bull job để kiểm tra hủy
   * @returns Array metadata đã trích xuất
   */
  private async processConcurrentUrlsWithSmartCrawling(
    batch: Array<{ url: string; depth: number }>,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
    sessionId?: string,
    job?: Job,
  ): Promise<ExtractedMetadata[]> {
    const results: ExtractedMetadata[] = [];

    // ✅ Kiểm tra hủy trước khi bắt đầu xử lý batch
    const isCancelled = await this.checkCancellation(sessionId, job);
    if (isCancelled) {
      this.logger.log(`🛑 Batch processing cancelled for session ${sessionId}`);
      return results; // Return empty results nếu bị hủy
    }

    // ✅ XỬ LÝ SONG SONG đơn giản với Promise.allSettled
    const promises = batch.map(({ url, depth }) =>
      this.processSingleUrlWithSmartCrawling(
        url,
        depth,
        userId,
        crawlDto,
        visitedUrls,
        urlsToVisit,
        errors,
        sessionId,
        job,
      ),
    );

    const settledResults = await Promise.allSettled(promises);

    // Xử lý kết quả
    settledResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      } else if (result.status === 'rejected') {
        const errorMsg = `Lỗi xử lý URL ${batch[index].url}: ${result.reason}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
      }
    });

    return results;
  }

  // ✅ Cache để giảm database queries
  private cancellationCache = new Map<string, { status: string; timestamp: number }>();
  private readonly CANCELLATION_CACHE_TTL = 5000; // 5 giây

  /**
   * ✅ Kiểm tra xem crawl có bị hủy không (với caching để giảm DB queries)
   * @param sessionId ID session
   * @param job Bull job
   * @returns true nếu bị hủy
   */
  private async checkCancellation(sessionId?: string, job?: Job): Promise<boolean> {
    // ✅ Kiểm tra cache trước
    if (sessionId) {
      const cached = this.cancellationCache.get(sessionId);
      if (cached && (Date.now() - cached.timestamp) < this.CANCELLATION_CACHE_TTL) {
        if (cached.status === 'cancelled') {
          return true;
        }
        // Nếu cache cho thấy không bị hủy và còn fresh, skip DB check
        return false;
      }

      // ✅ Kiểm tra database nếu cache miss hoặc expired
      try {
        const currentSession = await this.crawlSessionCustomRepository.findSessionById(sessionId);
        if (!currentSession || currentSession.status === 'cancelled') {
          this.logger.log(`🛑 Session ${sessionId} đã bị hủy hoặc không tồn tại`);
          // Cache kết quả
          this.cancellationCache.set(sessionId, { status: 'cancelled', timestamp: Date.now() });
          return true;
        }

        // Cache kết quả không bị hủy
        this.cancellationCache.set(sessionId, { status: currentSession.status, timestamp: Date.now() });
      } catch (error) {
        this.logger.warn(`⚠️ Không thể kiểm tra session data: ${error.message}`);
      }
    }

    // ✅ Kiểm tra job data (backup check) - ít sử dụng hơn
    if (job && job.id) {
      try {
        const currentJob = await this.crawlQueue.getJob(String(job.id));
        if (currentJob && currentJob.data.cancelled) {
          this.logger.log(`🛑 Job ${job.id} đã bị đánh dấu cancelled`);
          return true;
        }
      } catch (error) {
        this.logger.warn(`⚠️ Không thể kiểm tra job data: ${error.message}`);
      }
    }

    return false;
  }

  /**
   * ✅ Clear cancellation cache khi job hoàn thành
   */
  private clearCancellationCache(sessionId?: string): void {
    if (sessionId && this.cancellationCache.has(sessionId)) {
      this.cancellationCache.delete(sessionId);
    }
  }



  /**
   * Xử lý một URL đơn lẻ với smart crawling và metadata extraction
   * @param url URL cần xử lý
   * @param depth Độ sâu hiện tại
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @param sessionId ID session để kiểm tra hủy
   * @param job Bull job để kiểm tra hủy
   * @returns Metadata đã trích xuất hoặc null nếu thất bại
   */
  private async processSingleUrlWithSmartCrawling(
    url: string,
    depth: number,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
    sessionId?: string,
    job?: Job,
  ): Promise<ExtractedMetadata | null> {
    try {
      this.logger.log(`🧠 Smart processing URL: ${url} (độ sâu: ${depth})`);

      // ✅ Kiểm tra hủy trước khi xử lý URL
      const isCancelled = await this.checkCancellation(sessionId, job);
      if (isCancelled) {
        this.logger.log(`🛑 URL processing cancelled for ${url} in session ${sessionId}`);
        return null; // Return null nếu bị hủy
      }

      // Kiểm tra metadata cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Metadata cache hit for URL: ${normalizedUrl}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          try {
            const childUrls = await this.extractChildUrls(url);
            for (const childUrl of childUrls) {
              if (
                !visitedUrls.has(childUrl) &&
                urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
              ) {
                urlsToVisit.push({ url: childUrl, depth: depth + 1 });
              }
            }
          } catch (childError) {
            this.logger.warn(
              `Lỗi khi trích xuất child URLs từ cache cho ${url}: ${childError.message}`,
            );
          }
        }

        return cached.metadata;
      }

      // ✅ Kiểm tra hủy trước khi áp dụng rate limiting
      const isCancelledBeforeRateLimit = await this.checkCancellation(sessionId, job);
      if (isCancelledBeforeRateLimit) {
        this.logger.log(`🛑 URL processing cancelled before rate limit for ${url}`);
        return null;
      }

      // Áp dụng rate limiting trước khi crawl
      await this.waitForRateLimit(url);

      // ✅ Kiểm tra hủy sau rate limiting (có thể mất thời gian)
      const isCancelledAfterRateLimit = await this.checkCancellation(sessionId, job);
      if (isCancelledAfterRateLimit) {
        this.logger.log(`🛑 URL processing cancelled after rate limit for ${url}`);
        return null;
      }

      // Sử dụng smart crawling thay vì fetchOptimalHtml
      const crawlResult = await this.smartCrawl(url);

      // 🎯 OPTIMIZATION: Update success rate tracking
      const domain = new URL(url).hostname;
      this.updateDomainSuccessRate(domain, true);

      // Trích xuất metadata từ HTML
      const metadata = this.extractHeadMetadata(crawlResult.html, url);

      // Trích xuất metadata từ JSON-LD nếu có
      const jsonLdData = this.extractJsonLdMetadata(crawlResult.html);

      // Kết hợp metadata từ JSON-LD nếu có
      if (jsonLdData && jsonLdData.length > 0) {
        const jsonLd = jsonLdData[0];

        if (!metadata.title && jsonLd.name) {
          metadata.title = jsonLd.name;
        }

        if (!metadata.content && jsonLd.description) {
          metadata.content = jsonLd.description;
        }

        if (!metadata.tags && jsonLd.keywords) {
          if (Array.isArray(jsonLd.keywords)) {
            metadata.tags = jsonLd.keywords.join(', ');
          } else if (typeof jsonLd.keywords === 'string') {
            metadata.tags = jsonLd.keywords;
          }
        }
      }

      // Clean up metadata để đảm bảo tags không bị lưu vào content
      const cleanedMetadata = this.cleanupMetadata(metadata);

      // Cache metadata (với format mới - tags riêng biệt)
      this.metadataCache.set(normalizedUrl, {
        metadata: cleanedMetadata,
        expiry: Date.now() + this.METADATA_CACHE_TTL,
      });

      this.logger.debug(
        `Metadata extracted for ${url}: title="${cleanedMetadata.title}", content="${cleanedMetadata.content?.substring(0, 50)}..."`,
      );

      // Nếu chưa đạt độ sâu tối đa, trích xuất các URL con
      if (depth < crawlDto.depth) {
        try {
          // Sử dụng URLs từ smart crawl result nếu có
          let childUrls: string[] = [];
          if (crawlResult.urls && crawlResult.urls.length > 0) {
            childUrls = crawlResult.urls;
            this.logger.log(
              `Smart crawl extracted ${childUrls.length} child URLs from ${url}`,
            );
          } else {
            // Fallback: trích xuất từ HTML
            childUrls = await this.extractChildUrls(url);
            this.logger.log(
              `Fallback extraction: ${childUrls.length} child URLs from ${url}`,
            );
          }

          // Thêm các URL con vào hàng đợi
          for (const childUrl of childUrls) {
            if (
              !visitedUrls.has(childUrl) &&
              urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
            ) {
              urlsToVisit.push({ url: childUrl, depth: depth + 1 });
            }
          }

          this.logger.debug(
            `Added ${Math.min(childUrls.length, (crawlDto.maxUrls || 20) - urlsToVisit.length - visitedUrls.size)} child URLs to queue`,
          );
        } catch (childError) {
          this.logger.warn(
            `Lỗi khi trích xuất child URLs cho ${url}: ${childError.message}`,
          );
          errors.push(`Lỗi trích xuất child URLs: ${childError.message}`);
        }
      }

      return cleanedMetadata;
    } catch (error) {
      // 🎯 OPTIMIZATION: Update failure rate tracking
      try {
        const domain = new URL(url).hostname;
        this.updateDomainSuccessRate(domain, false);
      } catch (domainError) {
        // Ignore domain parsing errors
      }

      const errorMsg = `Lỗi xử lý URL ${url}: ${error.message}`;
      this.logger.error(errorMsg);
      errors.push(errorMsg);
      return null;
    }
  }

  /**
   * Trích xuất metadata từ thẻ head
   * @param html HTML của trang web
   * @param url URL của trang web
   * @returns Metadata đã trích xuất
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title từ nhiều nguồn
    let title = $('title').text().trim();
    if (!title) {
      title =
        $('meta[property="og:title"]').attr('content') ||
        $('meta[name="twitter:title"]').attr('content') ||
        $('meta[itemprop="name"]').attr('content') ||
        $('h1').first().text().trim() ||
        '';
    }

    // Trích xuất content (description) từ nhiều nguồn
    let content = $('meta[name="description"]').attr('content') || '';
    if (!content) {
      content =
        $('meta[property="og:description"]').attr('content') ||
        $('meta[name="twitter:description"]').attr('content') ||
        $('meta[itemprop="description"]').attr('content') ||
        $('meta[property="description"]').attr('content') ||
        '';
    }

    // Trích xuất tags (keywords) từ nhiều nguồn
    const tags =
      $('meta[name="keywords"]').attr('content') ||
      $('meta[property="article:tag"]').attr('content') ||
      $('meta[property="keywords"]').attr('content') ||
      '';

    this.logger.debug(
      `Extracted metadata from head: title="${title}", content="${content.substring(0, 50)}...", tags="${tags}"`,
    );

    return {
      url,
      title,
      content,
      tags,
    };
  }

  /**
   * Trích xuất metadata từ JSON-LD
   * @param html HTML của trang web
   * @returns Dữ liệu JSON-LD đã phân tích
   */
  private extractJsonLdMetadata(html: string): Array<Record<string, any>> {
    const $ = cheerio.load(html);
    const jsonLdScripts = $('script[type="application/ld+json"]');

    if (jsonLdScripts.length === 0) return [];

    try {
      const jsonLdData: Array<Record<string, any>> = [];
      jsonLdScripts.each((_, element) => {
        try {
          const jsonContent = $(element).html();
          if (jsonContent) {
            const parsed = JSON.parse(jsonContent);
            jsonLdData.push(parsed);
          }
        } catch (e) {
          this.logger.warn(`Failed to parse JSON-LD: ${e.message}`);
        }
      });

      this.logger.debug(`Extracted ${jsonLdData.length} JSON-LD objects`);
      return jsonLdData;
    } catch (error) {
      this.logger.warn(`Error extracting JSON-LD: ${error.message}`);
      return [];
    }
  }

  /**
   * 🎯 OPTIMIZATION: Cached website type detection
   * @param url URL cần phân tích
   * @returns Website analysis result
   */
  private async getCachedWebsiteType(url: string) {
    const domain = new URL(url).hostname;
    const now = Date.now();

    // Kiểm tra cache trước
    const cached = this.websiteAnalysisCache.get(domain);
    if (cached && cached.cachedAt + this.WEBSITE_ANALYSIS_CACHE_TTL > now) {
      this.logger.debug(`🎯 Using cached website analysis for domain: ${domain}`);
      return {
        type: cached.type,
        framework: cached.framework,
        needsBrowser: cached.needsBrowser,
        confidence: cached.confidence,
      };
    }

    // Phân tích và cache
    this.logger.log(`🔍 Analyzing website type for domain: ${domain} (first time)`);
    const websiteType = await this.advancedCrawlerService.detectWebsiteType(url);

    // Cache kết quả
    this.websiteAnalysisCache.set(domain, {
      type: websiteType.type,
      framework: websiteType.framework || 'unknown',
      needsBrowser: websiteType.needsBrowser,
      confidence: websiteType.confidence,
      cachedAt: now,
    });

    this.logger.log(`💾 Cached website analysis for domain: ${domain} - ${websiteType.type} (${websiteType.framework || 'unknown'})`);
    return websiteType;
  }

  /**
   * Smart crawling với auto-detection của loại trang web
   * @param url URL cần crawl
   * @param maxRetries Số lần retry tối đa
   * @returns HTML content và URLs
   */
  private async smartCrawl(
    url: string,
    maxRetries = 3,
  ): Promise<{
    html: string;
    urls: string[];
    metadata?: any;
  }> {
    try {
      this.logger.log(`🧠 Smart crawling: ${url}`);

      // 🎯 OPTIMIZATION: Sử dụng cached website type detection
      const websiteType = await this.getCachedWebsiteType(url);
      this.logger.log(
        `📊 Website type: ${websiteType.type} (${websiteType.framework || 'unknown'}) - Confidence: ${Math.round(websiteType.confidence * 100)}%`,
      );

      if (websiteType.needsBrowser && websiteType.confidence > 0.4) {
        // Giảm threshold từ 0.7 xuống 0.4
        // Sử dụng browser automation cho trang web phức tạp
        this.logger.log(
          `🚀 Using browser automation for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`,
        );

        const result = await this.advancedCrawlerService.crawlWithDedicatedBrowser(url, {
          waitTime: this.getOptimalWaitTime(websiteType),
          scrollToBottom: this.shouldScrollToBottom(websiteType),
          extractUrls: true,
          takeScreenshot: false,
          waitForSelector: this.getWaitSelector(websiteType),
        }, `smartCrawl_${Date.now()}`); // ✅ Pass unique jobId để tracking

        return {
          html: result.html,
          urls: result.urls,
          metadata: result.metadata,
        };
      } else {
        // Sử dụng traditional HTTP crawling
        this.logger.log(
          `📄 Using traditional HTTP crawling for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`,
        );
        const html = await this.fetchOptimalHtml(url, maxRetries);
        const urls = await this.extractChildUrlsFromHtml(html, url);

        return { html, urls };
      }
    } catch (error) {
      this.logger.warn(
        `Smart crawl failed for ${url}, falling back to traditional method: ${error.message}`,
      );

      // Fallback to traditional method
      const html = await this.fetchOptimalHtml(url, maxRetries);
      const urls = await this.extractChildUrlsFromHtml(html, url);

      return { html, urls };
    }
  }

  /**
   * Trích xuất các URL con từ một URL với smart crawling
   * @param url URL cần trích xuất
   * @returns Danh sách các URL con
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      // Sử dụng smart crawling
      const result = await this.smartCrawl(url);

      if (result.urls && result.urls.length > 0) {
        // Nếu smart crawl đã trích xuất URLs, sử dụng kết quả đó
        this.logger.log(
          `Smart crawl extracted ${result.urls.length} URLs from ${url}`,
        );
        return result.urls;
      } else {
        // Fallback: sử dụng cả normal và aggressive extraction
        this.logger.log(`Fallback: extracting URLs from HTML for ${url}`);
        const normalUrls = await this.extractChildUrlsFromHtml(
          result.html,
          url,
        );
        const aggressiveUrls = await this.extractChildUrlsAggressive(
          result.html,
          url,
        );

        // Combine và deduplicate
        const allUrls = [...new Set([...normalUrls, ...aggressiveUrls])];
        this.logger.log(
          `Combined extraction: ${normalUrls.length} normal + ${aggressiveUrls.length} aggressive = ${allUrls.length} total URLs`,
        );
        return allUrls;
      }
    } catch (error) {
      this.logger.error(`Error extracting child URLs: ${error.message}`);
      return [];
    }
  }

  // Placeholder methods - cần implement đầy đủ từ file gốc
  private async fetchOptimalHtml(url: string, maxRetries = 3): Promise<string> {
    return await this.fetchHeadWithRetry(url, maxRetries);
  }

  private async extractChildUrlsFromHtml(html: string, baseUrl: string): Promise<string[]> {
    // Placeholder - cần implement đầy đủ
    return [];
  }

  private async extractChildUrlsAggressive(html: string, baseUrl: string): Promise<string[]> {
    // Placeholder - cần implement đầy đủ
    return [];
  }

  private getOptimalWaitTime(websiteType: any): number {
    return 3000; // Placeholder
  }

  private shouldScrollToBottom(websiteType: any): boolean {
    return false; // Placeholder
  }

  private getWaitSelector(websiteType: any): string | undefined {
    return undefined; // Placeholder
  }

  // ==================== SESSION TRACKING METHODS ====================

  /**
   * Cập nhật tiến độ session trong database
   * @param sessionId ID của session
   * @param progress Thông tin tiến độ
   */
  private async updateSessionProgress(
    sessionId: string,
    progress: CrawlSession['progress']
  ): Promise<void> {
    try {
      this.logger.debug(`📊 Cập nhật progress vào database cho session ${sessionId}: ${progress.percentage}%`);
      const success = await this.crawlSessionCustomRepository.updateSessionProgress(sessionId, progress);
      if (success) {
        this.logger.debug(`✅ Đã cập nhật progress thành công cho session ${sessionId}`);
      } else {
        this.logger.warn(`⚠️ Không thể cập nhật progress cho session ${sessionId} - session có thể không tồn tại`);
      }
    } catch (error) {
      this.logger.error(`❌ Lỗi khi cập nhật progress cho session ${sessionId}: ${error.message}`);
    }
  }

  /**
   * Cập nhật kết quả session trong database
   * @param sessionId ID của session
   * @param status Trạng thái mới
   * @param result Kết quả crawl
   */
  private async updateSessionResult(
    sessionId: string,
    status: CrawlSession['status'],
    result: CrawlSession['result']
  ): Promise<void> {
    try {
      const endTime = status !== 'running' ? Date.now() : undefined;
      await this.crawlSessionCustomRepository.updateSessionResult(
        sessionId,
        status,
        result,
        endTime
      );
    } catch (error) {
      this.logger.warn(`Lỗi khi cập nhật kết quả cho session ${sessionId}: ${error.message}`);
    }
  }

  /**
   * Thêm lỗi vào session
   * @param sessionId ID của session
   * @param error Thông tin lỗi
   */
  private async addSessionError(
    sessionId: string,
    error: {
      type: string;
      message: string;
      url: string;
      timestamp: number;
    }
  ): Promise<void> {
    try {
      await this.crawlSessionCustomRepository.addSessionError(sessionId, error);
    } catch (dbError) {
      this.logger.warn(`Lỗi khi thêm error vào session ${sessionId}: ${dbError.message}`);
    }
  }
}