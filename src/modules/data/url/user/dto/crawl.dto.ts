import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsInt, Min, Max, IsBoolean, IsOptional } from 'class-validator';

export class CrawlDto {
  @ApiProperty({
    description: 'URL gốc để bắt đầu crawl',
    example: 'https://example.com',
  })
  @IsNotEmpty({ message: 'URL không được để trống' })
  @IsUrl({}, { message: 'URL không hợp lệ' })
  url: string;

  @ApiProperty({
    description: 'Độ sâu tìm kiếm URL (1-3)',
    example: 2,
    minimum: 1,
    maximum: 3,
  })
  @IsNotEmpty({ message: 'Độ sâu không được để trống' })
  @IsInt({ message: 'Độ sâu phải là số nguyên' })
  @Min(1, { message: '<PERSON><PERSON> sâu tối thiểu là 1' })
  @Max(3, { message: '<PERSON><PERSON> sâu tối đa là 3' })
  depth: number;

  @ApiProperty({
    description: 'Bỏ qua kiểm tra robots.txt (mặc định: false)',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'ignoreRobotsTxt phải là giá trị boolean' })
  ignoreRobotsTxt?: boolean;

  @ApiProperty({
    description: 'Số lượng URL tối đa để crawl (1-100, mặc định: 20)',
    example: 20,
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsInt({ message: 'maxUrls phải là số nguyên' })
  @Min(1, { message: 'maxUrls tối thiểu là 1' })
  @Max(2000, { message: 'maxUrls tối đa là 100' })
  maxUrls?: number;
}
