import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { HttpModule } from '@nestjs/axios';
import { UrlUserController } from './controllers/url-user.controller';
import { Url } from '../entities/url.entity';
import { CrawlSession } from '../entities/crawl-session.entity';
import { UrlMetadata } from './entities/url-metadata.entity';
import { UrlRepository } from '../repositories/url.repository';
import { CrawlSessionRepository } from '../repositories/crawl-session.repository';
import { UrlMetadataRepository } from './repositories/url-metadata.repository';
import { AuthModule } from '../../../../modules/auth/auth.module';
import { RedisService } from '../../../../shared/services/redis.service';
import { UrlUserService } from './services/url-user.service';
import { ServicesModule } from '../../../../shared/services/services.module';
import { ProxyRotationService } from '../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../shared/services/user-agent-rotation.service';
import { AdvancedCrawlerService } from '../shared/services/advanced-crawler.service';
import { CrawlWorker } from './services/crawl.worker';
import { QueueName } from '@shared/queue/queue.constants';
import { QueueModule } from '@shared/queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Url, UrlMetadata, CrawlSession]),
    AuthModule,
    HttpModule,
    QueueModule,
    ServicesModule
  ],
  controllers: [UrlUserController],
  providers: [
    UrlUserService,
    UrlRepository,
    UrlMetadataRepository,
    CrawlSessionRepository,
    RedisService,
    ProxyRotationService,
    UserAgentRotationService,
    AdvancedCrawlerService,
    CrawlWorker
  ],
  exports: [UrlUserService, CrawlWorker],
})
export class UrlUserModule {}
