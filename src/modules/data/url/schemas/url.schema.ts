import { ApiProperty } from '@nestjs/swagger';
import { Url } from '../entities/url.entity';

export class UrlSchema {
  @ApiProperty({
    description: 'Mã định danh tài nguyên URL',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Đường dẫn URL',
    example: 'https://example.com/article/how-to-use-nestjs',
  })
  url: string;

  @ApiProperty({
    description: 'Tiêu đề của tài nguyên URL',
    example: 'Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao',
  })
  title: string;

  @ApiProperty({
    description: 'Nội dung về tài nguyên URL',
    example: 'Bài viết này hướng dẫn cách sử dụng NestJS, một framework Node.js mạnh mẽ cho phép xây dựng các ứng dụng server-side hiệu quả...',
  })
  content: string;

  @ApiProperty({
    description: 'Loại tài nguyên URL',
    example: 'web',
    nullable: true,
  })
  type: string;

  @ApiProperty({
    description: 'Các thẻ phân loại URL',
    example: ['nestjs', 'tutorial', 'backend'],
    nullable: true,
  })
  tags: string[];

  @ApiProperty({
    description: 'Mã người sở hữu tài nguyên URL',
    example: 1,
  })
  ownedBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bản ghi (unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;

  constructor(partial: Partial<Url>) {
    Object.assign(this, partial);
  }
}

export class UrlListResponseSchema {
  @ApiProperty({
    description: 'Danh sách URL',
    type: [UrlSchema],
  })
  items: UrlSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số URL',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số URL trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số URL trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
