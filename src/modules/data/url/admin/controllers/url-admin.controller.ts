import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse as SwaggerApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ApiResponseDto as ApiResponse } from '@common/response/api-response-dto';
import { UrlSchema, UrlListResponseSchema } from '../../schemas/url.schema';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { UrlAdminService } from '../services/url-admin.service';
import { FindAllUrlAdminDto } from '../dto/find-all-url-admin.dto';
import { CreateUrlAdminDto } from '../dto/create-url-admin.dto';
import { UpdateUrlAdminDto } from '../dto/update-url-admin.dto';
import {
  CrawlAdminDto,
  StartCrawlWithTrackingAdminDto,
  StartCrawlAdminResponseDto,
  CrawlProgressAdminResponseDto,
  CrawlSessionListAdminResponseDto
} from '../dto/crawl-admin.dto';
import { DeleteUrlsAdminDto } from '../dto/delete-urls-admin.dto';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';

/**
 * Controller xử lý các endpoint liên quan đến URL cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_URL)
@ApiSecurity('JWT-auth')
@Controller('data/admin/url')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
export class UrlAdminController {
  private readonly logger = new Logger(UrlAdminController.name);

  constructor(
    private readonly urlAdminService: UrlAdminService,
  ) {}

  /**
   * Lấy danh sách URL với phân trang và tìm kiếm
   */
  @ApiOperation({ summary: 'Lấy danh sách URL với phân trang và tìm kiếm' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng bản ghi trên mỗi trang', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Trường cần sắp xếp', type: String })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC hoặc DESC)', type: String })
  @ApiQuery({ name: 'keyword', required: false, description: 'Từ khóa tìm kiếm', type: String })
  @ApiQuery({ name: 'type', required: false, description: 'Loại URL cần lọc', type: String })
  @ApiQuery({ name: 'tags', required: false, description: 'Các thẻ cần lọc (có thể dùng dấu phẩy để phân tách nhiều thẻ, ví dụ: tags=nestjs,tutorial)', type: String })
  @ApiQuery({ name: 'userId', required: false, description: 'ID người dùng sở hữu URL', type: Number })
  @ApiQuery({ name: 'isActive', required: false, description: 'Trạng thái kích hoạt', type: Boolean })
  @SwaggerApiResponse({ status: 200, description: 'Danh sách URL', type: UrlListResponseSchema })
  @Get()
  async findAll(@Query() queryParams: FindAllUrlAdminDto) {
    this.logger.log(`Finding all URLs with params: ${JSON.stringify(queryParams)}`);

    const result = await this.urlAdminService.findAllUrls(queryParams);

    this.logger.log(`Found ${result.items.length} URLs`);

    return ApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   */
  @ApiOperation({ summary: 'Lấy thông tin chi tiết URL theo ID' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Thông tin chi tiết URL', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    this.logger.log(`Finding URL with ID: ${id}`);

    const result = await this.urlAdminService.findUrlById(id);

    this.logger.log(`URL with ID: ${id} found`);

    return ApiResponse.success(result);
  }

  /**
   * Tạo URL mới
   */
  @ApiOperation({ summary: 'Tạo URL mới' })
  @SwaggerApiResponse({ status: 201, description: 'URL đã được tạo thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post()
  async create(@Body() createUrlDto: CreateUrlAdminDto) {
    this.logger.log(`Creating new URL`);
    this.logger.debug(`URL data: ${JSON.stringify(createUrlDto)}`);

    const result = await this.urlAdminService.createUrl(createUrlDto);

    this.logger.log(`URL created successfully with ID: ${result.id}`);

    return ApiResponse.created(result, 'URL đã được tạo thành công');
  }

  /**
   * Cập nhật thông tin URL
   */
  @ApiOperation({ summary: 'Cập nhật thông tin URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'URL đã được cập nhật thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUrlDto: UpdateUrlAdminDto
  ) {
    this.logger.log(`Updating URL with ID: ${id}`);
    this.logger.debug(`Update data: ${JSON.stringify(updateUrlDto)}`);

    const result = await this.urlAdminService.updateUrl(id, updateUrlDto);

    this.logger.log(`URL with ID: ${id} updated successfully`);

    return ApiResponse.success(result, 'URL đã được cập nhật thành công');
  }

  /**
   * Xóa nhiều URL
   */
  @ApiOperation({ summary: 'Xóa nhiều URL' })
  @SwaggerApiResponse({
    status: 200,
    description: 'URL đã được xóa thành công',
    examples: {
      success: {
        summary: 'Xóa thành công',
        value: {
          success: true,
          message: 'Đã xóa thành công 3 URL',
          data: null
        }
      }
    }
  })
  @SwaggerApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    examples: {
      invalidData: {
        summary: 'Dữ liệu không hợp lệ',
        value: {
          success: false,
          message: 'IDs phải là một mảng',
          errorCode: 30203
        }
      }
    }
  })
  @SwaggerApiResponse({
    status: 404,
    description: 'Một hoặc nhiều URL không tồn tại',
    examples: {
      notFound: {
        summary: 'URL không tồn tại',
        value: {
          success: false,
          message: 'Không tìm thấy URL với ID: 550e8400-e29b-41d4-a716-446655440999',
          errorCode: 30001
        }
      }
    }
  })
  @Delete('batch')
  async remove(@Body() deleteUrlsDto: DeleteUrlsAdminDto) {
    this.logger.log(`Deleting URLs with IDs: ${JSON.stringify(deleteUrlsDto.ids)}`);

    await this.urlAdminService.deleteUrls(deleteUrlsDto.ids);

    this.logger.log(`Successfully deleted ${deleteUrlsDto.ids.length} URLs`);

    return ApiResponse.success(null, `Đã xóa thành công ${deleteUrlsDto.ids.length} URL`);
  }


  /**
   * Đảo ngược trạng thái kích hoạt của URL (toggle) - Endpoint 3
   */
  @ApiOperation({ summary: 'Đảo ngược trạng thái kích hoạt của URL (toggle)' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Trạng thái URL đã được đảo ngược thành công', type: UrlSchema })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Post(':id/toggle')
  async toggleStatusPost(
    @Param('id') id: string
  ) {
    this.logger.log(`Toggling URL status with ID: ${id} (POST method)`);

    const result = await this.urlAdminService.toggleUrlStatus(id);

    this.logger.log(`URL status with ID: ${id} toggled successfully to: ${result.isActive}`);

    return ApiResponse.success(result, `Trạng thái URL đã được đảo ngược thành công. Trạng thái mới: ${result.isActive ? 'Kích hoạt' : 'Vô hiệu hóa'}`);
  }


  /**
   * Bắt đầu crawl với session tracking cho admin
   */
  @ApiOperation({ summary: 'Bắt đầu crawl với session tracking cho admin' })
  @SwaggerApiResponse({
    status: 200,
    description: 'Bắt đầu crawl thành công',
    type: StartCrawlAdminResponseDto
  })
  @SwaggerApiResponse({ status: 400, description: 'Lỗi khi bắt đầu crawl' })
  @Post('crawl/start')
  async startCrawlWithTracking(
    @CurrentEmployee('id') employeeId: number,
    @Body() crawlDto: StartCrawlWithTrackingAdminDto
  ) {
    this.logger.log(`Admin starting crawl with tracking: ${crawlDto.url} with depth: ${crawlDto.depth}`);

    const result = await this.urlAdminService.startCrawlWithTracking(employeeId, crawlDto);

    this.logger.log(`Admin crawl session started: ${result.sessionId}`);
    return ApiResponse.success(result, result.message);
  }

  /**
   * Lấy tiến độ crawl theo session ID cho admin
   */
  @ApiOperation({ summary: 'Lấy tiến độ crawl theo session ID cho admin' })
  @ApiParam({ name: 'sessionId', description: 'ID của session crawl', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'Thông tin tiến độ crawl',
    type: CrawlProgressAdminResponseDto
  })
  @SwaggerApiResponse({ status: 404, description: 'Session không tồn tại' })
  @Get('crawl/progress/:sessionId')
  async getCrawlProgress(
    @CurrentEmployee('id') employeeId: number,
    @Param('sessionId') sessionId: string
  ) {
    this.logger.log(`Admin getting crawl progress for session: ${sessionId}`);

    const result = await this.urlAdminService.getCrawlProgress(employeeId, sessionId);

    this.logger.log(`Admin crawl progress retrieved for session: ${sessionId}, status: ${result.status}`);
    return ApiResponse.success(result, 'Lấy tiến độ crawl thành công');
  }

  /**
   * Lấy danh sách sessions crawl của admin
   */
  @ApiOperation({ summary: 'Lấy danh sách sessions crawl của admin' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng items mỗi trang', type: Number })
  @ApiQuery({ name: 'status', required: false, description: 'Lọc theo trạng thái', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'Danh sách sessions crawl',
    type: CrawlSessionListAdminResponseDto
  })
  @Get('crawl/sessions')
  async getAdminCrawlSessions(
    @CurrentEmployee('id') employeeId: number,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string
  ) {
    this.logger.log(`Admin getting crawl sessions, page: ${page}, limit: ${limit}, status: ${status}`);

    const result = await this.urlAdminService.getAdminCrawlSessions(
      employeeId,
      page || 1,
      limit || 20,
      status
    );

    this.logger.log(`Admin found ${result.sessions.length} crawl sessions`);
    return ApiResponse.success(result, 'Lấy danh sách sessions thành công');
  }

  /**
   * Hủy session crawl đang chạy cho admin
   */
  @ApiOperation({ summary: 'Hủy session crawl đang chạy cho admin' })
  @ApiParam({ name: 'sessionId', description: 'ID của session crawl', type: String })
  @SwaggerApiResponse({ status: 200, description: 'Hủy session thành công' })
  @SwaggerApiResponse({ status: 404, description: 'Session không tồn tại' })
  @SwaggerApiResponse({ status: 400, description: 'Session không thể hủy' })
  @Delete('crawl/sessions/:sessionId')
  async cancelCrawlSession(
    @CurrentEmployee('id') employeeId: number,
    @Param('sessionId') sessionId: string
  ) {
    this.logger.log(`Admin cancelling crawl session: ${sessionId}`);

    const result = await this.urlAdminService.cancelCrawlSession(employeeId, sessionId);

    this.logger.log(`Admin crawl session cancelled: ${sessionId}`);
    return ApiResponse.success({ cancelled: result }, 'Đã hủy session thành công');
  }
}
