import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { HttpModule } from '@nestjs/axios';
import { UrlAdminController } from './controllers/url-admin.controller';
import { UrlAdminService } from './services/url-admin.service';
import { CrawlAdminWorker } from './services/crawl-admin.worker';
import { Url } from '../entities/url.entity';
import { CrawlSession } from '../entities/crawl-session.entity';
import { UrlRepository } from '../repositories';
import { CrawlSessionRepository } from '../repositories/crawl-session.repository';
import { AuthModule } from '@modules/auth/auth.module';
import { ProxyRotationService } from '../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../shared/services/user-agent-rotation.service';
import { AdminAdvancedCrawlerService } from './services/admin-advanced-crawler.service';
import { RedisService } from '../../../../shared/services/redis.service';
import { QueueName } from '@shared/queue/queue.constants';
import { QueueModule } from '@shared/queue/queue.module';
import { ServicesModule } from '@shared/services/services.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Url, CrawlSession]),
    HttpModule,
    AuthModule,
    QueueModule,
    ServicesModule
  ],
  controllers: [UrlAdminController],
  providers: [
    UrlAdminService,
    CrawlAdminWorker,
    UrlRepository,
    CrawlSessionRepository,
    RedisService,
    ProxyRotationService,
    UserAgentRotationService,
    AdminAdvancedCrawlerService
  ],
  exports: [UrlAdminService, AdminAdvancedCrawlerService, CrawlAdminWorker],
})
export class UrlAdminModule {}
