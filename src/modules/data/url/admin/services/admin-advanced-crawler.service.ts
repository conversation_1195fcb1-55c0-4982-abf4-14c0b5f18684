import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import * as puppeteer from 'puppeteer';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

/**
 * Admin Advanced Crawler Service sử dụng Puppeteer để crawl các trang web phức tạp
 * Service riêng cho admin để tránh xung đột browser với user
 * Hỗ trợ JavaScript, React, SPA, và dynamic content
 */
@Injectable()
export class AdminAdvancedCrawlerService {
  private readonly logger = new Logger(AdminAdvancedCrawlerService.name);
  private browser: Browser | null = null;
  private readonly MAX_CONCURRENT_PAGES = 5;
  private activePagesCount = 0;

  constructor(private readonly httpService: HttpService) {}

  /**
   * Khởi tạo browser instance cho admin
   */
  async initBrowser(): Promise<void> {
    if (!this.browser) {
      this.logger.log('🚀 [ADMIN] Initializing Puppeteer browser...');

      this.browser = await puppeteer.launch({
        headless: true, // Chạy ẩn
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ],
        defaultViewport: {
          width: 1366,
          height: 768
        }
      });

      this.logger.log('✅ [ADMIN] Puppeteer browser initialized successfully');
    }
  }

  /**
   * Đóng browser admin
   */
  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.logger.log('🔒 [ADMIN] Browser closed');
    }
  }

  /**
   * Phát hiện loại trang web
   * @param url URL cần kiểm tra
   * @returns Loại trang web
   */
  async detectWebsiteType(url: string): Promise<{
    type: 'static' | 'spa' | 'dynamic' | 'complex';
    framework?: string;
    needsBrowser: boolean;
    confidence: number;
  }> {
    try {
      this.logger.log(`🔍 [ADMIN] Detecting website type for: ${url}`);

      // Kiểm tra URL patterns
      const urlLower = url.toLowerCase();

      // SPA frameworks patterns
      if (urlLower.includes('/#/') || urlLower.includes('#!')) {
        this.logger.log(`📱 [ADMIN] SPA pattern detected in URL: ${url}`);
        return { type: 'spa', needsBrowser: true, confidence: 0.9 };
      }

      // E-commerce patterns
      if (urlLower.includes('shopee.') || urlLower.includes('lazada.') || 
          urlLower.includes('tiki.') || urlLower.includes('sendo.')) {
        this.logger.log(`🛒 [ADMIN] E-commerce pattern detected: ${url}`);
        return { type: 'complex', needsBrowser: true, confidence: 0.95 };
      }

      // Fetch HTML để phân tích
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      const html = await response.text();
      this.logger.log(`📄 [ADMIN] HTML fetched successfully, length: ${html.length} chars`);

      // Phân tích HTML content
      const analysis = this.analyzeHtmlContent(html);
      this.logger.log(`🧠 [ADMIN] Analysis complete: type=${analysis.type}, framework=${analysis.framework}, needsBrowser=${analysis.needsBrowser}, confidence=${Math.round(analysis.confidence * 100)}%`);

      return analysis;
    } catch (error) {
      this.logger.error(`❌ [ADMIN] Error detecting website type for ${url}: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      return { type: 'static', needsBrowser: false, confidence: 0.5 };
    }
  }

  /**
   * Phân tích HTML content để xác định loại trang web
   */
  private analyzeHtmlContent(html: string): {
    type: 'static' | 'spa' | 'dynamic' | 'complex';
    framework?: string;
    needsBrowser: boolean;
    confidence: number;
  } {
    let confidence = 0;
    let type: 'static' | 'spa' | 'dynamic' | 'complex' = 'static';
    let framework: string | undefined;
    let needsBrowser = false;

    // Check for SPA frameworks
    if (html.includes('ng-app') || html.includes('ng-controller') || html.includes('angular')) {
      type = 'spa';
      framework = 'Angular';
      needsBrowser = true;
      confidence = 0.9;
    } else if (html.includes('data-reactroot') || html.includes('__REACT_DEVTOOLS_GLOBAL_HOOK__') || html.includes('react')) {
      type = 'spa';
      framework = 'React';
      needsBrowser = true;
      confidence = 0.9;
    } else if (html.includes('__NUXT__') || html.includes('nuxt')) {
      type = 'spa';
      framework = 'Nuxt.js';
      needsBrowser = true;
      confidence = 0.9;
    } else if (html.includes('__NEXT_DATA__') || html.includes('next')) {
      type = 'spa';
      framework = 'Next.js';
      needsBrowser = true;
      confidence = 0.9;
    } else if (html.includes('vue') || html.includes('Vue')) {
      type = 'spa';
      framework = 'Vue.js';
      needsBrowser = true;
      confidence = 0.85;
    }

    // Check for dynamic content indicators
    const dynamicIndicators = [
      'document.addEventListener',
      'window.onload',
      'setTimeout',
      'setInterval',
      'fetch(',
      'XMLHttpRequest',
      'ajax',
      'async',
      'await'
    ];

    let dynamicScore = 0;
    dynamicIndicators.forEach(indicator => {
      if (html.includes(indicator)) {
        dynamicScore += 0.1;
      }
    });

    if (dynamicScore > 0.3 && type === 'static') {
      type = 'dynamic';
      needsBrowser = true;
      confidence = Math.min(0.8, dynamicScore);
    }

    // Check for complex e-commerce patterns
    const ecommerceIndicators = [
      'add-to-cart',
      'shopping-cart',
      'product-list',
      'price',
      'buy-now',
      'checkout',
      'shopee',
      'lazada',
      'tiki'
    ];

    let ecommerceScore = 0;
    ecommerceIndicators.forEach(indicator => {
      if (html.toLowerCase().includes(indicator)) {
        ecommerceScore += 0.15;
      }
    });

    if (ecommerceScore > 0.4) {
      type = 'complex';
      needsBrowser = true;
      confidence = Math.min(0.95, ecommerceScore + 0.3);
    }

    // If no dynamic content detected, it's likely static
    if (type === 'static') {
      confidence = 0.7;
      needsBrowser = false;
    }

    return { type, framework, needsBrowser, confidence };
  }

  /**
   * Crawl trang web với Puppeteer - Enhanced for anti-bot websites (Admin)
   * @param url URL cần crawl
   * @param options Tùy chọn crawl
   * @returns HTML content và URLs
   */
  async crawlWithBrowser(url: string, options: {
    waitForSelector?: string;
    waitTime?: number;
    scrollToBottom?: boolean;
    extractUrls?: boolean;
    takeScreenshot?: boolean;
  } = {}): Promise<{
    html: string;
    urls: string[];
    metadata: any;
    screenshot?: Buffer;
  }> {
    if (this.activePagesCount >= this.MAX_CONCURRENT_PAGES) {
      throw new Error('Too many concurrent pages. Please try again later.');
    }

    await this.initBrowser();

    const page = await this.browser!.newPage();
    this.activePagesCount++;

    try {
      this.logger.log(`🌐 [ADMIN] Crawling with browser: ${url}`);

      // Enhanced stealth configuration for anti-bot websites
      await this.setupStealthMode(page);

      // Navigate to page với retry logic
      await this.navigateWithRetry(page, url);

      // Wait for specific selector if provided với timeout dài hơn
      if (options.waitForSelector) {
        try {
          await page.waitForSelector(options.waitForSelector, { timeout: 20000 });
        } catch (e) {
          this.logger.warn(`[ADMIN] Waiting for selector \`${options.waitForSelector}\` failed: ${e.message}, continuing anyway...`);
        }
      }

      // Enhanced wait strategy for dynamic content
      await this.waitForDynamicContent(page, options.waitTime);

      // Scroll to bottom to trigger lazy loading
      if (options.scrollToBottom) {
        await this.autoScroll(page);
      }

      // Get page content
      const html = await page.content();
      this.logger.log(`📄 [ADMIN] Page content extracted: ${html.length} characters`);

      // Extract URLs if requested với enhanced extraction
      let urls: string[] = [];
      if (options.extractUrls) {
        this.logger.log(`🔗 [ADMIN] Starting enhanced URL extraction from page...`);
        urls = await this.extractUrlsFromPageEnhanced(page, url);
        this.logger.log(`🔗 [ADMIN] URL extraction completed: ${urls.length} URLs found`);

        if (urls.length > 0) {
          this.logger.log(`📋 [ADMIN] First 5 URLs found: ${urls.slice(0, 5).join(', ')}`);
        } else {
          this.logger.warn(`⚠️ [ADMIN] No URLs extracted from page - trying fallback methods...`);
          // Fallback URL extraction from HTML
          urls = await this.extractUrlsFromHtmlFallback(html, url);
          this.logger.log(`🔄 [ADMIN] Fallback extraction found: ${urls.length} URLs`);
        }
      }

      // Extract metadata
      const metadata = await this.extractMetadata(page);

      // Take screenshot if requested
      let screenshot: Buffer | undefined;
      if (options.takeScreenshot) {
        screenshot = await page.screenshot({ fullPage: true }) as Buffer;
        this.logger.log(`📸 [ADMIN] Screenshot captured`);
      }

      return {
        html,
        urls,
        metadata,
        screenshot
      };

    } finally {
      await page.close();
      this.activePagesCount--;
      this.logger.log(`🔒 [ADMIN] Page closed, active pages: ${this.activePagesCount}`);
    }
  }

  /**
   * Enhanced stealth mode setup for anti-bot websites
   */
  private async setupStealthMode(page: Page): Promise<void> {
    // Set realistic user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // Set viewport
    await page.setViewport({ width: 1366, height: 768 });

    // Block unnecessary resources để tăng tốc và giảm detection
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    // Override navigator properties để bypass detection
    await page.evaluateOnNewDocument(() => {
      // Override the `plugins` property to use a custom getter.
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // Override the `languages` property to use a custom getter.
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // Override the `webdriver` property to remove it entirely.
      delete (navigator as any).webdriver;
    });

    // Set extra headers
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Upgrade-Insecure-Requests': '1',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    });
  }

  /**
   * Navigate với retry logic cho admin
   */
  private async navigateWithRetry(page: Page, url: string, maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`🌐 [ADMIN] Navigation attempt ${attempt}/${maxRetries} for: ${url}`);

        await page.goto(url, {
          waitUntil: 'domcontentloaded', // Thay đổi từ 'networkidle0' để tăng tốc
          timeout: 30000 // Tăng timeout lên 30s
        });

        this.logger.log(`✅ [ADMIN] Successfully navigated to: ${url}`);
        return;
      } catch (error) {
        this.logger.warn(`⚠️ [ADMIN] Navigation attempt ${attempt} failed for ${url}: ${error.message}`);

        if (attempt === maxRetries) {
          throw new Error(`Failed to navigate to ${url} after ${maxRetries} attempts: ${error.message}`);
        }

        // Wait before retry với exponential backoff
        await new Promise(resolve => setTimeout(resolve, attempt * 2000));
      }
    }
  }

  /**
   * Enhanced wait strategy for dynamic content - Special handling for complex sites
   */
  private async waitForDynamicContent(page: Page, customWaitTime?: number): Promise<void> {
    // Wait for initial load
    await new Promise(resolve => setTimeout(resolve, 3000)); // Tăng từ 2s lên 3s

    // Custom wait time if provided
    if (customWaitTime) {
      await new Promise(resolve => setTimeout(resolve, customWaitTime));
    }

    // Wait for common dynamic content indicators với timeout dài hơn
    const dynamicSelectors = [
      // E-commerce specific selectors
      '.shopee-header', '.navbar', '.navigation', '.header',
      '.product-list', '.category-list', '.main-content',
      // General selectors
      'main', '.content', '.container', '.wrapper',
      '#app', '#root', '[data-testid]'
    ];

    for (const selector of dynamicSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 }); // Tăng timeout lên 5s
        this.logger.log(`✅ [ADMIN] Found dynamic content selector: ${selector}`);
        break; // Nếu tìm thấy một selector, thoát khỏi loop
      } catch (e) {
        // Continue to next selector
      }
    }

    // Wait for network to be mostly idle với timeout ngắn hơn
    try {
      await page.waitForFunction(() => document.readyState === 'complete', { timeout: 10000 });
    } catch (e) {
      this.logger.warn(`[ADMIN] Network idle timeout, continuing anyway: ${e.message}`);
    }

    // Additional wait for JavaScript execution
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Auto scroll để trigger lazy loading
   */
  private async autoScroll(page: Page): Promise<void> {
    await page.evaluate(async () => {
      await new Promise<void>((resolve) => {
        let totalHeight = 0;
        const distance = 100;
        const maxScrolls = 50; // Giới hạn số lần scroll
        let scrollCount = 0;

        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;
          scrollCount++;

          if (totalHeight >= scrollHeight || scrollCount >= maxScrolls) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    });
  }

  /**
   * Extract metadata from page
   */
  private async extractMetadata(page: Page): Promise<any> {
    return await page.evaluate(() => {
      const metadata: any = {};

      // Title
      metadata.title = document.title || '';

      // Meta tags
      const metaTags = document.querySelectorAll('meta');
      metaTags.forEach(tag => {
        const name = tag.getAttribute('name') || tag.getAttribute('property');
        const content = tag.getAttribute('content');
        if (name && content) {
          metadata[name] = content;
        }
      });

      // Structured data
      const scripts = document.querySelectorAll('script[type="application/ld+json"]');
      const structuredData: any[] = [];
      scripts.forEach(script => {
        try {
          const data = JSON.parse(script.textContent || '');
          structuredData.push(data);
        } catch (e) {
          // Invalid JSON
        }
      });
      metadata.structuredData = structuredData;

      return metadata;
    });
  }

  /**
   * Fallback URL extraction from HTML
   */
  private async extractUrlsFromHtmlFallback(html: string, baseUrl: string): Promise<string[]> {
    const urls = new Set<string>();
    const base = new URL(baseUrl);

    // Regular expressions để tìm URLs
    const patterns = [
      /href=["']([^"']+)["']/gi,
      /src=["']([^"']+)["']/gi,
      /url\(["']?([^"')]+)["']?\)/gi,
      /"(https?:\/\/[^"]+)"/gi,
      /'(https?:\/\/[^']+)'/gi
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        const foundUrl = match[1];
        if (foundUrl && (foundUrl.startsWith('http://') || foundUrl.startsWith('https://') || foundUrl.startsWith('/'))) {
          try {
            const url = new URL(foundUrl, baseUrl);
            if (url.hostname === base.hostname) {
              urls.add(url.href);
            }
          } catch (e) {
            // Invalid URL
          }
        }
      }
    });

    return Array.from(urls);
  }

  /**
   * Enhanced URL extraction from page với multiple strategies - Optimized for SPA/Angular (Admin)
   */
  private async extractUrlsFromPageEnhanced(page: Page, baseUrl: string): Promise<string[]> {
    try {
      // Wait a bit more for dynamic content to load
      await new Promise(resolve => setTimeout(resolve, 2000));

      return await page.evaluate((baseUrl) => {
        const links: string[] = [];
        const base = new URL(baseUrl);

        // Strategy 1: Standard anchor tags
        const anchors = document.querySelectorAll('a[href]');
        anchors.forEach(anchor => {
          const href = anchor.getAttribute('href');
          if (href) {
            try {
              const url = new URL(href, baseUrl);
              if (url.hostname === base.hostname) {
                links.push(url.href);
              }
            } catch (e) {
              // Invalid URL
            }
          }
        });

        // Strategy 2: Links in data attributes (common in SPAs)
        const dataLinks = document.querySelectorAll('[data-href], [data-url], [data-link]');
        dataLinks.forEach(element => {
          const href = element.getAttribute('data-href') ||
                      element.getAttribute('data-url') ||
                      element.getAttribute('data-link');
          if (href) {
            try {
              const url = new URL(href, baseUrl);
              if (url.hostname === base.hostname) {
                links.push(url.href);
              }
            } catch (e) {
              // Invalid URL
            }
          }
        });

        // Strategy 3: Router links (Angular, React Router)
        const routerLinks = document.querySelectorAll('[routerLink], [to], [href^="/"]');
        routerLinks.forEach(element => {
          const href = element.getAttribute('routerLink') ||
                      element.getAttribute('to') ||
                      element.getAttribute('href');
          if (href && href.startsWith('/')) {
            try {
              const url = new URL(href, baseUrl);
              links.push(url.href);
            } catch (e) {
              // Invalid URL
            }
          }
        });

        // Strategy 4: Extract from onclick handlers và JavaScript
        const clickableElements = document.querySelectorAll('[onclick], [data-click]');
        clickableElements.forEach(element => {
          const onclick = element.getAttribute('onclick') || element.getAttribute('data-click');
          if (onclick) {
            // Extract URLs from JavaScript code
            const urlMatches = onclick.match(/['"`](\/[^'"`]*|https?:\/\/[^'"`]*)['"`]/g);
            if (urlMatches) {
              urlMatches.forEach(match => {
                const cleanUrl = match.replace(/['"`]/g, '');
                try {
                  const url = new URL(cleanUrl, baseUrl);
                  if (url.hostname === base.hostname) {
                    links.push(url.href);
                  }
                } catch (e) {
                  // Invalid URL
                }
              });
            }
          }
        });

        // Strategy 5: Extract from window object và global variables
        try {
          // Check for common SPA routing data
          const windowObj = window as any;
          if (windowObj.__NEXT_DATA__?.props?.pageProps) {
            // Next.js routing data
            const pageProps = windowObj.__NEXT_DATA__.props.pageProps;
            // Extract any URL-like properties
          }

          if (windowObj.__NUXT__) {
            // Nuxt.js routing data
          }

          // Check for Angular routing
          if (windowObj.ng) {
            // Angular routing data
          }
        } catch (e) {
          // Ignore errors accessing window object
        }

        return [...new Set(links)]; // Remove duplicates
      }, baseUrl);

    } catch (error) {
      this.logger.warn(`[ADMIN] Enhanced URL extraction failed: ${error.message}`);
      return [];
    }
  }
}
