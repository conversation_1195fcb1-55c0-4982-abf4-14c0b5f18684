import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { TestUploadService } from '../services/test-upload.service';
import { MediaUploadUrlDto, PresignedUrlResponseDto } from '../dto/media-upload-url.dto';
import { ApiResponseDto } from '@/common/response';

@ApiTags('Test')
@Controller('test')
export class TestUploadController {
  constructor(private readonly testUploadService: TestUploadService) {}

  /**
   * Tạo URL tạm thời để upload tài nguyên
   */
  @Post('upload-url')
  @ApiOperation({ summary: 'Tạo URL tạm thời để upload tài nguyên' })
  @ApiBody({ type: MediaUploadUrlDto })
  @ApiResponse({
    status: 200,
    description: 'URL tạm thời được tạo thành công',
    schema: ApiResponseDto.getSchema(PresignedUrlResponseDto),
  })
  async createUploadUrl(
    @Body() dto: MediaUploadUrlDto,
  ): Promise<ApiResponseDto<PresignedUrlResponseDto>> {
    const result = await this.testUploadService.createMediaUploadUrl(dto);
    return ApiResponseDto.success(result, 'URL tạm thời được tạo thành công');
  }
}
