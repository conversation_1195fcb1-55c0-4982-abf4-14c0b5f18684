import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@/shared/services/s3.service';
import {
  CategoryFolderEnum,
  generateS3Key,
  MediaType,
  TimeIntervalEnum,
} from '@/shared/utils';
import { MediaUploadUrlDto, PresignedUrlResponseDto } from '../dto/media-upload-url.dto';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class TestUploadService {
  private readonly logger = new Logger(TestUploadService.name);

  constructor(private readonly s3Service: S3Service) {}

  /**
   * Tạo URL tạm thời để upload tài nguyên media
   * @param dto Thông tin về loại media và kích thước file
   * @returns Thông tin URL tạm thời và key
   */
  async createMediaUploadUrl(
    dto: MediaUploadUrlDto,
  ): Promise<PresignedUrlResponseDto> {
    try {
      // <PERSON><PERSON>c định loại media và thư mục phù hợp
      let mediaType: MediaType;
      let categoryFolder: CategoryFolderEnum;

      // Xác định loại media từ MIME type
      if (dto.mediaType.startsWith('image/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.IMAGE;
      } else if (dto.mediaType.startsWith('video/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.VIDEO;
      } else if (dto.mediaType.startsWith('application/') || dto.mediaType.startsWith('text/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.DOCUMENT;
      } else {
        throw new AppException(
          ErrorCode.FILE_TYPE_NOT_FOUND,
          `Loại media không được hỗ trợ: ${dto.mediaType}`,
        );
      }

      // Tạo tên file nếu không được cung cấp
      const fileName = dto.fileName || `file-${Date.now()}`;

      // Tạo S3 key cho file
      const key = generateS3Key({
        baseFolder: 'test',
        categoryFolder: categoryFolder,
        fileName: fileName,
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const uploadUrl = await this.s3Service.createPresignedWithID(
        key,
        expirationTime,
        mediaType,
        dto.fileSize,
      );

      // Tính thời gian hết hạn
      const expiresAt = Date.now() + expirationTime * 1000;

      return {
        uploadUrl,
        key,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Không thể tạo URL upload: ${error.message}`,
      );
    }
  }
}
