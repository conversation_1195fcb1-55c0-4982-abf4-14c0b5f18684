# Cấu hình CORS

File này chứa cấu hình CORS (Cross-Origin Resource Sharing) cho ứng dụng NestJS.

## Cách sử dụng

### 1. Sử dụng trong main.ts

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { corsConfig } from './common/filters/cors.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Áp dụng cấu hình CORS
  app.enableCors(corsConfig);
  
  await app.listen(3000);
}
bootstrap();
```

### 2. Sử dụng cấu hình động

Nếu bạn muốn kiểm soát chi tiết hơn, bạn có thể sử dụng cấu hình động:

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { dynamicCorsConfig } from './common/filters/cors.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Áp dụng cấu hình CORS động
  app.enableCors(dynamicCorsConfig);
  
  await app.listen(3000);
}
bootstrap();
```

### 3. Thêm origin mới

Để thêm origin mới, bạn cần cập nhật cả hai mảng `origin` trong `corsConfig` và `allowedOrigins` trong `corsOriginCallback`.

## Các origin được phép

Hiện tại, các origin sau được phép truy cập API:

- http://localhost:5173
- http://localhost:5174
- http://localhost:5175

## Cấu hình chi tiết

- **credentials**: `true` - Cho phép gửi cookies và headers xác thực
- **methods**: Các phương thức HTTP được phép
- **allowedHeaders**: Các header được phép trong request
- **exposedHeaders**: Các header được phép trong response
- **maxAge**: Thời gian cache preflight request (3600 giây = 1 giờ)
