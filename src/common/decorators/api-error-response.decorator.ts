import { applyDecorators } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { ErrorCode } from '@common/exceptions';
import { ApiErrorResponseDto } from '@dto/api-error-response.dto';

/**
 * Decorator để tạo API Response cho lỗi dựa trên ErrorCode
 * @param errorCodes Danh sách các ErrorCode cần tạo response
 * @returns Decorator tích hợp với Swagger
 */
export function ApiErrorResponse(...errorCodes: ErrorCode[]) {
  const decorators = errorCodes.map((errorCode) => {
    return ApiResponse({
      status: errorCode.status,
      description: errorCode.message,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiErrorResponseDto) },
          {
            properties: {
              code: {
                type: 'number',
                example: errorCode.code,
              },
              message: {
                type: 'string',
                example: errorCode.message,
              },
              detail: {
                type: 'object',
                nullable: true,
                example: null,
              },
            },
          },
        ],
      },
    });
  });

  return applyDecorators(...decorators);
}

/**
 * Decorator để tạo API Response cho nhiều lỗi cùng HTTP status code
 * @param status HTTP status code
 * @param errorCodes Danh sách các ErrorCode cùng status code
 * @returns Decorator tích hợp với Swagger
 */
export function ApiMultipleErrorResponses(
  status: number,
  errorCodes: ErrorCode[],
) {
  const examples = {};

  errorCodes.forEach((errorCode, index) => {
    examples[`example${index + 1}`] = {
      value: {
        code: errorCode.code,
        message: errorCode.message,
        detail: null,
      },
      summary: `Lỗi ${errorCode.code}: ${errorCode.message}`,
    };
  });

  return ApiResponse({
    status,
    content: {
      'application/json': {
        schema: { $ref: getSchemaPath(ApiErrorResponseDto) },
        examples,
      },
    },
  });
}
