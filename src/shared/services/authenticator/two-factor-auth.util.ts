import { Injectable } from '@nestjs/common';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';

@Injectable()
export class TwoFactorAuthUtil {
  /**
   * Tạo secret key mới cho 2FA.
   * Speakeasy tạo ra một đối tượng chứa nhiều định dạng secret,
   * chúng ta sẽ trả về dạng base32 là phổ biến nhất cho các ứng dụng authenticator.
   * @returns {string} Secret key dưới dạng base32.
   */
  createSecretKey(): string {
    const secret = speakeasy.generateSecret({
      length: 20, // Độ dài của secret key
      name: 'YourAppName', // Tên ứng dụng sẽ hiển thị trong Authenticator App (tùy chọn)
    });
    // Trả về secret ở định dạng base32
    return secret.base32;
  }

  /**
   * <PERSON><PERSON><PERSON> thực mã TOTP (token) do người dùng nhập.
   * @param {string} secretKey - Secret key (base32) đã lưu của người dùng.
   * @param {string} token - Mã TOTP gồm 6 chữ số do người dùng nhập từ ứng dụng authenticator.
   * @returns {boolean} True nếu mã hợp lệ, False nếu không.
   */
  verifyToken(secretKey: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret: secretKey,
      encoding: 'base32',
      token: token,
      window: 1, // Cho phép sai lệch thời gian 1 khoảng (1 * 30 giây = 30 giây) về quá khứ hoặc tương lai
    });
  }

  /**
   * Tạo URL otpauth:// để hiển thị mã QR cho các ứng dụng authenticator.
   * URL này chứa thông tin cần thiết để ứng dụng authenticator thêm tài khoản.
   * @param {string} secretKey - Secret key (base32).
   * @param {string} accountName - Tên tài khoản (thường là email hoặc username) để hiển thị trong ứng dụng authenticator.
   * @param {string} issuer - Tên nhà phát hành (tên ứng dụng/dịch vụ của bạn).
   * @returns {string} URL otpauth://.
   */
  generateOtpAuthUrl(secretKey: string, accountName: string, issuer: string): string {
    // Đảm bảo accountName không chứa ký tự đặc biệt ảnh hưởng đến URL
    const encodedAccountName = encodeURIComponent(accountName);
    const label = `${issuer}:${encodedAccountName}`;

    return speakeasy.otpauthURL({
      secret: secretKey,
      encoding: 'base32',
      label: label, // Định dạng label chuẩn: Issuer:AccountName
      issuer: issuer,
      algorithm: 'sha1', // Thuật toán phổ biến, có thể đổi sang SHA256, SHA512 nếu cần
    });
  }

  /**
   * (Tùy chọn) Tạo mã QR dưới dạng Data URI từ URL otpauth.
   * Data URI này có thể được nhúng trực tiếp vào thuộc tính `src` của thẻ <img> trong HTML.
   * @param {string} otpAuthUrl - URL otpauth:// được tạo từ hàm generateOtpAuthUrl.
   * @returns {Promise<string>} Một Promise trả về Data URI của mã QR (ví dụ: "data:image/png;base64,...").
   */
  async generateQrCodeDataUri(otpAuthUrl: string): Promise<string> {
    try {
      const dataUrl = await qrcode.toDataURL(otpAuthUrl);
      return dataUrl;
    } catch (err) {
      console.error('Error generating QR code:', err);
      throw new Error('Could not generate QR code data URI.');
    }
  }
} 