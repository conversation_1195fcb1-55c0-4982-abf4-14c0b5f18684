# Telegram Integration Services

Module này cung cấp các service để tích hợp với Telegram Bot API, cho phép tạo và quản lý bot Telegram, x<PERSON> lý webhook, và kết nối bot với agent trong hệ thống.

## Cài đặt

### C<PERSON>u hình môi trường

Thêm các biến môi trường sau vào file `.env`:

```env
# Telegram Bot API
TELEGRAM_WEBHOOK_BASE_URL=https://your-domain.com/api/telegram
TELEGRAM_WEBHOOK_SECRET_TOKEN=your_secret_token
```

### Đăng ký module

Module đã được đăng ký trong `ServicesModule`, nên bạn có thể sử dụng các service của nó trong bất kỳ module nào đã import `ServicesModule`.

## Các service

### TelegramService

Service cơ bản cung cấp các phương thức để tương tác với Telegram Bot API.

```typescript
// Ví dụ sử dụng
import { TelegramService } from '@shared/services/telegram';

@Injectable()
export class YourService {
  constructor(private readonly telegramService: TelegramService) {}

  async sendMessage(botToken: string, chatId: number, text: string) {
    return await this.telegramService.sendMessage(botToken, chatId, text);
  }
}
```

### TelegramBotService

Service cung cấp các phương thức để quản lý bot Telegram.

```typescript
// Ví dụ sử dụng
import { TelegramBotService } from '@shared/services/telegram';

@Injectable()
export class YourService {
  constructor(private readonly telegramBotService: TelegramBotService) {}

  async verifyBot(botToken: string) {
    return await this.telegramBotService.verifyBotToken(botToken);
  }

  async setupWebhook(botToken: string, botId: number) {
    return await this.telegramBotService.setupWebhook(botToken, botId);
  }
}
```

### TelegramWebhookService

Service xử lý các webhook từ Telegram.

```typescript
// Ví dụ sử dụng
import { TelegramWebhookService } from '@shared/services/telegram';

@Controller('telegram/webhook')
export class TelegramWebhookController {
  constructor(private readonly telegramWebhookService: TelegramWebhookService) {}

  @Post(':botId')
  async handleWebhook(
    @Param('botId') botId: number,
    @Headers('X-Telegram-Bot-Api-Secret-Token') secretToken: string,
    @Body() update: TelegramUpdate
  ) {
    // Xác thực webhook request
    const isValid = await this.telegramWebhookService.validateWebhookRequest(secretToken, botId);
    if (!isValid) {
      throw new UnauthorizedException('Invalid webhook secret token');
    }

    // Xử lý update
    return await this.telegramWebhookService.processUpdate(update, botId);
  }
}
```

### TelegramAgentService

Service kết nối bot Telegram với agent trong hệ thống.

```typescript
// Ví dụ sử dụng
import { TelegramAgentService } from '@shared/services/telegram';

@Injectable()
export class YourService {
  constructor(private readonly telegramAgentService: TelegramAgentService) {}

  async processMessageWithAgent(update: TelegramUpdate, botId: number, agentId: string, botToken: string) {
    return await this.telegramAgentService.processMessageWithAgent(update, botId, agentId, botToken);
  }

  async sendAgentMessageToTelegram(botToken: string, chatId: number, message: string) {
    return await this.telegramAgentService.sendAgentMessageToTelegram(botToken, chatId, message);
  }
}
```

## Tạo bot Telegram

1. Mở Telegram và tìm kiếm [@BotFather](https://t.me/BotFather)
2. Gửi lệnh `/newbot` và làm theo hướng dẫn để tạo bot mới
3. Sau khi tạo bot thành công, BotFather sẽ cung cấp cho bạn một token API
4. Sử dụng token API này để tạo bot trong hệ thống của bạn

## Thiết lập webhook

Để nhận các update từ Telegram, bạn cần thiết lập webhook cho bot:

```typescript
// Ví dụ thiết lập webhook
const webhookUrl = await telegramBotService.setupWebhook(botToken, botId);
console.log(`Webhook đã được thiết lập: ${webhookUrl}`);
```

## Xử lý webhook

Khi Telegram gửi update đến webhook của bạn, bạn cần xử lý chúng:

```typescript
// Ví dụ xử lý webhook
const result = await telegramWebhookService.processUpdate(update, botId);
console.log(`Kết quả xử lý: ${result.message}`);
```

## Kết nối với agent

Để kết nối bot Telegram với agent trong hệ thống:

```typescript
// Ví dụ kết nối với agent
const result = await telegramAgentService.processMessageWithAgent(update, botId, agentId, botToken);
console.log(`Kết quả xử lý với agent: ${result.message}`);
```

## Lưu ý bảo mật

- Luôn sử dụng HTTPS cho webhook
- Thiết lập secret token cho webhook để xác thực request
- Không chia sẻ token bot với bất kỳ ai
- Kiểm tra kỹ các update từ Telegram trước khi xử lý
