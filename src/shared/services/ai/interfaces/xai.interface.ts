/**
 * Interface cho X.AI Model
 */
export interface XAIModel {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  // <PERSON><PERSON><PERSON> trường bổ sung có thể có trong response chi tiết
  description?: string;
  context_window?: number;
  max_tokens?: number;
  capabilities?: string[];
}

/**
 * Interface cho X.AI Models Response
 */
export interface XAIModelsResponse {
  object: string;
  data: XAIModel[];
}
