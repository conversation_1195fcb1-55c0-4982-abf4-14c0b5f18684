import { DeepSeekModel } from '@/shared/services/ai/interfaces/deepseek.interface';
import { GoogleAIModel } from '@/shared/services/ai/interfaces/google-ai.interface';
import { MetaAIModel } from '@/shared/services/ai/interfaces/meta-ai.interface';
import { XAIModel } from '@/shared/services/ai/interfaces/xai.interface';
import { ModelInfo } from '@/shared/services/ai/interfaces/anthropic.interface';
import { Model } from 'openai/resources/models';

export type ModelResponse = Model | ModelInfo | DeepSeekModel | XAIModel | MetaAIModel | GoogleAIModel;

export type ListModelResponse = ModelResponse[];
