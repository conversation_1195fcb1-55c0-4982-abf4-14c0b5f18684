import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { S3Service } from '../s3.service';
import {
  MetaAIModel,
  MetaAIModelsResponse,
  MetaAIFineTuningParams,
  MetaAIFineTuningResponse
} from './interfaces/meta-ai.interface';

/**
 * Service tương tác với Meta AI API (Llama)
 */
@Injectable()
export class MetaAIService {
  private readonly logger = new Logger(MetaAIService.name);
  private readonly apiBaseUrl = 'https://api.meta.ai/v1';

  constructor(
    private readonly httpService: HttpService,
    private readonly s3Service: S3Service,
  ) { }

  /**
   * <PERSON><PERSON><PERSON> danh sách model từ Meta AI API
   * @param options T<PERSON><PERSON> chọn bổ sung (apiKey, useEnvKey)
   * @returns Danh sách model từ Meta AI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  /**
   * Lấy danh sách model từ Meta AI API
   * @param apiKey API key của Meta AI
   * @returns Danh sách model từ Meta AI
   * @throws AppException nếu có lỗi khi lấy danh sách model
   */
  async getModels(apiKey: string): Promise<MetaAIModel[]> {
    try {
      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy danh sách model
      const response = await firstValueFrom(
        this.httpService.get<MetaAIModelsResponse>(
          `${this.apiBaseUrl}/models`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy danh sách model từ response
      const models = response.data.data || [];

      this.logger.log(`Retrieved ${models.length} models from Meta AI`);
      return models;
    } catch (error: any) {
      this.handleApiError(error, 'lấy danh sách model');
    }
  }

  /**
   * Lấy thông tin chi tiết của một model theo ID
   * @param modelId ID của model cần lấy thông tin
   * @param apiKey API key của Meta AI
   * @returns Thông tin chi tiết của model
   * @throws AppException nếu có lỗi khi lấy thông tin model
   */
  async retrieveModel(modelId: string, apiKey: string): Promise<MetaAIModel> {
    try {
      // Kiểm tra modelId
      if (!modelId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Model ID không được để trống',
        );
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      // Gọi REST API để lấy thông tin chi tiết của model
      const response = await firstValueFrom(
        this.httpService.get<MetaAIModel>(
          `${this.apiBaseUrl}/models/${modelId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Lấy thông tin model từ response
      const model = response.data;

      if (!model || !model.id) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.logger.log(`Retrieved details for model: ${model.id}`);
      return model;
    } catch (error: any) {
      // Xử lý trường hợp không tìm thấy model (404)
      if (error.response && error.response.status === 404) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          `Không tìm thấy model với ID: ${modelId}`,
        );
      }

      this.handleApiError(error, `lấy thông tin model ${modelId}`);
    }
  }

  /**
   * Upload file dữ liệu huấn luyện lên Meta AI
   * @param fileKey S3 key của file dữ liệu huấn luyện (định dạng JSONL)
   * @param apiKey API key của Meta AI
   * @returns ID của file đã upload
   * @throws AppException nếu có lỗi khi upload file
   */
  async uploadTrainingFile(fileKey: string, apiKey: string): Promise<string> {
    try {
      // Tạo thư mục tạm để lưu file
      const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'meta-training-'));
      const tempFilePath = path.join(tempDir, path.basename(fileKey));

      // Tải file trực tiếp từ S3 dưới dạng byte array
      const fileBytes = await this.s3Service.downloadFileAsBytes(fileKey);

      // Ghi file vào thư mục tạm
      fs.writeFileSync(tempFilePath, Buffer.from(fileBytes));

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Tạo form data để upload file
      // Sử dụng FormData từ Node.js
      const FormData = require('form-data');
      const formData = new FormData();
      formData.append('file', fs.createReadStream(tempFilePath));
      formData.append('purpose', 'fine-tune');

      // Upload file lên Meta AI
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiBaseUrl}/files`,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
              Authorization: `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      // Xóa file tạm
      try {
        fs.unlinkSync(tempFilePath);
        fs.rmdirSync(tempDir);
      } catch (cleanupError) {
        this.logger.warn(
          `Failed to clean up temp files: ${cleanupError.message}`,
        );
      }

      this.logger.log(`Training file uploaded successfully: ${response.data.id}`);
      return response.data.id;
    } catch (error: any) {
      this.logger.error(
        `Meta AI API error uploading training file: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.response && error.response.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Meta AI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Meta AI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ENOTFOUND' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Meta AI API',
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi upload file dữ liệu huấn luyện: ' + error.message,
      );
    }
  }

  /**
   * Tạo fine-tuning job trên Meta AI
   * @param params Tham số cho fine-tuning job
   * @param apiKey API key của Meta AI
   * @returns Thông tin về fine-tuning job đã tạo
   * @throws AppException nếu có lỗi khi tạo fine-tuning job
   */
  async createFineTuningJob(
    params: MetaAIFineTuningParams,
    apiKey: string,
  ): Promise<MetaAIFineTuningResponse> {
    try {
      // Kiểm tra các tham số bắt buộc
      if (!params.model) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'model là bắt buộc',
        );
      }

      if (!params.trainingFileId) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'trainingFileId là bắt buộc',
        );
      }

      // Thiết lập timeout cho request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 seconds timeout

      // Chuẩn bị tham số cho fine-tuning job
      const requestBody: any = {
        model: params.model,
        training_file: params.trainingFileId,
      };

      // Thêm các tham số tùy chọn nếu có
      if (params.suffix) {
        requestBody.suffix = params.suffix;
      }

      if (params.validationFileId) {
        requestBody.validation_file = params.validationFileId;
      }

      if (params.hyperparameters) {
        requestBody.hyperparameters = {};

        if (params.hyperparameters.nEpochs !== undefined) {
          requestBody.hyperparameters.n_epochs = params.hyperparameters.nEpochs;
        }

        if (params.hyperparameters.batchSize !== undefined) {
          requestBody.hyperparameters.batch_size = params.hyperparameters.batchSize;
        }

        if (params.hyperparameters.learningRateMultiplier !== undefined) {
          requestBody.hyperparameters.learning_rate_multiplier = params.hyperparameters.learningRateMultiplier;
        }
      }

      // Gọi API để tạo fine-tuning job
      const response = await firstValueFrom(
        this.httpService.post<MetaAIFineTuningResponse>(
          `${this.apiBaseUrl}/fine-tuning/jobs`,
          requestBody,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${apiKey}`,
            },
            signal: controller.signal,
          },
        ),
      );

      clearTimeout(timeoutId);

      this.logger.log(`Fine-tuning job created successfully: ${response.data.id}`);
      return response.data;
    } catch (error: any) {
      this.logger.error(
        `Meta AI API error creating fine-tuning job: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi quota exceeded
      if (error.response && error.response.status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Meta AI API',
        );
      }

      // Xử lý lỗi timeout
      if (
        error.name === 'TimeoutError' ||
        error.code === 'ETIMEDOUT' ||
        error.message.includes('timeout')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Kết nối đến Meta AI API bị gián đoạn hoặc quá thời gian chờ',
        );
      }

      // Xử lý lỗi kết nối
      if (
        error.name === 'NetworkError' ||
        error.code === 'ENOTFOUND' ||
        error.message.includes('network')
      ) {
        throw new AppException(
          ErrorCode.OPENAI_TIMEOUT,
          'Lỗi kết nối đến Meta AI API',
        );
      }

      // Xử lý lỗi validation
      if (error.response && error.response.status === 400) {
        const errorMessage = error.response.data?.error?.message || 'Dữ liệu không hợp lệ';
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi validation: ' + errorMessage,
        );
      }

      // Xử lý lỗi xác thực
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi xác thực: ' + (error.response.data?.error?.message || 'Không có quyền truy cập'),
        );
      }

      // Các lỗi khác
      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        'Lỗi khi tạo fine-tuning job: ' + error.message,
      );
    }
  }

  /**
   * Xử lý lỗi từ API
   * @param error Lỗi từ API
   * @param action Hành động đang thực hiện
   * @throws AppException với mã lỗi và thông báo phù hợp
   */
  private handleApiError(error: any, action: string): never {
    this.logger.error(
      `Error ${action} from Meta AI: ${error.message}`,
      error.stack,
    );

    // Xử lý các lỗi khi kết nối Meta AI API
    if (error.response) {
      // Lỗi từ API response
      const status = error.response.status;
      const data = error.response.data;

      if (status === 429) {
        throw new AppException(
          ErrorCode.OPENAI_QUOTA_EXCEEDED,
          'Đã vượt quá giới hạn sử dụng Meta AI API',
        );
      }

      if (status === 401 || status === 403) {
        throw new AppException(
          ErrorCode.OPENAI_API_ERROR,
          'Lỗi xác thực API key: ' +
          (data?.error?.message || 'Không có quyền truy cập'),
        );
      }

      throw new AppException(
        ErrorCode.OPENAI_API_ERROR,
        `Lỗi API (${status}): ${data?.error?.message || 'Không xác định'}`,
      );
    }

    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Kết nối đến Meta AI API bị gián đoạn hoặc quá thời gian chờ',
      );
    }

    if (error.code === 'ENOTFOUND' || error.message.includes('network')) {
      throw new AppException(
        ErrorCode.OPENAI_TIMEOUT,
        'Lỗi kết nối đến Meta AI API',
      );
    }

    // Các lỗi khác
    throw new AppException(
      ErrorCode.OPENAI_API_ERROR,
      `Lỗi khi ${action}: ${error.message}`,
    );
  }
}
