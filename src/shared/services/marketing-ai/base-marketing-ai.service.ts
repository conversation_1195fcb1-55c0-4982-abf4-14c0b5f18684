import { Logger } from '@nestjs/common';
import { BaseMarketingAiService, MarketingAiResponse } from './interfaces';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from 'axios';

/**
 * Base class for all marketing AI services
 */
export abstract class BaseMarketingAiServiceImpl implements BaseMarketingAiService {
  protected readonly logger: Logger;
  protected readonly axiosInstance: AxiosInstance;

  /**
   * Name of the service
   */
  abstract readonly serviceName: string;

  /**
   * Base URL for the service API
   */
  protected abstract readonly baseUrl: string;

  /**
   * API key for the service
   */
  protected abstract readonly apiKey: string | undefined;

  /**
   * Constructor
   * @param loggerName Name for the logger
   */
  constructor(loggerName: string) {
    this.logger = new Logger(loggerName);
    this.axiosInstance = axios.create({
      timeout: 30000, // 30 seconds default timeout
    });
  }

  /**
   * Test the connection to the service
   * @returns A promise that resolves to a boolean indicating if the connection was successful
   */
  abstract testConnection(): Promise<boolean>;

  /**
   * Create a request configuration with authentication
   * @param config Additional request configuration
   * @returns Request configuration with authentication
   */
  protected abstract createRequestConfig(config?: AxiosRequestConfig): AxiosRequestConfig;

  /**
   * Handle API exceptions
   * @param error Error from the API
   * @param operation Name of the operation that failed
   * @returns A standardized error response
   */
  protected handleApiError<T>(error: any, operation: string): MarketingAiResponse<T> {
    let errorMessage = `Error during ${operation}: ${error.message}`;
    let statusCode: number | undefined;

    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      statusCode = axiosError.response?.status;

      if (axiosError.response?.data) {
        errorMessage = `API Error (${statusCode}): ${JSON.stringify(axiosError.response.data)}`;
      }
    }

    this.logger.error(errorMessage, error.stack);

    return {
      success: false,
      error: errorMessage,
      rawResponse: axios.isAxiosError(error) ? (error as AxiosError).response?.data : undefined,
    };
  }

  /**
   * Create a successful response
   * @param data Response data
   * @param rawResponse Raw response from the API
   * @returns A standardized successful response
   */
  protected createSuccessResponse<T>(data: T, rawResponse?: any): MarketingAiResponse<T> {
    return {
      success: true,
      data,
      rawResponse,
    };
  }
}
