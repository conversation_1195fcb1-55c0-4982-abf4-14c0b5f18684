# Services SMS

Ce module fournit une intégration avec différents fournisseurs de services SMS, permettant d'envoyer des SMS, des SMS brandname et des OTP via une interface unifiée.

## Fournisseurs supportés

- **SpeedSMS**: Fournisseur SMS vietnamien
- **Twilio**: Fournisseur SMS international
- **Vonage** (anciennement Nexmo): Fournisseur SMS international
- **FPT SMS**: Fournisseur SMS vietnamien

## Installation

### Dépendances

Selon les fournisseurs que vous souhaitez utiliser, vous devrez installer les packages suivants:

```bash
# Pour Twilio
npm install twilio

# Pour Vonage
npm install @vonage/server-sdk
```

### Configuration

Ajoutez les variables d'environnement suivantes à votre fichier `.env`:

```env
# Fournisseur par défaut
SMS_DEFAULT_PROVIDER=SPEED_SMS

# SpeedSMS
SPEED_SMS_API_TOKEN=votre_token_api
SPEED_SMS_API_URL=https://api.speedsms.vn/index.php
SPEED_SMS_TYPE=2
SPEED_SMS_SENDER=

# Twilio
TWILIO_ACCOUNT_SID=votre_account_sid
TWILIO_AUTH_TOKEN=votre_auth_token
TWILIO_PHONE_NUMBER=votre_numero_de_telephone
TWILIO_MESSAGING_SERVICE_SID=votre_messaging_service_sid

# Vonage
VONAGE_API_KEY=votre_api_key
VONAGE_API_SECRET=votre_api_secret
VONAGE_FROM=Vonage

# FPT SMS
FPT_SMS_CLIENT_ID=votre_client_id
FPT_SMS_CLIENT_SECRET=votre_client_secret
FPT_SMS_SCOPE=send_brandname_otp send_brandname
FPT_SMS_API_URL=http://api.fpt.net/api
FPT_SMS_BRANDNAME=votre_brandname
```

## Utilisation

### Importer le module

```typescript
import { SmsModule } from '@shared/services/sms';
```

Le module est déjà importé dans le `ServicesModule` global, donc vous n'avez pas besoin de l'importer explicitement dans vos modules.

### Envoyer un SMS

```typescript
import { Injectable } from '@nestjs/common';
import { SmsService, SmsProviderType } from '@shared/services/sms';

@Injectable()
export class VotreService {
  constructor(private readonly smsService: SmsService) {}

  async envoyerSms() {
    const resultat = await this.smsService.sendSms(
      '**********',
      'Votre message ici',
      {
        // Optionnel: spécifier le fournisseur à utiliser
        providerType: SmsProviderType.SPEED_SMS,
        
        // Options spécifiques au fournisseur
        // Pour SpeedSMS
        smsType: 2, // 2: CSKH, 3: Brandname, 4: Notify, 5: App Android, 6: Numéro fixe
        sender: 'BRAND', // Obligatoire si smsType = 3 ou 5
        
        // Pour Twilio
        from: '+**********',
        messagingServiceSid: 'MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
        
        // Pour Vonage
        from: 'BRAND',
        type: 'text',
        
        // Pour FPT SMS
        brandName: 'BRAND',
      }
    );

    if (resultat.success) {
      console.log(`SMS envoyé avec succès, ID: ${resultat.messageId}`);
    } else {
      console.error(`Erreur lors de l'envoi du SMS: ${resultat.errorMessage}`);
    }
  }
}
```

### Envoyer un SMS avec un brandname

```typescript
const resultat = await this.smsService.sendBrandnameSms(
  '**********',
  'Votre message ici',
  'VOTRE_BRANDNAME',
  {
    // Options supplémentaires
    providerType: SmsProviderType.FPT_SMS,
    
    // Pour FPT SMS
    campaignName: 'Campagne_Test',
    scheduleTime: '2023-12-31 12:00',
    quota: 1,
  }
);
```

### Envoyer un SMS OTP

```typescript
const resultat = await this.smsService.sendOtp(
  '**********',
  '123456',
  {
    // Options supplémentaires
    providerType: SmsProviderType.SPEED_SMS,
    
    // Modèle de message OTP
    template: 'Votre code de vérification est: {code}',
  }
);
```

### Envoyer des SMS en masse

```typescript
const resultat = await this.smsService.sendBulkSms(
  ['**********', '**********'],
  'Votre message ici',
  {
    // Options supplémentaires
    providerType: SmsProviderType.TWILIO,
  }
);

console.log(`${resultat.successCount} SMS envoyés avec succès, ${resultat.failureCount} échecs`);
```

### Vérifier le statut d'un message

```typescript
const statut = await this.smsService.checkMessageStatus(
  'message_id',
  SmsProviderType.VONAGE
);

console.log(`Statut du message: ${statut.status}`);
```

### Tester la connexion avec un fournisseur

```typescript
const test = await this.smsService.testConnection(
  SmsProviderType.FPT_SMS,
  {
    // Configuration spécifique pour le test
    clientId: 'nouveau_client_id',
    clientSecret: 'nouveau_client_secret',
  }
);

if (test.success) {
  console.log(`Connexion réussie: ${test.message}`);
} else {
  console.error(`Échec de la connexion: ${test.message}`);
}
```

## Extension

Pour ajouter un nouveau fournisseur SMS:

1. Créez une nouvelle classe qui étend `BaseSmsProvider` et implémente les méthodes requises
2. Ajoutez le nouveau fournisseur à l'énumération `SmsProviderType`
3. Mettez à jour la méthode `createProvider` dans `SmsProviderFactory`
4. Ajoutez le nouveau fournisseur aux providers dans `SmsModule`

## Dépannage

### Problèmes courants

- **Erreur "Le client X n'est pas initialisé"**: Assurez-vous d'avoir installé les packages requis et configuré les variables d'environnement correctement.
- **Erreur d'authentification**: Vérifiez vos identifiants API dans le fichier `.env`.
- **Format de numéro de téléphone incorrect**: Les numéros sont automatiquement formatés au format international, mais assurez-vous qu'ils sont valides.

### Journalisation

Tous les services SMS utilisent le système de journalisation de NestJS. Pour voir les journaux détaillés, configurez le niveau de journalisation sur `debug` dans votre application.
