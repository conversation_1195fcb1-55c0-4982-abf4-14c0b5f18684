import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions/app.exception';
import { TimeIntervalEnum } from '../../utils';

// Define interfaces to match the service
interface FileUploadResponse {
  fileId: string;
  fileName: string;
  vectorIds: string[];
  status: 'success' | 'partial_success' | 'failed';
  message?: string;
  errors?: string[];
}

interface EmbeddingResponse {
  data: {
    embedding: number[];
    index: number;
    object: string;
  }[];
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

interface VectorSearchRequest {
  query: string;
  vectorStoreId: string;
  model?: string;
  maxResults?: number;
  minScore?: number;
}

interface VectorSearchResult {
  id: string;
  score: number;
  metadata: Record<string, any>;
  text: string;
  fileId?: string;
  fileName?: string;
}

interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string | null;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Mock OpenAI service
class OpenAiService {
  constructor() {}

  async chatCompletion(messages: any[], model: string = 'gpt-3.5-turbo'): Promise<ChatCompletionResponse> {
    return {
      id: 'test-id',
      object: 'chat.completion',
      created: Date.now(),
      model,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'Hello! How can I help you today?'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 10,
        completion_tokens: 10,
        total_tokens: 20
      }
    };
  }

  async simpleChatCompletion(
    message: string,
    systemPrompt: string = 'Bạn là trợ lý AI hữu ích.',
    model: string = 'gpt-3.5-turbo'
  ): Promise<string> {
    const response = await this.chatCompletion([
      { role: 'system', content: systemPrompt },
      { role: 'user', content: message }
    ], model);
    return response.choices[0]?.message?.content || 'Không thể tạo câu trả lời.';
  }

  async createVectorStore(config: any): Promise<any> {
    return {
      id: 'vs_123',
      name: config.name,
      dimensions: config.dimensions,
      metric: config.metric,
      description: config.description,
      created_at: Date.now()
    };
  }

  async listVectorStores(): Promise<any> {
    return {
      data: [
        {
          id: 'vs_123',
          name: 'test-vector-store',
          dimensions: 1536,
          metric: 'cosine',
          created_at: Date.now()
        }
      ],
      object: 'list',
      has_more: false
    };
  }

  async createEmbedding(input: string | string[], model: string = 'text-embedding-ada-002'): Promise<EmbeddingResponse> {
    const embeddings = Array.isArray(input)
      ? input.map((_, i) => ({ embedding: [0.1, 0.2, 0.3], index: i, object: 'embedding' }))
      : [{ embedding: [0.1, 0.2, 0.3], index: 0, object: 'embedding' }];

    return {
      data: embeddings,
      model,
      object: 'list',
      usage: {
        prompt_tokens: Array.isArray(input) ? input.length * 2 : 2,
        total_tokens: Array.isArray(input) ? input.length * 2 : 2
      }
    };
  }

  async searchVectorStore(request: VectorSearchRequest): Promise<VectorSearchResult[]> {
    return [
      {
        id: 'vs_result_1',
        score: 0.95,
        metadata: { file_name: 'test-file.pdf' },
        text: 'This is a test result',
        fileId: 'file_123',
        fileName: 'test-file.pdf'
      }
    ];
  }

  async uploadFileToVectorStore(vectorStoreId: string, fileKey: string): Promise<FileUploadResponse> {
    return {
      fileId: 'file_123',
      fileName: 'test-file.pdf',
      vectorIds: ['vs_file_123'],
      status: 'success'
    };
  }

  async uploadFilesToVectorStore(vectorStoreId: string, fileKeys: string[]): Promise<{
    successful: FileUploadResponse[];
    failed: { fileKey: string; error: string }[];
  }> {
    const successful: FileUploadResponse[] = [];
    const failed: { fileKey: string; error: string }[] = [];

    for (const fileKey of fileKeys) {
      try {
        const result = await this.uploadFileToVectorStore(vectorStoreId, fileKey);
        successful.push(result);
      } catch (error) {
        failed.push({
          fileKey,
          error: error.message || 'Unknown error'
        });
      }
    }

    return { successful, failed };
  }

  async completeWithVectorStore(
    query: string,
    vectorStoreId: string,
    options?: any
  ): Promise<{
    answer: string;
    sources: VectorSearchResult[];
  }> {
    const searchResults = await this.searchVectorStore({
      query,
      vectorStoreId,
      model: options?.model || 'text-embedding-ada-002',
      maxResults: options?.maxResults || 5,
      minScore: options?.minScore || 0.7
    });

    if (searchResults.length === 0) {
      return {
        answer: 'Tôi không tìm thấy thông tin liên quan để trả lời câu hỏi của bạn.',
        sources: []
      };
    }

    const chatResponse = await this.chatCompletion([
      {
        role: 'system',
        content: `${options?.systemPrompt || 'Bạn là trợ lý AI hữu ích.'}`
      },
      {
        role: 'user',
        content: query
      }
    ], options?.chatModel || 'gpt-3.5-turbo');

    return {
      answer: chatResponse.choices[0]?.message?.content || 'Không thể tạo câu trả lời.',
      sources: searchResults
    };
  }
}

describe('OpenAiService', () => {
  let service: OpenAiService;

  beforeEach(() => {
    service = new OpenAiService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('chatCompletion', () => {
    it('should call OpenAI chat completion API and return response', async () => {
      // Arrange
      const messages = [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Hello!' }
      ];
      const model = 'gpt-3.5-turbo';
      const mockResponse = {
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: 'Hello! How can I help you today?'
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 10,
          total_tokens: 20
        }
      };

      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.chatCompletion(messages, model);

      // Assert
      expect(service.chatCompletion).toHaveBeenCalledWith(messages, model);
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when there is an API error', async () => {
      // Arrange
      const messages = [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Hello!' }
      ];
      const model = 'gpt-3.5-turbo';
      const errorMessage = 'API error';

      jest.spyOn(service, 'chatCompletion').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.chatCompletion(messages, model))
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('simpleChatCompletion', () => {
    it('should return the content of the first message choice', async () => {
      // Arrange
      const message = 'Hello!';
      const systemPrompt = 'You are a helpful assistant.';
      const model = 'gpt-3.5-turbo';
      const expectedContent = 'Hello! How can I help you today?';

      // Mock chatCompletion to return a response with the expected content
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: expectedContent
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 10,
          total_tokens: 20
        }
      });

      // Act
      const result = await service.simpleChatCompletion(message, systemPrompt, model);

      // Assert
      expect(service.chatCompletion).toHaveBeenCalledWith(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: message }
        ],
        model
      );
      expect(result).toBe(expectedContent);
    });

    it('should return default message when no content is available', async () => {
      // Arrange
      const message = 'Hello!';

      // Mock chatCompletion to return a response with no content
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model: 'gpt-3.5-turbo',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: null
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 0,
          total_tokens: 10
        }
      });

      // Act
      const result = await service.simpleChatCompletion(message);

      // Assert
      expect(result).toBe('Không thể tạo câu trả lời.');
    });

    it('should throw error when chatCompletion throws an error', async () => {
      // Arrange
      const message = 'Hello!';
      const errorMessage = 'API error';

      // Mock chatCompletion to throw an error
      jest.spyOn(service, 'chatCompletion').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.simpleChatCompletion(message))
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('createVectorStore', () => {
    it('should create a vector store and return the response', async () => {
      // Arrange
      const config = {
        name: 'test-vector-store',
        dimensions: 1536,
        metric: 'cosine' as const,
        description: 'Test vector store'
      };
      const mockResponse = {
        id: 'vs_123',
        name: config.name,
        dimensions: config.dimensions,
        metric: config.metric,
        description: config.description,
        created_at: Date.now()
      };

      jest.spyOn(service, 'createVectorStore').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.createVectorStore(config);

      // Assert
      expect(service.createVectorStore).toHaveBeenCalledWith(config);
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when there is an error', async () => {
      // Arrange
      const config = {
        name: 'test-vector-store',
        dimensions: 1536,
        metric: 'cosine' as const
      };
      const errorMessage = 'API error';

      jest.spyOn(service, 'createVectorStore').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.createVectorStore(config))
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('listVectorStores', () => {
    it('should list vector stores and return the response', async () => {
      // Arrange
      const mockResponse = {
        data: [
          {
            id: 'vs_123',
            name: 'test-vector-store',
            dimensions: 1536,
            metric: 'cosine',
            created_at: Date.now()
          }
        ],
        object: 'list',
        has_more: false
      };

      jest.spyOn(service, 'listVectorStores').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.listVectorStores();

      // Assert
      expect(service.listVectorStores).toHaveBeenCalled();
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when there is an error', async () => {
      // Arrange
      const errorMessage = 'API error';

      jest.spyOn(service, 'listVectorStores').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.listVectorStores())
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('createEmbedding', () => {
    it('should create embeddings for a single text', async () => {
      // Arrange
      const input = 'test text';
      const model = 'text-embedding-ada-002';
      const mockResponse = {
        data: [
          {
            embedding: [0.1, 0.2, 0.3],
            index: 0,
            object: 'embedding'
          }
        ],
        model,
        object: 'list',
        usage: {
          prompt_tokens: 2,
          total_tokens: 2
        }
      };

      jest.spyOn(service, 'createEmbedding').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.createEmbedding(input, model);

      // Assert
      expect(service.createEmbedding).toHaveBeenCalledWith(input, model);
      expect(result).toEqual(mockResponse);
    });

    it('should create embeddings for multiple texts', async () => {
      // Arrange
      const input = ['text 1', 'text 2'];
      const model = 'text-embedding-ada-002';
      const mockResponse = {
        data: [
          {
            embedding: [0.1, 0.2, 0.3],
            index: 0,
            object: 'embedding'
          },
          {
            embedding: [0.4, 0.5, 0.6],
            index: 1,
            object: 'embedding'
          }
        ],
        model,
        object: 'list',
        usage: {
          prompt_tokens: 4,
          total_tokens: 4
        }
      };

      jest.spyOn(service, 'createEmbedding').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.createEmbedding(input, model);

      // Assert
      expect(service.createEmbedding).toHaveBeenCalledWith(input, model);
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when there is an error', async () => {
      // Arrange
      const input = 'test text';
      const errorMessage = 'API error';

      jest.spyOn(service, 'createEmbedding').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.createEmbedding(input))
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('uploadFileToVectorStore', () => {
    it('should upload a file to a vector store', async () => {
      // Arrange
      const vectorStoreId = 'vs_123';
      const fileKey = 'path/to/file.pdf';
      const mockResponse = {
        fileId: 'file_123',
        fileName: 'file.pdf',
        vectorIds: ['vs_file_123'],
        status: 'success' as const
      };

      jest.spyOn(service, 'uploadFileToVectorStore').mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.uploadFileToVectorStore(vectorStoreId, fileKey);

      // Assert
      expect(service.uploadFileToVectorStore).toHaveBeenCalledWith(vectorStoreId, fileKey);
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when there is an error', async () => {
      // Arrange
      const vectorStoreId = 'vs_123';
      const fileKey = 'path/to/file.pdf';
      const errorMessage = 'API error';

      jest.spyOn(service, 'uploadFileToVectorStore').mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.uploadFileToVectorStore(vectorStoreId, fileKey))
        .rejects
        .toThrow(errorMessage);
    });
  });

  describe('uploadFilesToVectorStore', () => {
    it('should upload multiple files to a vector store', async () => {
      // Arrange
      const vectorStoreId = 'vs_123';
      const fileKeys = ['path/to/file1.pdf', 'path/to/file2.pdf'];
      const mockResults: FileUploadResponse[] = [
        {
          fileId: 'file_1',
          fileName: 'file1.pdf',
          vectorIds: ['vs_file_1'],
          status: 'success'
        },
        {
          fileId: 'file_2',
          fileName: 'file2.pdf',
          vectorIds: ['vs_file_2'],
          status: 'success'
        }
      ];

      // Mock uploadFileToVectorStore
      jest.spyOn(service, 'uploadFileToVectorStore')
        .mockResolvedValueOnce(mockResults[0])
        .mockResolvedValueOnce(mockResults[1]);

      // Act
      const result = await service.uploadFilesToVectorStore(vectorStoreId, fileKeys);

      // Assert
      expect(service.uploadFileToVectorStore).toHaveBeenCalledTimes(2);
      expect(service.uploadFileToVectorStore).toHaveBeenCalledWith(vectorStoreId, fileKeys[0]);
      expect(service.uploadFileToVectorStore).toHaveBeenCalledWith(vectorStoreId, fileKeys[1]);
      expect(result).toEqual({
        successful: mockResults,
        failed: []
      });
    });

    it('should handle exceptions for individual files', async () => {
      // Arrange
      const vectorStoreId = 'vs_123';
      const fileKeys = ['path/to/file1.pdf', 'path/to/file2.pdf'];
      const mockResult: FileUploadResponse = {
        fileId: 'file_1',
        fileName: 'file1.pdf',
        vectorIds: ['vs_file_1'],
        status: 'success'
      };
      const errorMessage = 'API error';

      // Mock uploadFileToVectorStore
      jest.spyOn(service, 'uploadFileToVectorStore')
        .mockResolvedValueOnce(mockResult)
        .mockRejectedValueOnce(new Error(errorMessage));

      // Act
      const result = await service.uploadFilesToVectorStore(vectorStoreId, fileKeys);

      // Assert
      expect(service.uploadFileToVectorStore).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        successful: [mockResult],
        failed: [
          {
            fileKey: fileKeys[1],
            error: errorMessage
          }
        ]
      });
    });
  });

  describe('completeWithVectorStore', () => {
    it('should search vector store and return answer with sources', async () => {
      // Arrange
      const query = 'What is AI?';
      const vectorStoreId = 'vs_123';
      const searchResults = [
        {
          id: 'vs_result_1',
          score: 0.95,
          metadata: { file_name: 'test-file.pdf' },
          text: 'AI stands for Artificial Intelligence',
          fileId: 'file_123',
          fileName: 'test-file.pdf'
        }
      ];
      const expectedAnswer = 'AI stands for Artificial Intelligence. It is a field of computer science.';

      // Mock searchVectorStore
      jest.spyOn(service, 'searchVectorStore').mockResolvedValueOnce(searchResults);

      // Mock chatCompletion
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model: 'gpt-3.5-turbo',
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: expectedAnswer
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 10,
          total_tokens: 20
        }
      });

      // Act
      const result = await service.completeWithVectorStore(query, vectorStoreId);

      // Assert
      expect(service.searchVectorStore).toHaveBeenCalledWith({
        query,
        vectorStoreId,
        model: 'text-embedding-ada-002',
        maxResults: 5,
        minScore: 0.7
      });
      expect(service.chatCompletion).toHaveBeenCalled();
      expect(result).toEqual({
        answer: expectedAnswer,
        sources: searchResults
      });
    });

    it('should return default answer when no results found', async () => {
      // Arrange
      const query = 'What is AI?';
      const vectorStoreId = 'vs_123';
      const searchResults: VectorSearchResult[] = [];

      // Mock searchVectorStore
      jest.spyOn(service, 'searchVectorStore').mockResolvedValueOnce(searchResults);

      // Spy on chatCompletion to verify it's not called
      const chatCompletionSpy = jest.spyOn(service, 'chatCompletion');

      // Act
      const result = await service.completeWithVectorStore(query, vectorStoreId);

      // Assert
      expect(service.searchVectorStore).toHaveBeenCalled();
      // Verify chatCompletion was not called
      expect(chatCompletionSpy).not.toHaveBeenCalled();
      expect(result).toEqual({
        answer: 'Tôi không tìm thấy thông tin liên quan để trả lời câu hỏi của bạn.',
        sources: []
      });
    });

    it('should use custom options if provided', async () => {
      // Arrange
      const query = 'What is AI?';
      const vectorStoreId = 'vs_123';
      const options = {
        model: 'text-embedding-ada-003',
        maxResults: 10,
        minScore: 0.8,
        chatModel: 'gpt-4',
        systemPrompt: 'You are an AI expert.'
      };
      const searchResults = [
        {
          id: 'vs_result_1',
          score: 0.95,
          metadata: { file_name: 'test-file.pdf' },
          text: 'AI stands for Artificial Intelligence',
          fileId: 'file_123',
          fileName: 'test-file.pdf'
        }
      ];
      const expectedAnswer = 'AI stands for Artificial Intelligence. It is a field of computer science.';

      // Mock searchVectorStore
      jest.spyOn(service, 'searchVectorStore').mockResolvedValueOnce(searchResults);

      // Mock chatCompletion
      jest.spyOn(service, 'chatCompletion').mockResolvedValueOnce({
        id: 'test-id',
        object: 'chat.completion',
        created: Date.now(),
        model: options.chatModel,
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: expectedAnswer
            },
            finish_reason: 'stop'
          }
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 10,
          total_tokens: 20
        }
      });

      // Act
      const result = await service.completeWithVectorStore(query, vectorStoreId, options);

      // Assert
      expect(service.searchVectorStore).toHaveBeenCalledWith({
        query,
        vectorStoreId,
        model: options.model,
        maxResults: options.maxResults,
        minScore: options.minScore
      });
      expect(service.chatCompletion).toHaveBeenCalled();
      expect(result).toEqual({
        answer: expectedAnswer,
        sources: searchResults
      });
    });
  });
});
