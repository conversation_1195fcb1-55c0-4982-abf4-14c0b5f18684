# Test Tracking API

## Endpoint
```
GET /v1/user/orders/{orderId}/tracking
```

## Test Cases

### 1. Success Case
**Request:**
```
GET /v1/user/orders/20/tracking
Authorization: Bearer {jwt_token}
```

**Expected Response (200):**
```json
{
  "success": true,
  "message": "<PERSON><PERSON>y thông tin tracking thành công",
  "data": {
    "orderId": "20",
    "trackingNumber": "LBK6X3",
    "carrier": "GHN",
    "status": {
      "shop_id": 196768,
      "client_id": 2509992,
      "from_name": "<PERSON><PERSON>a hàng ABC",
      "from_phone": "0789282471",
      "from_address": "Số 123, Đường ABC, Phường 1, Quận 1, TP.HCM",
      "to_name": "NGUYEN NGOC HAI ANH",
      "to_phone": "0793355880",
      "to_address": "<PERSON><PERSON> <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON> <PERSON><PERSON><PERSON>",
      "weight": 1200,
      "length": 20,
      "width": 15,
      "height": 10,
      "client_order_code": "ORDER_20_1749016810896",
      "order_code": "LBK6X3",
      "status": "ready_to_pick",
      "content": "Túi xách nữ da thật [60] [2 cái]",
      "note": "Giao hàng trong giờ hành chính",
      "pickup_time": "2025-06-04T06:00:15.42Z",
      "leadtime": "2025-06-05T16:59:59Z",
      "created_date": "2025-06-04T06:00:14.139Z",
      "updated_date": "2025-06-04T06:00:16.789Z",
      "items": [
        {
          "name": "Túi xách nữ da thật",
          "code": "60",
          "quantity": 2,
          "weight": 600,
          "status": "ready_to_pick",
          "item_order_code": "LBK6X3_1"
        }
      ]
    },
    "lastUpdated": 1749016943203
  }
}
```

### 2. Order Not Found (404)
**Request:**
```
GET /v1/user/orders/999/tracking
Authorization: Bearer {jwt_token}
```

**Expected Response (404):**
```json
{
  "code": 30021,
  "message": "Không tìm thấy đơn hàng hoặc bạn không có quyền truy cập",
  "detail": {}
}
```

### 3. Order Without Shipping Info (400)
**Request:**
```
GET /v1/user/orders/5/tracking
Authorization: Bearer {jwt_token}
```

**Expected Response (500):**
```json
{
  "code": 30025,
  "message": "Đơn hàng chưa có thông tin vận chuyển",
  "detail": {}
}
```

### 4. Access Denied (403)
**Request:**
```
GET /v1/user/orders/1/tracking
Authorization: Bearer {other_user_jwt_token}
```

**Expected Response (403):**
```json
{
  "code": 30027,
  "message": "Access denied to this order",
  "detail": {}
}
```

## Swagger Documentation Updates

### Response Schema
- ✅ Updated with detailed `TrackingApiResponseDto`
- ✅ Includes real GHN response structure
- ✅ Shows all fields from actual API response

### Error Responses
- ✅ Uses `@ApiErrorResponse` with specific error codes
- ✅ Shows proper HTTP status codes
- ✅ Includes meaningful error messages

### Before vs After

**Before:**
```json
{
  "orderId": 1,
  "trackingNumber": "GHN123456789", 
  "carrier": "GHN",
  "status": {},  // Empty object
  "lastUpdated": 1641708800000
}
```

**After:**
```json
{
  "orderId": "20",
  "trackingNumber": "LBK6X3",
  "carrier": "GHN", 
  "status": {
    // Full detailed GHN response with all fields
    "shop_id": 196768,
    "from_name": "Cửa hàng ABC",
    "to_name": "NGUYEN NGOC HAI ANH",
    "status": "ready_to_pick",
    "items": [...],
    // ... many more fields
  },
  "lastUpdated": 1749016943203
}
```
